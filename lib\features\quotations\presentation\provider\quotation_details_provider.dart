import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/quotations/data/models/quotation_details_model.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/domain/usecases/quotation_details.dart';

final quotationDetailsProvider =
    StateNotifierProvider<QuotationDetailsProvider, ViewState>(
        (ref) => QuotationDetailsProvider(
              ref.watch(quotationDetailsUseCaseProvider),
            ));

class QuotationDetailsProvider extends BaseProvider<QuotationsModel> {
  final QuotationDetails _quotationDetails;

  QuotationDetailsProvider(this._quotationDetails);

  Future<void> fetchQuotationDetails(String quotationID) async {
    state = LoadingViewState();
    final response = await _quotationDetails
        .call(QuotationDetailsParams(quotationID: quotationID));
    response.fold(
      (failure) => setErrorState(failure.message),
      setLoadedState,
    );
  }

  void _updateTotalVat(QuotationsModel model) {
    final acceptedItems = model.items!.where((e) => e.isAccepted).toList();
    final totalPrices = acceptedItems.fold([0.0, 0.0], (acc, item) {
      if (item.total != null) {
        acc[0] += item.total!;
        if (item.isVATExcluded) acc[1] += item.total!;
      }
      return acc;
    });
    final diffTotal = totalPrices[0] - totalPrices[1];
    final vat = (diffTotal +
            (model.serviceFees ?? 0) +
            (model.deliveryFees ?? 0) +
            (model.additionalFees ?? 0)) *
        (model.vatPer ?? 0) /
        100;
    final total = totalPrices[0] +
        (model.serviceFees ?? 0) +
        (model.deliveryFees ?? 0) +
        (model.additionalFees ?? 0) +
        vat;
    setLoadedState(
      model.copyWith(
        price: totalPrices[0],
        priceWithNoVAT: totalPrices[1],
        vat: vat,
        total: total,
      ),
    );
  }

  void acceptProduct({required String productId, required String unitId}) {
    if (state is LoadedViewState<QuotationsModel>) {
      final oldState = state as LoadedViewState<QuotationsModel>;
      final productIndex = oldState.data.items?.indexWhere((element) =>
          element.productID == productId && element.unitID == unitId);
      if (productIndex != null) {
        final product = oldState.data.items?[productIndex];
        if (product != null) {
          final editedProduct =
              product.copyWith(isAccepted: !product.isAccepted);
          oldState.data.items?[productIndex] = editedProduct;
          _updateTotalVat(oldState.data);
        }
      }
    }
  }

  void acceptAllProducts() {
    if (state is LoadedViewState<QuotationsModel>) {
      final oldState = state as LoadedViewState<QuotationsModel>;
      final editProducts = oldState.data.items?.map<QuotationDetailsModel>((v) {
        return oldState.data.mustAprroveAll == true ||
                oldState.data.initialApproval == true
            ? v
            : v.copyWith(isAccepted: true);
      }).toList();
      if (editProducts != null) {
        oldState.data.items?.replaceRange(0, editProducts.length, editProducts);
      }
      _updateTotalVat(oldState.data);
    }
  }

  void rejectAllProducts() {
    if (state is LoadedViewState<QuotationsModel>) {
      final oldState = state as LoadedViewState<QuotationsModel>;
      final editProducts = oldState.data.items
          ?.map<QuotationDetailsModel>((v) => v.copyWith(isAccepted: false))
          .toList();
      if (editProducts != null) {
        oldState.data.items?.replaceRange(0, editProducts.length, editProducts);
      }
      setLoadedState(oldState.data);
    }
  }

  bool get isAllAccepted {
    if (state is LoadedViewState<QuotationsModel>) {
      final oldState = state as LoadedViewState<QuotationsModel>;
      if (oldState.data.items != null) {
        return !oldState.data.items!
            .any((element) => element.isAccepted == false);
      }
    }
    return false;
  }

  bool get isAtLeastOneAccepted {
    if (state is LoadedViewState<QuotationsModel>) {
      final oldState = state as LoadedViewState<QuotationsModel>;
      if (oldState.data.items != null) {
        return oldState.data.items!.any((element) => element.isAccepted);
      }
    }
    return false;
  }

  double get totalAmount {
    if (state is LoadedViewState<QuotationsModel>) {
      final oldState = state as LoadedViewState<QuotationsModel>;
      if (oldState.data.items != null) {
        return oldState.data.items!
                .where(
                  (element) => element.isAccepted,
                )
                .map((data) => data.total)
                .reduce((prev, next) => (prev! + next!)) ??
            0;
      }
    }
    return 0;
  }
}
