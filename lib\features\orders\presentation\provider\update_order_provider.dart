import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/orders/data/models/orders_model.dart';
import 'package:mdd/features/orders/domain/usecases/update_order.dart';
import 'package:mdd/features/orders/presentation/provider/add_order_provider.dart';

final updateOrderProvider =
    StateNotifierProvider.autoDispose<UpdateOrderProvider, ViewState>(
  (ref) => UpdateOrderProvider(
    ref.watch(updateOrderUseCaseProvider),
    ref.watch(addOrderProvider).getOrder(),
  ),
);

class UpdateOrderProvider extends BaseProvider<ResultModel> {
  final UpdateOrder _updateOrder;
  final OrdersModel? _order;

  UpdateOrderProvider(this._updateOrder, this._order);

  Future<void> updateOrder() async {
    setLoadingState();
    final response = await _updateOrder.call(
      UpdateOrderParams(_order),
    );
    response.fold(
      (failure) {
        setErrorState(failure.message);
      },
      setLoadedState,
    );
  }
}
