import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/core/widgets/search_bar_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/projects/presentation/provider/project_text_field_provider.dart';
import 'package:mdd/features/projects/presentation/provider/projects_provider.dart';
import 'package:mdd/features/projects/presentation/screens/projects_view.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

@RoutePage()
class ProjectsScreen extends ConsumerStatefulWidget {
  const ProjectsScreen({
    super.key,
  });

  @override
  ConsumerState createState() => _ProjectsScreenState();
}

class _ProjectsScreenState extends ConsumerState<ProjectsScreen> {
  void _fetch() {
    ref.read(projectsProvider.notifier).fetchProjects();
  }

  @override
  void initState() {
    super.initState();
    UiHelper.postBuildCallback((p0) {
      _fetch();
    });
  }

  @override
  Widget build(BuildContext context) {
    final projectSearchController = ref.watch(projectSearchControllerProvider);
    final projectProvider = ref.read(projectsProvider.notifier);
    return Scaffold(
      appBar: const MainAppBar(),
      floatingActionButton: FloatingActionButton.extended(
        heroTag: 'floating_action_projects_screen',
        onPressed: () {
          context.pushRoute(const CreateProjectRoute());
        },
        label: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.kSizeSmall,
          ),
          child: TextWidget(
            '+ ${'add_project'.tr()}',
            color: AppColors.white,
          ),
        ),
      ),
      body: Column(
        children: [
          DetailsCustomBar(title: 'projects'.tr()),
          SearchBarWidget(
            keyboardType: TextInputType.text,
            showClearIcon: ref.watch(projectSuffixIconProvider),
            onPressedSearch: () {
              if (projectSearchController.text.isNotEmpty) {
                projectProvider.resetPagination();
                _fetch();
              }
            },
            onPressedClear: () {
              ref.read(projectSuffixIconProvider.state).state = false;
              projectSearchController.clear();
              projectProvider.resetPagination();
              _fetch();
            },
            textController: projectSearchController,
            onChange: (String value) {
              if (value.isEmpty) {
                ref.read(projectSuffixIconProvider.state).state = false;
                projectProvider.resetPagination();
                _fetch();
                return;
              }
              ref.read(projectSuffixIconProvider.state).state = true;
            },
          ),
          const Expanded(child: ProjectsView()),
        ],
      ),
    );
  }
}
