import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/user/domain/entities/role_entity.dart';

part 'role_model.g.dart';

@JsonSerializable()
class RoleModel extends RoleEntity {
  RoleModel({
    super.roleId,
    super.roleName,
    super.displayName,
    super.employeeId,
    super.employeeName,
    super.employeeRoleId,
    super.group,
  });

  factory RoleModel.fromJson(Map<String, dynamic> json) =>
      _$RoleModelFromJson(json);

  Map<String, dynamic> toJson() => _$RoleModelToJson(this);
}
