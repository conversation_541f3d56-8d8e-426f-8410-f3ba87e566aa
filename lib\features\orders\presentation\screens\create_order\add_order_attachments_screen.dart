import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/form_field_widget.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/features/attachments/presentation/screens/attachments_view.dart';
import 'package:mdd/features/orders/presentation/provider/add_order_provider.dart';
import 'package:mdd/features/orders/presentation/provider/selected_order_type_provider.dart';
import 'package:mdd/features/orders/presentation/widgets/bottom_order_widget.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

@RoutePage()
class AddOrderAttachmentsScreen extends ConsumerStatefulWidget {
  const AddOrderAttachmentsScreen({
    super.key,
  });

  @override
  ConsumerState createState() => _AddOrderAttachmentsScreenState();
}

class _AddOrderAttachmentsScreenState
    extends ConsumerState<AddOrderAttachmentsScreen> {
  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      ref.read(descriptionOrderProvider).text =
          ref.read(addOrderProvider).order?.description ?? '';
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MainAppBar(),
      body: Column(
        children: [
          DetailsCustomBar(
            title: 'add_order'.tr(),
            activeIndex: 1,
          ),
          FormFieldWidget(
            controller: ref.watch(descriptionOrderProvider),
            fillColor: AppColors.cardDetailsBackground,
            borderColor: AppColors.cardDetailsBackground,
            hintText: 'add_note'.tr(),
            textInputType: TextInputType.text,
            maxLines: 5,
          ),
          const Expanded(
            child: SingleChildScrollView(
              child: AttachmentsView(),
            ),
          ),
          BottomOrderWidget(
            onNextPressed: () => context.pushRoute(const OrderSummaryRoute()),
          ),
        ],
      ),
    );
  }
}
