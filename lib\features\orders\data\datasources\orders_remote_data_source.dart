import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/endPoints/end_points.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/features/orders/data/models/orders_model.dart';
import 'package:mdd/features/orders/data/models/orders_params.dart';
import 'package:mdd/features/orders/domain/usecases/approve_order.dart';
import 'package:mdd/features/orders/domain/usecases/create_order.dart';
import 'package:mdd/features/orders/domain/usecases/order_details.dart';
import 'package:mdd/features/orders/domain/usecases/update_order.dart';
import 'package:mdd/services/dio_client.dart';

abstract class OrdersRemoteDataSource {
  Future<List<OrdersModel>> getOrders(OrdersParams params);
  Future<OrdersModel> getOrderById(OrderDetailsParams params);
  Future<ResultModel> updateOrder(UpdateOrderParams params);
  Future<ResultModel> createOrder(CreateOrderParams params);
  Future<ResultModel> approveOrder(ApproveOrderParams params);
}

final ordersRemoteDataSourceImpl = Provider<OrdersRemoteDataSourceImpl>(
    (ref) => OrdersRemoteDataSourceImpl(ref.watch(dioClientProvider)));

class OrdersRemoteDataSourceImpl implements OrdersRemoteDataSource {
  final DioClient _dioClient;

  OrdersRemoteDataSourceImpl(this._dioClient);
  @override
  Future<List<OrdersModel>> getOrders(OrdersParams params) async {
    final response = await _dioClient.dio.get(
      EndPoints.orders,
      queryParameters: params.toJson()
        ..removeWhere((key, value) => value == null || value == ''),
    );
    return (response.data['data'] as List)
        .map((order) => OrdersModel.fromJson(order))
        .toList();
  }

  @override
  Future<OrdersModel> getOrderById(OrderDetailsParams params) async {
    final response = await _dioClient.dio.get(
      '${EndPoints.orders}/${params.id}',
      queryParameters: {
        "includeItems": params.includeItems,
        "includeQuotations": params.includeQuotations,
        "includeAttachments": params.includeAttachments,
        "includeComments": params.includeComments,
        "includeApprovals": params.includeApprovals,
      },
    );

    return OrdersModel.fromJson(response.data['data']);
  }

  @override
  Future<ResultModel> createOrder(CreateOrderParams params) async {
    final response = await _dioClient.dio.post(
      params.isDraft ? EndPoints.orders : EndPoints.submitOrders,
      data: {
        "projectID": params.createdOrder.projectID,
        "organizationID": params.createdOrder.organizationID,
        "customerID": params.createdOrder.customerID,
        "attachments": params.createdOrder.attachments,
        "orderType": params.createdOrder.orderType,
        "details": params.createdOrder.orderDetails,
        "cancelNote": params.createdOrder.description,
      },
    );

    return ResultModel.fromJson(response.data);
  }

  @override
  Future<ResultModel> updateOrder(UpdateOrderParams params) async {
    final response = await _dioClient.dio.put(
      EndPoints.orders,
      data: params.order?.toJson(),
    );
    return ResultModel.fromJson(response.data);
  }

  @override
  Future<ResultModel> approveOrder(ApproveOrderParams params) async {
    final response = await _dioClient.dio.post(
      EndPoints.approveOrders,
      data: {
        "orderID": params.orderID,
        "status": params.status,
        "notes": params.notes
      },
    );

    return ResultModel.fromJson(response.data);
  }
}
