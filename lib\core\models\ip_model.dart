class IpModel {
  IpModel({
    this.ip,
    this.org,
    this.city,
    this.region,
    this.timezone,
  });

  String? ip;
  String? org;
  String? city;
  String? region;
  String? timezone;

  factory IpModel.fromJson(Map<String, dynamic> json) => IpModel(
        ip: json["ip"],
        org: json["org"],
        city: json["city"],
        region: json["region"],
        timezone: json["timezone"],
      );

  Map<String, dynamic> toJson() => {
        "ip": ip,
        "org": org,
        "city": city,
        "region": region,
        "timezone": timezone,
      };
}
