import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/drop_down_widget.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/presentation/providers/reject_reasons_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class RejectReasonsDropDown extends ConsumerStatefulWidget {
  const RejectReasonsDropDown({Key? key}) : super(key: key);

  @override
  ConsumerState<RejectReasonsDropDown> createState() =>
      _RejectReasonsDropDownState();
}

class _RejectReasonsDropDownState extends ConsumerState<RejectReasonsDropDown> {
  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      ref.read(rejectReasonsProvider.notifier).fetchRejectReasons();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(rejectReasonsProvider);
    if (state is LoadingViewState) {
      return const LoaderWidget();
    }
    if (state is LoadedViewState<List<DropDownEntity>>) {
      return Padding(
        padding: const EdgeInsets.symmetric(
          vertical: AppDimensions.kSizeSmall,
          horizontal: AppDimensions.kSizeMedium,
        ),
        child: DropDownWidget<DropDownEntity>(
          title: 'حدد سبب الرفض',
          validatorMessage: 'الرجاء اختيار سبب الرفض',
          onChanged: (rejectReason) {
            ref.watch(selectedRejectReasonProvider.notifier).state =
                rejectReason;
          },
          borderColor: AppColors.cardDetailsBackground,
          fillColor: AppColors.white,
          itemAsString: (rejectReason) =>
              (context.locale.languageCode == 'en'
                  ? rejectReason.textEn ?? rejectReason.textAr
                  : rejectReason.textAr ?? rejectReason.textEn) ??
              '',
          items: state.data,
        ),
      );
    }
    return const SizedBox();
  }
}
