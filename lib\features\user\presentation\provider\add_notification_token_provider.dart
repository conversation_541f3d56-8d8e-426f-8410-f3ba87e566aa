import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/user/domain/usecases/notification_token.dart';

final addNotificationTokenProvider =
    StateNotifierProvider<AddNotificationTokenProvider, ViewState>(
        (ref) => AddNotificationTokenProvider(
              ref.watch(notificationTokenUseCaseProvider),
            ));

class AddNotificationTokenProvider extends BaseProvider<ResultModel> {
  final NotificationToken _notificationToken;
  AddNotificationTokenProvider(
    this._notificationToken,
  );

  Future<void> addNotificationToken(
      {required NotificationTokenParams params}) async {
    setLoadingState();
    final response = await _notificationToken.call(params);
    response.fold((failure) {
      setErrorState(failure.message);
    }, (response) {
      setLoadedState(response);
    });
  }
}
