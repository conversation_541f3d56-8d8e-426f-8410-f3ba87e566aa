// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'package_info.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$packageInfoHash() => r'1a9dfaf6ac2b1577a48d408bcc4852f8ae20e628';

/// See also [packageInfo].
@ProviderFor(packageInfo)
final packageInfoProvider = Provider<PackageInfo>.internal(
  packageInfo,
  name: r'packageInfoProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$packageInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef PackageInfoRef = ProviderRef<PackageInfo>;
String _$packageInfoFutureHash() => r'8db3549e8db6c6c16b8d31e2d82975b26b7c0e99';

/// See also [packageInfoFuture].
@ProviderFor(packageInfoFuture)
final packageInfoFutureProvider = FutureProvider<PackageInfo>.internal(
  packageInfoFuture,
  name: r'packageInfoFutureProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$packageInfoFutureHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef PackageInfoFutureRef = FutureProviderRef<PackageInfo>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
