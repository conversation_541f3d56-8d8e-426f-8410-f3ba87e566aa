import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/orders/data/models/orders_params.dart';
import 'package:mdd/features/orders/data/repositories/orders_repository_impl.dart';
import 'package:mdd/features/orders/domain/entities/orders_entity.dart';
import 'package:mdd/features/orders/domain/repositories/orders_repository.dart';

final ordersUseCaseProvider =
    Provider<Orders>((ref) => Orders(ref.watch(orderRepositoryImpl)));

class Orders implements UseCase<List<OrdersEntity>?, OrdersParams> {
  final OrdersRepository ordersRepository;

  Orders(this.ordersRepository);

  @override
  Future<Either<Failure, List<OrdersEntity>?>> call(OrdersParams params) async {
    return await ordersRepository.getOrders(params);
  }
}
