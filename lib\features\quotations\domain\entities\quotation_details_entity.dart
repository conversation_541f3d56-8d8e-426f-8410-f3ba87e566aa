class QuotationDetailsEntity {
  final String? quotationDetailID;
  final int? orderNo;
  final String? productID;
  final String? productName;
  final String? supplierID;
  final String? supplierName;
  final String? unitID;
  final String? unitName;
  final bool isAccepted;
  final bool isVATExcluded;
  final double? price;
  final double? purchasePrice;
  final int? qty;
  final double? total;

//<editor-fold desc="data Methods">

  const QuotationDetailsEntity({
    this.quotationDetailID,
    this.orderNo,
    this.productID,
    this.productName,
    this.supplierID,
    this.supplierName,
    this.unitID,
    this.unitName,
    required this.isAccepted,
    required this.isVATExcluded,
    this.price,
    this.purchasePrice,
    this.qty,
    this.total,
  });
}
