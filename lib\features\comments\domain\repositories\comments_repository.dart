import 'package:dartz/dartz.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/features/comments/data/models/comments_model.dart';
import 'package:mdd/features/comments/domain/usecases/add_comments.dart';
import 'package:mdd/features/comments/domain/usecases/order_comments.dart';

abstract class CommentsRepository {
  Future<Either<Failure, ResultModel>> addComment(AddCommentsParams params);
  Future<Either<Failure, List<CommentsModel>>> orderComments(
      OrderCommentsParams params);
}
