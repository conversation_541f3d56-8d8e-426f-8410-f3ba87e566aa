import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/endPoints/end_points.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/organization/data/models/organizations_model.dart';
import 'package:mdd/features/organization/domain/usecases/update_organization.dart';
import 'package:mdd/services/dio_client.dart';

abstract class OrgsRemoteDataSource {
  Future<OrgsModel> orgDetails(NoParams params);
  Future<ResultModel> update(UpdateOrgParams params);
}

final orgsRemoteDataSourceImpl = Provider<OrgsRemoteDataSourceImpl>(
  (ref) {
    return OrgsRemoteDataSourceImpl(ref.watch(dioClientProvider));
  },
);

class OrgsRemoteDataSourceImpl implements OrgsRemoteDataSource {
  final DioClient _dioClient;

  OrgsRemoteDataSourceImpl(this._dioClient);

  @override
  Future<OrgsModel> orgDetails(NoParams params) async {
    final response = await _dioClient.dio.get(EndPoints.organizations);
    return OrgsModel.fromJson(response.data['data']);
  }

  @override
  Future<ResultModel> update(UpdateOrgParams params) async {
    final response = await _dioClient.dio.put(
      EndPoints.organizationProfile,
      data: params.org.toJson(),
    );
    return ResultModel.fromJson(response.data);
  }
}
