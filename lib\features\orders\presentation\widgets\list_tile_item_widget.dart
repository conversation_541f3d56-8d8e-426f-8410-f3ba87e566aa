import 'package:flutter/material.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/theme/colors.dart';

class ListTileItemWidget extends StatelessWidget {
  final String title;
  final VoidCallback onTap;
  final bool isSelected;
  const ListTileItemWidget(
      {super.key,
      required this.title,
      required this.onTap,
      required this.isSelected});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListTile(
            contentPadding: EdgeInsets.zero,
            dense: true,
            onTap: onTap,
            title: TextWidget(
              title,
              color: isSelected ? null : AppColors.tabTextColor,
            ),
            trailing: const Icon(
              Icons.arrow_drop_down,
              color: AppColors.primary,
            )),
        const Divider(
          height: 0,
        ),
      ],
    );
  }
}
