import 'package:dartz/dartz.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/features/customer/data/models/customers_model.dart';
import 'package:mdd/features/customer/domain/usecases/create_new_customer.dart';
import 'package:mdd/features/customer/domain/usecases/customers.dart';
import 'package:mdd/features/customer/domain/usecases/update_customer.dart';

abstract class CustomersRepository {
  Future<Either<Failure, List<CustomersModel>>> getCustomers(
      CustomersParams params);

  Future<Either<Failure, ResultModel>> createNewCustomer(
      CreateCustomerParams params);

  Future<Either<Failure, ResultModel>> updateCustomer(
      UpdateCustomerParams params);
}
