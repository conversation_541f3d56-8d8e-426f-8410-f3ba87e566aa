import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/domain/usecases/projects_list.dart';

final selectedProjectProvider =
    StateProvider.autoDispose<DropDownEntity?>((ref) => null);

final projectsListProvider =
    StateNotifierProvider.autoDispose<ProjectsListProvider, ViewState>(
  (ref) => ProjectsListProvider(
    ref.watch(projectsListUseCaseProvider),
  ),
);

class ProjectsListProvider extends BaseProvider<List<DropDownEntity>> {
  final ProjectsList _projects;

  ProjectsListProvider(this._projects);

  Future<void> fetchProjectsList(String? orgID) async {
    setLoadingState();
    final res = await _projects.call(ProjectsListParams(orgID: orgID));
    res.fold(
      (failure) => setErrorState(failure.message),
      setLoadedState,
    );
  }
}
