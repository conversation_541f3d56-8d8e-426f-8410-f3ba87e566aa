import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/features/orders/domain/entities/orders_entity.dart';
import 'package:mdd/features/orders/presentation/provider/order_details_provider.dart';
import 'package:mdd/features/orders/presentation/provider/order_tap_provider.dart';
import 'package:mdd/features/orders/presentation/screens/order_details/attachments_details_tap_view.dart';
import 'package:mdd/features/orders/presentation/screens/order_details/comments_details_tap_view.dart';
import 'package:mdd/features/orders/presentation/screens/order_details/note_details_tap_view.dart';
import 'package:mdd/features/orders/presentation/screens/order_details/order_details_tap_menu.dart';
import 'package:mdd/features/orders/presentation/screens/order_details/order_details_tap_view.dart';
import 'package:mdd/features/orders/presentation/screens/order_details/quotation_details_tap_view.dart';
import 'package:mdd/features/quotations/presentation/screens/approvals_details_tap_view.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class OrderDetailsView extends ConsumerStatefulWidget {
  final String orderId;
  const OrderDetailsView({
    super.key,
    required this.orderId,
  });

  @override
  ConsumerState createState() => _OrderDetailsViewState();
}

class _OrderDetailsViewState extends ConsumerState<OrderDetailsView> {
  void _fetch() {
    ref.read(orderDetailsProvider.notifier).fetchGetOrderById(widget.orderId);
  }

  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      _fetch();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      final selectedTapProvider = ref.watch(tapMenuSelected.state);
      final state = ref.watch(orderDetailsProvider);
      if (state is LoadingViewState) {
        return const Center(child: LoaderWidget());
      }
      if (state is LoadedViewState<OrdersEntity>) {
        return Column(
          children: [
            OrderDetailsTapMenu(state.data),
            const SizedBox(
              height: AppDimensions.kSizeSmall,
            ),
            if (selectedTapProvider.state == 0)
              OrderDetailsTapView(order: state.data),
            if (selectedTapProvider.state == 1)
              QuotationDetailsTapView(
                quotations: state.data.quotations ?? [],
              ),
            if (selectedTapProvider.state == 2)
              AttachmentsDetailsTapView(
                attachments: state.data.attachments ?? [],
              ),
            if (selectedTapProvider.state == 3 && state.data.cancelNote != null)
              NoteDetailsTapView(
                note: state.data.cancelNote!,
              ),
            if (selectedTapProvider.state == 4)
              CommentsDetailsTapView(
                orderId: state.data.orderID ?? '',
                comments: state.data.comments
                        ?.where(
                          (element) => !element.isInternal,
                        )
                        .toList() ??
                    [],
              ),
            if (selectedTapProvider.state == 5)
              ApprovalsDetailsTapView(approvals: state.data.approvals ?? [])
          ],
        );
      }
      return const SizedBox();
    });
  }
}
