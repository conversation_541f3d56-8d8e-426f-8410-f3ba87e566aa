import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/utils/constants/constants.dart';
import 'package:mdd/utils/enums.dart';

part 'orders_params.g.dart';

@JsonSerializable(explicitToJson: true)
class OrdersParams {
  final int page;
  final OrderStatusType? status;
  final String? find;
  final String? orderType;
  final String sort;
  final int limit;
  final bool includeQuotations;
  final bool includeDetails;

  OrdersParams({
    required this.page,
    this.status,
    this.find,
    this.orderType,
    this.sort = 'CreatedOnDesc',
    this.limit = AppConstants.paginationLimit,
    this.includeQuotations = true,
    this.includeDetails = true,
  });

  factory OrdersParams.fromJson(Map<String, dynamic> json) =>
      _$OrdersParamsFromJson(json);

  Map<String, dynamic> toJson() => _$OrdersParamsToJson(this);
}
