import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';

class CardTransferInfo extends StatelessWidget {
  const CardTransferInfo({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    const subTextStyle = TextStyle(
      color: AppColors.deepBlue,
      fontWeight: FontWeight.bold,
    );
    const mainTextStyle = TextStyle(
      color: AppColors.deepBlue,
    );

    return Card(
      color: AppColors.lightBlue,
      margin: const EdgeInsets.symmetric(
        vertical: AppDimensions.kSizeSmall,
        horizontal: AppDimensions.kSizeMedium,
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.kSizeMedium),
        child: Column(
          children: [
            Text(
              'transfer_info'.tr(),
              style: const TextStyle(
                color: AppColors.deepBlue,
              ),
            ),
            const SizedBox(height: 10.0),
            Table(
              columnWidths: const {
                0: FlexColumnWidth(0.8),
                1: FlexColumnWidth(2),
              },
              children: [
                TableRow(
                  children: [
                    Text(
                      'bank'.tr(),
                      style: mainTextStyle,
                    ),
                    const Text(
                      'مصرف الإنماء - Inmaa Bank',
                      style: subTextStyle,
                    ),
                  ],
                ),
                TableRow(
                  children: [
                    Text(
                      'account_name'.tr(),
                      style: mainTextStyle,
                    ),
                    const Text(
                      'مؤسسة مدد الذكية - .SMART MDD Co',
                      style: subTextStyle,
                    ),
                  ],
                ),
                TableRow(
                  children: [
                    Text(
                      'iban'.tr(),
                      style: mainTextStyle,
                    ),
                    const SelectableText(
                      '************************',
                      style: subTextStyle,
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
