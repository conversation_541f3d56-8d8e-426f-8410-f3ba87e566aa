import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/organization/data/models/organizations_model.dart';
import 'package:mdd/features/organization/domain/usecases/update_organization.dart';

final updateOrgProvider = StateNotifierProvider<UserOrgProvider, ViewState>(
  (ref) => UserOrgProvider(ref.watch(updateOrgUseCaseProvider)),
);

class UserOrgProvider extends BaseProvider<ResultModel> {
  final UpdateOrg _updateOrg;

  UserOrgProvider(this._updateOrg);

  Future<void> updateOrg(OrgsModel orgs) async {
    setLoadingState();
    final response = await _updateOrg.call(UpdateOrgParams(orgs));
    response.fold(
      (failure) {
        setErrorState(failure.message);
      },
      setLoadedState,
    );
  }
}
