import 'package:flutter/material.dart';
import 'package:mdd/core/widgets/subtitle_widget.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';

class TapMenuWidget extends StatelessWidget {
  final bool isActive;
  final String? tabTitle;
  final String? leadingTitle;
  final VoidCallback onTap;

  const TapMenuWidget({
    super.key,
    required this.isActive,
    required this.tabTitle,
    this.leadingTitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Card(
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
            color: isActive
                ? AppColors.tabBackgroundColor
                : AppColors.white,
            elevation: 0,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(
                  width: AppDimensions.kSizeSmall,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: AppDimensions.kSizeSmall / 2,
                  ),
                  child: SubtitleWidget(
                    tabTitle ?? '',
                    color: isActive ? AppColors.white : AppColors.grey2,
                  ),
                ),
                if (leadingTitle != null)
                  Card(
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5)),
                    color: isActive
                        ? AppColors.white.withOpacity(0.3)
                        : AppColors.cardColor,
                    elevation: 0,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppDimensions.kSizeXSmall,
                        vertical: AppDimensions.kSizeXSmall / 2,
                      ),
                      child: SubtitleWidget(
                        leadingTitle!,
                        color:
                            isActive ? AppColors.white : AppColors.tabTextColor,
                      ),
                    ),
                  ),
                const SizedBox(
                  width: AppDimensions.kSizeSmall,
                ),
              ],
            ),
          ),
          // SvgPicture.asset(
          //   'assets/icons/horizontal_dots.svg',
          //   color: isActive
          //       ? AppColors.tabBackgroundColor
          //       : AppColors.tabTextColor,
          // ),
          const SizedBox(
            width: AppDimensions.kSizeXSmall,
          ),
        ],
      ),
    );
  }
}
