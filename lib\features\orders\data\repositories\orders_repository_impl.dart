import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/exceptions/exceptions.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/features/orders/data/datasources/orders_remote_data_source.dart';
import 'package:mdd/features/orders/data/models/orders_model.dart';
import 'package:mdd/features/orders/data/models/orders_params.dart';
import 'package:mdd/features/orders/domain/entities/orders_entity.dart';
import 'package:mdd/features/orders/domain/repositories/orders_repository.dart';
import 'package:mdd/features/orders/domain/usecases/approve_order.dart';
import 'package:mdd/features/orders/domain/usecases/create_order.dart';
import 'package:mdd/features/orders/domain/usecases/order_details.dart';
import 'package:mdd/features/orders/domain/usecases/update_order.dart';

final orderRepositoryImpl = Provider<OrdersRepositoryImpl>(
  (ref) => OrdersRepositoryImpl(
    ref.watch(ordersRemoteDataSourceImpl),
  ),
);

class OrdersRepositoryImpl implements OrdersRepository {
  final OrdersRemoteDataSource ordersRemoteDataSource;
  OrdersRepositoryImpl(this.ordersRemoteDataSource);
  @override
  Future<Either<Failure, List<OrdersEntity>>> getOrders(
      OrdersParams params) async {
    try {
      final res = await ordersRemoteDataSource.getOrders(params);

      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }

  @override
  Future<Either<Failure, OrdersModel>> getOrderById(
      OrderDetailsParams params) async {
    try {
      final res = await ordersRemoteDataSource.getOrderById(params);

      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }

  @override
  Future<Either<Failure, ResultModel>> createOrder(
      CreateOrderParams params) async {
    try {
      final res = await ordersRemoteDataSource.createOrder(params);

      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }

  @override
  Future<Either<Failure, ResultModel>> updateOrder(
      UpdateOrderParams params) async {
    try {
      final response = await ordersRemoteDataSource.updateOrder(params);

      return Right(response);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException catch (e) {
      return Left(InternalServerErrorFailure(message: e.error?.toString()));
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.error?.toString()));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }

  @override
  Future<Either<Failure, ResultModel>> approveOrder(
      ApproveOrderParams params) async {
    try {
      final res = await ordersRemoteDataSource.approveOrder(params);

      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }
}
