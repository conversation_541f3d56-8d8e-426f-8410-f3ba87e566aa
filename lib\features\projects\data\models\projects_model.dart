import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/customer/data/models/customers_model.dart';
import 'package:mdd/features/projects/domain/entities/projects_entity.dart';

part 'projects_model.g.dart';

@JsonSerializable(explicitToJson: true)
class ProjectsModel extends ProjectsEntity {
  ProjectsModel({
    super.projectID,
    required super.organizationID,
    this.organizationName,
    required super.name,
    required super.location,
    required super.description,
    this.createdOn,
    required super.customerID,
    this.customerName,
    this.customerEmail,
    this.customerMobile,
    this.technicalApproverID,
    this.technicalApproverName,
    this.technicalApproverEmail,
    this.finalApproverID,
    this.finalApproverName,
    this.finalApproverEmail,
    super.contactPerson,
  });

  final String? organizationName;
  final String? createdOn;
  final String? customerName;
  final String? customerEmail;
  final String? customerMobile;
  final String? technicalApproverID;
  final String? technicalApproverName;
  final String? technicalApproverEmail;
  final String? finalApproverID;
  final String? finalApproverName;
  final String? finalApproverEmail;

  factory ProjectsModel.fromJson(Map<String, dynamic> json) =>
      _$ProjectsModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectsModelToJson(this);

  ProjectsModel copyWith({
    String? projectID,
    String? organizationID,
    String? name,
    String? location,
    String? description,
    String? customerID,
    String? customerName,
    String? createdOn,
  }) {
    return ProjectsModel(
      projectID: projectID ?? this.projectID,
      customerID: customerID ?? this.customerID,
      customerName: customerName ?? this.customerName,
      organizationID: organizationID ?? this.organizationID,
      name: name ?? this.name,
      location: location ?? this.location,
      description: description ?? this.description,
      createdOn: createdOn ?? this.createdOn,
    );
  }
}
