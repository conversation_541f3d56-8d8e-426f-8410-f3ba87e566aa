import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/title_item_widget.dart';
import 'package:mdd/features/attachments/presentation/provider/attachments_provider.dart';
import 'package:mdd/features/attachments/presentation/widgets/attachment_widget.dart';
import 'package:mdd/features/attachments/presentation/widgets/attachments_list_view.dart';
import 'package:mdd/utils/helper_functions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class AttachmentsView extends ConsumerWidget {
  final bool primary;
  final String? buttonTitle;
  final int maximumAttachments;
  const AttachmentsView(
      {super.key,
      this.primary = false,
      this.buttonTitle,
      this.maximumAttachments = 5});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        TitleItemWidget(title: 'attachments'.tr()),
        AttachmentWidget(
          title: 'maximum_attachment'.tr(),
          buttonTitle: buttonTitle ?? 'add_attachment'.tr(),
          onPressed: ref.watch(attachmentsProvider).totalAttachments <
                  maximumAttachments
              ? () async {
                  final result = await FilePicker.platform.pickFiles(
                    type: FileType.custom,
                    allowedExtensions: ['pdf', 'jpg', 'png', 'jpeg'],
                  );

                  if (result != null && result.files.single.path != null) {
                    if (HelperFunctions.isAttachmentAllowed(
                        result.files.single.size)) {
                      ref
                          .read(attachmentsProvider)
                          .addAttachments(result.files.single);
                    } else {
                      UiHelper.showNotification('maximum_attachment'.tr());
                    }
                  }
                }
              : null,
        ),
        AttachmentsListView(primary: primary),
      ],
    );
  }
}
