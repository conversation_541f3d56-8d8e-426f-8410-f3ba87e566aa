import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/attachments/domain/entites/attachments_entity.dart';

part 'attachments_model.g.dart';

@JsonSerializable()
class AttachmentsModel extends AttachmentsEntity {
  AttachmentsModel(
      {required super.fileContent,
      required super.fileContentType,
      required super.fileName,
      required super.orderAttachmentId});
  factory AttachmentsModel.fromJson(Map<String, dynamic> json) =>
      _$AttachmentsModelFromJson(json);

  Map<String, dynamic> toJson() => _$AttachmentsModelToJson(this);
}
