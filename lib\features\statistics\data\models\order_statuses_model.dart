import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/statistics/domain/entity/order_statuses_entity.dart';
import 'package:mdd/utils/enums.dart';

part 'order_statuses_model.g.dart';

@JsonSerializable()
class OrderStatusesModel extends OrderStatusesEntity {
  OrderStatusesModel({
    required super.status,
    required super.statusName,
    required super.ordersCount,
    required super.ordersAmount,
  });

  factory OrderStatusesModel.fromJson(Map<String, dynamic> json) =>
      _$OrderStatusesModelFromJson(json);

  Map<String, dynamic> toJson() => _$OrderStatusesModelToJson(this);
}
