import 'dart:math' as math;

import 'package:animate_do/animate_do.dart';
import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/extensions.dart';

@RoutePage()
class SecondOnBoardingScreen extends StatelessWidget {
  const SecondOnBoardingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          constraints: const BoxConstraints.expand(),
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage('assets/images/login_background.png'),
              opacity: 0.1,
              fit: BoxFit.cover,
            ),
            color: Color(0xFF252D2E),
          ),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          body: SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElasticIn(
                  child: Image.asset(
                    'assets/images/on_boarding_image2.png',
                    height: context.widthR(0.9),
                  ),
                ),
                Column(
                  children: [
                    TextWidget(
                      'on_boarding_title2'.tr(),
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: context.textTheme.headlineMedium?.fontSize,
                    ),
                    const SizedBox(height: AppDimensions.kSize3XLarge),
                    const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircleAvatar(
                          radius: 5,
                          backgroundColor: AppColors.activeIndicatorColor,
                        ),
                        SizedBox(width: AppDimensions.kSizeSmall),
                        CircleAvatar(
                          radius: 5,
                          backgroundColor: AppColors.activeIndicatorColor,
                        ),
                        SizedBox(width: AppDimensions.kSizeSmall),
                        CircleAvatar(
                          radius: 5,
                          backgroundColor: AppColors.unActiveIndicatorColor,
                        ),
                        SizedBox(width: AppDimensions.kSizeSmall),
                        CircleAvatar(
                          radius: 5,
                          backgroundColor: AppColors.unActiveIndicatorColor,
                        ),
                      ],
                    ),
                  ],
                ),
                Transform.rotate(
                  angle: context.locale.languageCode == 'en' ? math.pi : 0,
                  child: InkWell(
                    onTap: () =>
                        context.replaceRoute(const ThirdOnBoardingRoute()),
                    child: Hero(
                      tag: 'on_boarding_button',
                      child: Image.asset(
                        'assets/images/rounded_button_ar.png',
                        height: 150,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
