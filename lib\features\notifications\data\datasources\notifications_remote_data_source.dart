import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/endPoints/end_points.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/notifications/data/models/notifications_model.dart';
import 'package:mdd/services/dio_client.dart';

abstract class NotificationsRemoteDataSource {
  Future<List<NotificationsModel>> getNotifications(NoParams params);
}

final notificationsRemoteDataSourceImpl =
    Provider<NotificationsRemoteDataSourceImpl>((ref) =>
        NotificationsRemoteDataSourceImpl(ref.watch(dioClientProvider)));

class NotificationsRemoteDataSourceImpl
    implements NotificationsRemoteDataSource {
  final DioClient _dioClient;

  NotificationsRemoteDataSourceImpl(this._dioClient);
  @override
  Future<List<NotificationsModel>> getNotifications(NoParams params) async {
    final response = await _dioClient.dio.get(EndPoints.notifications);
    return (response.data['data'] as List)
        .map((notification) => NotificationsModel.fromJson(notification))
        .toList();
  }
}
