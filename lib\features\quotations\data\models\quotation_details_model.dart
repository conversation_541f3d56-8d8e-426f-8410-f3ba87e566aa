import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/quotations/domain/entities/quotation_details_entity.dart';

part 'quotation_details_model.g.dart';

@JsonSerializable()
class QuotationDetailsModel extends QuotationDetailsEntity {
  QuotationDetailsModel({
    super.quotationDetailID,
    this.sNo,
    this.quotationID,
    this.quotationNo,
    this.orderID,
    super.orderNo,
    required super.productID,
    required super.productName,
    required super.supplierID,
    required super.supplierName,
    required super.unitID,
    required super.unitName,
    required super.isAccepted,
    required super.isVATExcluded,
    this.item,
    required super.price,
    required super.purchasePrice,
    required super.qty,
    required super.total,
    this.isReturned,
    this.returnRequestBy,
    this.returnRequestDate,
    this.returnBy,
    this.returnDate,
  });

  final int? sNo;
  final String? quotationID;
  final String? quotationNo;
  final String? orderID;
  final String? item;
  final bool? isReturned;
  final String? returnRequestBy;
  final String? returnRequestDate;
  final String? returnBy;
  final String? returnDate;

  factory QuotationDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$QuotationDetailsModelFromJson(json);

  Map<String, dynamic> toJson() => _$QuotationDetailsModelToJson(this);

  QuotationDetailsModel copyWith({
    String? quotationDetailID,
    int? sNo,
    String? quotationID,
    String? quotationNo,
    String? orderID,
    int? orderNo,
    String? productID,
    String? productName,
    String? supplierID,
    String? supplierName,
    String? unitID,
    String? unitName,
    bool? isAccepted,
    bool? isVATExcluded,
    String? item,
    double? price,
    double? purchasePrice,
    int? qty,
    double? total,
    bool? isReturned,
    String? returnRequestBy,
    String? returnRequestDate,
    String? returnBy,
    String? returnDate,
  }) {
    return QuotationDetailsModel(
      quotationDetailID: quotationDetailID ?? this.quotationDetailID,
      sNo: sNo ?? this.sNo,
      quotationID: quotationID ?? this.quotationID,
      quotationNo: quotationNo ?? this.quotationNo,
      orderID: orderID ?? this.orderID,
      orderNo: orderNo ?? this.orderNo,
      productID: productID ?? this.productID,
      productName: productName ?? this.productName,
      supplierID: supplierID ?? this.supplierID,
      supplierName: supplierName ?? this.supplierName,
      unitID: unitID ?? this.unitID,
      unitName: unitName ?? this.unitName,
      isAccepted: isAccepted ?? this.isAccepted,
      isVATExcluded: isVATExcluded ?? this.isVATExcluded,
      item: item ?? this.item,
      price: price ?? this.price,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      qty: qty ?? this.qty,
      total: total ?? this.total,
      isReturned: isReturned ?? this.isReturned,
      returnRequestBy: returnRequestBy ?? this.returnRequestBy,
      returnRequestDate: returnRequestDate ?? this.returnRequestDate,
      returnBy: returnBy ?? this.returnBy,
      returnDate: returnDate ?? this.returnDate,
    );
  }
}
