import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_flavor/flutter_flavor.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/app.dart';
import 'package:mdd/utils/config_reader.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class SentryReporter {
  static Future<void> setup({ProviderContainer? parent}) async {
    await SentryFlutter.init(
          (options) => options
        ..dsn = ConfigReader.getSentryDns()
      //we recommend lowering this value in production and use tracesSampler (Sentry Docs)
        ..tracesSampleRate = 1.0
        ..reportPackages = false
        ..considerInAppFramesByDefault = false
        ..attachScreenshot = true
        ..sendDefaultPii = true
        ..enableNdkScopeSync = true
        ..environment = FlavorConfig.instance.name,
      appRunner: () => runApp(
        ProviderScope(
          parent: parent,
          child: SentryScreenshotWidget(
            child: DefaultAssetBundle(
              bundle: SentryAssetBundle(enableStructuredDataTracing: true),
              child: const App(),
            ),
          ),
        ),
      ),
    );
  }

  static Future<void> genericThrow(dynamic exception,
      {dynamic stackTrace}) async {
    if (kDebugMode) {
      print('SentryReporter: $exception');
    } else {
      if (stackTrace != null) {
        await Sentry.captureException(exception, stackTrace: stackTrace);
      } else {
        await Sentry.captureException(exception);
      }
    }
  }
}
