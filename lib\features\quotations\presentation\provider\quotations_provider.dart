import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/paginated_provider.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/domain/usecases/quotations.dart';
import 'package:mdd/features/quotations/presentation/provider/quotation_state_provider.dart';

final quotationStatusProvider = StateProvider<int?>((ref) => null);
final quotationsProvider = StateNotifierProvider<QuotationsProvider, ViewState>(
    (ref) => QuotationsProvider(
          ref,
          ref.watch(quotationsUseCaseProvider),
        ));

class QuotationsProvider extends PaginatedProvider<QuotationsModel> {
  final Ref _ref;
  final Quotations _quotations;

  QuotationsProvider(this._ref, this._quotations) : super(InitialViewState());
  Future<void> fetchQuotations() async {
    state = LoadingViewState();
    final response = await fetchList();
    response.fold((failure) {
      state = ErrorViewState(errorMessage: failure.message);
    }, (quotations) {
      state = LoadedViewState(quotations);
    });
  }

  @override
  Future<Either<Failure, List<QuotationsModel>>> fetchList() {
    return _quotations.call(QuotationsParams(
      page: pageNumber++,
      quotationNo: _ref.watch(quotationSearchControllerProvider).text,
      quotationStatus: _ref.watch(quotationStatusProvider),
    ));
  }
}
