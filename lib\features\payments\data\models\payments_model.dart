import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/payments/domain/entities/payments_entity.dart';
import 'package:mdd/utils/enums.dart';

part 'payments_model.g.dart';

@JsonSerializable()
class PaymentsModel extends PaymentsEntity {
  PaymentsModel({
    required super.paymentID,
    this.invoiceID,
    this.orderID,
    super.orderNo,
    this.prNumber,
    this.quotationID,
    this.quotationNo,
    required super.paidAmount,
    required super.approvedAmount,
    super.paymentDate,
    super.transactionNo,
    this.bankID,
    super.bankName,
    this.paymentRecipt,
    required super.status,
    this.statusName,
    this.customerID,
    this.customerName,
    this.employeeID,
    this.employeeName,
    this.organizationID,
    this.organizationName,
    this.approvedBy,
    this.approvedName,
    this.approvedOn,
    this.rejectedOn,
    this.fileName,
    this.fileContent,
    this.fileContentType,
    required this.isDebit,
    required this.isFromDebitPayment,
    this.paymentDebitID,
  });

  final String? orderID;
  final String? prNumber;
  final String? quotationID;
  final String? quotationNo;
  final String? invoiceID;
  final String? employeeID;
  final String? employeeName;
  final String? bankID;
  final String? paymentRecipt;
  final String? statusName;
  final String? customerID;
  final String? customerName;
  final String? organizationID;
  final String? organizationName;
  final String? approvedBy;
  final String? approvedName;
  final String? approvedOn;
  final String? rejectedOn;
  final String? fileName;
  final String? fileContent;
  final String? fileContentType;
  final bool isDebit;
  final bool isFromDebitPayment;
  final String? paymentDebitID;

  factory PaymentsModel.fromJson(Map<String, dynamic> json) =>
      _$PaymentsModelFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentsModelToJson(this);
}
