import 'package:auto_route/annotations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/features/orders/domain/entities/orders_entity.dart';
import 'package:mdd/features/orders/presentation/screens/order_details_view.dart';

@RoutePage()
class OrderDetailsScreen extends ConsumerStatefulWidget {
  final OrdersEntity order;
  const OrderDetailsScreen({
    super.key,
    required this.order,
  });

  @override
  ConsumerState createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends ConsumerState<OrderDetailsScreen> {
  // void _fetch() {
  //   ref
  //       .read(orderDetailsProvider.notifier)
  //       .fetchGetOrderById(widget.order.orderID!);
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MainAppBar(),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: DetailsCustomBar(
                  title: widget.order.projectName ?? '',
                ),
              ),

            ],
          ),
          Expanded(child: OrderDetailsView(orderId: widget.order.orderID!)),
        ],
      ),
    );
  }
}
