import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/orders/domain/entities/order_details_entity.dart';

part 'order_details_model.g.dart';

@JsonSerializable()
class OrderDetailsModel extends OrderDetailsEntity {
  OrderDetailsModel(
      {super.orderDetailID,
      super.sNo,
      super.orderID,
      super.orderNo,
      required super.categoryID,
      required super.categoryName,
      super.description,
      required super.qty,
      required super.productID,
      required super.productName,
      required super.unitID,
      required super.unitName,
      super.fileName,
      super.createdOn,
      super.image});

  factory OrderDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$OrderDetailsModelFromJson(json);

  Map<String, dynamic> toJson() => _$OrderDetailsModelToJson(this)
    ..removeWhere((key, value) => value == null);

  OrderDetailsModel copyWith({
    String? orderDetailID,
    int? sNo,
    String? orderID,
    int? orderNo,
    String? categoryID,
    String? categoryName,
    String? description,
    int? qty,
    String? productID,
    String? productName,
    String? unitID,
    String? unitName,
    String? fileName,
    String? createdOn,
    String? image,
  }) {
    return OrderDetailsModel(
      orderDetailID: orderDetailID ?? this.orderDetailID,
      sNo: sNo ?? this.sNo,
      orderID: orderID ?? this.orderID,
      orderNo: orderNo ?? this.orderNo,
      categoryID: categoryID ?? this.categoryID,
      categoryName: categoryName ?? this.categoryName,
      description: description ?? this.description,
      qty: qty ?? this.qty,
      productID: productID ?? this.productID,
      productName: productName ?? this.productName,
      unitID: unitID ?? this.unitID,
      unitName: unitName ?? this.unitName,
      fileName: fileName ?? this.fileName,
      createdOn: createdOn ?? this.createdOn,
      image: image ?? this.image,
    );
  }
}
