import 'package:flutter/material.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/theme/colors.dart';

class CardMenuWidget extends StatelessWidget {
  final String title;
  final String content;
  final String image;
  final Color backgroundColor;

  const CardMenuWidget(
      {super.key,
      required this.title,
      required this.content,
      required this.image,
      required this.backgroundColor});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      color: backgroundColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(image),
          TextWidget(
            title,
            color: AppColors.white,
          ),
          TextWidget(content, color: AppColors.white),
        ],
      ),
    );
  }
}
