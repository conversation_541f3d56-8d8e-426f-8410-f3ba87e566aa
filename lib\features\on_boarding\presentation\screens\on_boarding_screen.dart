// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:mdd/features/on_boarding/presentation/provider/page_view_provider.dart';
//
// import 'first_on_boarding_screen.dart';
// import 'second_on_boarding_screen.dart';
// import 'third_on_boarding_screen.dart';
// import 'fourth_on_boarding_screen.dart';
//
// class OnBoardingScreen extends StatefulWidget {
//   const OnBoardingScreen({Key? key}) : super(key: key);
//
//   @override
//   _OnBoardingScreenState createState() => _OnBoardingScreenState();
// }
//
// class _OnBoardingScreenState extends State<OnBoardingScreen> {
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(body: Consumer(builder: (context, ref, child) {
//       return PageView(
//         controller: ref.read(pageControllerProvider),
//         children: const [
//           OnBoardingView1(),
//           OnBoardingView2(),
//           OnBoardingView3(),
//           OnBoardingView4(),
//         ],
//
//         // controller: ref.read(coastControllerProvider),
//         // observers: [
//         //   CrabController(),
//         // ],
//       );
//     }));
//   }
// }
