import 'package:auto_route/annotations.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/features/quotations/presentation/screens/quotation_details_view.dart';

@RoutePage()
class QuotationDetailsScreen extends StatelessWidget {
  final String quotationId;
  const QuotationDetailsScreen({
    super.key,
    required this.quotationId,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MainAppBar(),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DetailsCustomBar(
            title: 'quotation'.tr(),
          ),
          Expanded(
            child: QuotationDetailsView(quotationId: quotationId),
          ),
        ],
      ),
    );
  }
}
