import 'package:mdd/features/customer/data/models/customers_model.dart';
import 'package:mdd/features/user/data/models/role_model.dart';
import 'package:mdd/utils/enums.dart';

class UserEntity {
  UserEntity({
    required this.userID,
    required this.fullName,
    required this.email,
    required this.userName,
    this.password,
    this.lastPasswordChage,
    this.isMfaEnabled,
    this.mfaCode,
    this.isActive,
    this.userType,
    required this.token,
    this.tokenExpireDate,
    this.roleType,
    this.customer,
    this.roles,
  });

  String? userID;
  String? fullName;
  String? email;
  String? userName;
  String? password;
  String? lastPasswordChage;
  bool? isMfaEnabled;
  String? mfaCode;
  bool? isActive;
  UserType? userType;
  String? token;
  String? tokenExpireDate;
  String? roleType;
  CustomersModel? customer;
  List<RoleModel>? roles;
}
