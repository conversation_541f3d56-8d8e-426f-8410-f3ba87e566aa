import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/features/organization/data/models/organizations_model.dart';
import 'package:mdd/features/organization/presentation/provider/organization_details_provider.dart';
import 'package:mdd/features/user/domain/entities/user_entity.dart';
import 'package:mdd/features/user/presentation/provider/user_provider.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/utils/constants/constants.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/helper_functions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';
import 'package:package_info_plus/package_info_plus.dart';

class DrawerView extends ConsumerStatefulWidget {
  const DrawerView({super.key});

  @override
  ConsumerState createState() => _DrawerViewState();
}

class _DrawerViewState extends ConsumerState<DrawerView> {
  CustomerType? _customerType;

  @override
  void initState() {
    _customerType = (ref.read(userProvider) as LoadedViewState<UserEntity>)
        .data
        .customer
        ?.type;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(orgDetailsProvider, (previous, state) async {
      if (state is LoadingViewState) {
        UiHelper.showLoadingDialog(context);
      }
      if (state is LoadedViewState<OrgsModel>) {
        Navigator.pop(context);
        Scaffold.of(context).closeDrawer();
        context.pushRoute(UpdateOrgRoute(org: state.data));
      } else if (state is ErrorViewState) {
        await context.popRoute();
        UiHelper.showNotification(state.errorMessage);
      }
    });

    return Drawer(
      child: SafeArea(
        child: Column(
          children: [
            DrawerHeader(
              child: SvgPicture.asset(
                'assets/icons/mdd-logo.svg',
                height: context.heightR(0.05),
              ),
            ),
            // _DrawerItemWidget(
            //   title: 'orders'.tr(),
            //   iconPath: 'assets/icons/orders_icon.svg',
            //   onTap: () {
            //     Scaffold.of(context).closeDrawer();
            //     ref
            //         .read(bottomBarIndex.notifier)
            //         .setActiveIndex(BottomNavBarDestination.orders.index);
            //   },
            // ),
            // _DrawerItemWidget(
            //   title: 'quotations'.tr(),
            //   iconPath: 'assets/icons/offers_icon.svg',
            //   onTap: () {
            //     Scaffold.of(context).closeDrawer();
            //     ref
            //         .read(bottomBarIndex.notifier)
            //         .setActiveIndex(BottomNavBarDestination.offers.index);
            //   },
            // ),
            // _DrawerItemWidget(
            //   title: 'invoices'.tr(),
            //   iconPath: 'assets/icons/invoices_icon.svg',
            //   onTap: () {
            //     Scaffold.of(context).closeDrawer();
            //     ref
            //         .read(bottomBarIndex.notifier)
            //         .setActiveIndex(BottomNavBarDestination.invoices.index);
            //   },
            // ),
            // _DrawerItemWidget(
            //   title: 'statistics'.tr(),
            //   iconPath: 'assets/icons/statistics_icon.svg',
            //   onTap: () {
            //     Scaffold.of(context).closeDrawer();
            //     ref
            //         .read(bottomBarIndex.notifier)
            //         .setActiveIndex(BottomNavBarDestination.statistics.index);
            //   },
            // ),
            Consumer(builder: (context, ref, child) {
              final orgState = ref.watch(orgDetailsProvider);
              if (orgState is LoadingViewState) {
                return const SizedBox();
              }
              if (orgState is EmptyViewState) {
                return const SizedBox();
              }
              if (orgState is ErrorViewState) {
                return const SizedBox();
              }
              if (orgState is LoadedViewState<OrgsModel>) {
                final isApproved =
                    orgState.data.status == OrganizationStatus.aprroved;
                return Column(
                  children: [
                    if (isApproved)
                      _DrawerItemWidget(
                        title: 'payments'.tr(),
                        iconPath: 'assets/icons/payments_icon.svg',
                        onTap: () {
                          Scaffold.of(context).closeDrawer();
                          context.pushRoute(const PaymentsRoute());
                        },
                      ),
                    if (isApproved)
                      _DrawerItemWidget(
                        title: 'projects'.tr(),
                        iconPath: 'assets/icons/project_icon.svg',
                        onTap: () {
                          Scaffold.of(context).closeDrawer();
                          context.pushRoute(const ProjectsRoute());
                        },
                      ),
                    if (_customerType == CustomerType.fullAuthority &&
                        isApproved)
                      _DrawerItemWidget(
                        title: 'customers'.tr(),
                        iconPath: 'assets/icons/customers_icon.svg',
                        onTap: () {
                          Scaffold.of(context).closeDrawer();
                          context.pushRoute(const CustomersRoute());
                        },
                      ),
                    _DrawerItemWidget(
                      title: 'organizationProfile'.tr(),
                      iconPath: 'assets/icons/clients_icon.svg',
                      onTap: () {
                        Scaffold.of(context).closeDrawer();
                        context.pushRoute(UpdateOrgRoute(org: orgState.data));
                      },
                    ),
                  ],
                );
              }
              return const SizedBox();
            }),
            _DrawerItemWidget(
              title: 'settings'.tr(),
              iconPath: 'assets/icons/settings_icon.svg',
              onTap: () {
                Scaffold.of(context).closeDrawer();
                context.pushRoute(const SettingsRoute());
              },
            ),
            _DrawerItemWidget(
              title: 'terms_and_conditions'.tr(),
              iconPath: 'assets/icons/privacy_icon.svg',
              onTap: () {
                Scaffold.of(context).closeDrawer();
                HelperFunctions.openLink(AppConstants.kPrivacyPolicy);
              },
            ),
            Expanded(
              child: FutureBuilder<PackageInfo>(
                future: PackageInfo.fromPlatform(),
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    return Align(
                      alignment: Alignment.bottomCenter,
                      child: Text('V.${snapshot.data?.version}'),
                    );
                  }
                  return const SizedBox();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _DrawerItemWidget extends StatelessWidget {
  final String title;
  final String iconPath;
  final VoidCallback onTap;

  const _DrawerItemWidget({
    required this.title,
    required this.iconPath,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(title),
      horizontalTitleGap: 0,
      leading: SvgPicture.asset(
        iconPath,
        colorFilter: const ColorFilter.mode(AppColors.grey2, BlendMode.srcIn),
      ),
      onTap: onTap,
    );
  }
}
