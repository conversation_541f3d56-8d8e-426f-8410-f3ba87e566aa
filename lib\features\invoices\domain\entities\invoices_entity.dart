import 'package:mdd/core/models/paginated_model.dart';
import 'package:mdd/utils/enums.dart';

class InvoicesEntity implements PaginatedModel {
  double? amount;
  String? invoiceID;
  String? orderID;
  double? paidAmount;
  double? pendingAmount;
  String? createOn;
  String? dueDate;
  int? orderNo;
  String? employeeName;
  String? invoicePath;
  InvoicesStatusType? status;
  String? statusName;

  InvoicesEntity({
    required this.amount,
    required this.paidAmount,
    required this.pendingAmount,
    required this.createOn,
    required this.dueDate,
    required this.orderNo,
    required this.employeeName,
    required this.invoicePath,
    required this.status,
    required this.statusName,
    required this.orderID,
    required this.invoiceID,
  });
}
