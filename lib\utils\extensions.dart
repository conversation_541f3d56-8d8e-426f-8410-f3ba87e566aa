import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart' as intl;

/// *** Extensions on BuildContext class ***
extension SugarExt on BuildContext {
// Widgets Extensions
  void showAlertDialog(Widget widget) =>
      showDialog(context: this, builder: (context) => widget);

  // MediaQuery
  MediaQueryData get mediaQuery => MediaQuery.of(this);
  //* Dimensions Extensions
  double get height => mediaQuery.size.height;
  double get width => mediaQuery.size.width;
  double heightR(double value) => mediaQuery.size.height * value;
  double widthR(double value) => mediaQuery.size.width * value;
  double defaultSize() {
    if (mediaQuery.orientation == Orientation.landscape) {
      return height * 0.024;
    } else {
      return width * 0.024;
    }
  }

  bool get isScreenReaderActive => mediaQuery.accessibleNavigation;

  // Device Breakpoints
  bool get isMobile => width <= 600;
  bool get isTablet => width > 600;
  bool get isDesktop => width > 950;
  bool get isLargeMobile => isMobile && height > 800;
  bool get isSmallMobile => isMobile && height < 550;

  // Locale Extensions
  bool get isRTL =>
      intl.Bidi.isRtlLanguage(Localizations.localeOf(this).languageCode);

  // Theme Extensions
  ThemeData get theme => Theme.of(this);
  TextTheme get textTheme => theme.textTheme;
  ColorScheme get colorScheme => theme.colorScheme;

  // Request UnFocus
  void unFocusRequest() {
    FocusManager.instance.primaryFocus?.unfocus();
  }
}

/// *** Extensions on Color class ***
extension HexColor on Color {
  /// String is in the format "aabbcc" or "ffaabbcc" with an optional leading "#".
  static Color fromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }
}

// *** Extensions on String class ***
extension StringExtensions on String {
  String groupingSeparator() {
    final number = double.tryParse(this) ?? 0.0;
    final formatter = NumberFormat("###.0#", 'en_US');
    return formatter.format(number);
  }
}

extension PriceExtensions on double {
  String toFormattedPrice() {
    if (this == 0.0) {
      return '0'; // return '0' instead of formatting as a decimal
    } else {
      final formatter = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
      String formattedPrice = toStringAsFixed(2)
          .replaceAllMapped(formatter, (match) => '${match[1]},');
      if (formattedPrice.endsWith('.00')) {
        // remove the decimal and the trailing zeros
        formattedPrice = formattedPrice.replaceAll('.00', '');
      }
      return formattedPrice;
    }
  }
}

extension IntExtensions on int {
  String numberFormatting() {
    return intl.NumberFormat.decimalPattern().format(this);
  }
}

extension ArabicNumberConverter on String {
  String arabicNumberConverter() {
    const arabic = [
      '٠',
      '١',
      '٢',
      '٣',
      '٤',
      '٥',
      '٦',
      '٧',
      '٨',
      '٩',
    ];

    const english = [
      '0',
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
    ];
    final output = StringBuffer();

    for (final rune in runes) {
      final char = String.fromCharCode(rune);
      if (english.contains(char) || char == '.') {
        output.write(char);
        continue;
      }
      final newNumber = arabic.indexOf(char);
      output.write(newNumber.toString());
    }
    return output.toString();
  }
}
