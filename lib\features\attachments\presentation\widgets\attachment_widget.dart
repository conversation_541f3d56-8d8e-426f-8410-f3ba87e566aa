import 'package:flutter/material.dart';
import 'package:mdd/core/widgets/rounded_button_widget.dart';
import 'package:mdd/core/widgets/subtitle_widget.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';

class AttachmentWidget extends StatelessWidget {
  final VoidCallback? onPressed;
  final String title;
  final String buttonTitle;

  const AttachmentWidget({
    super.key,
    required this.onPressed,
    required this.title,
    required this.buttonTitle,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Card(
        margin: const EdgeInsets.symmetric(
          horizontal: AppDimensions.kSizeMedium,
          vertical: AppDimensions.kSizeMedium,
        ),
        color: AppColors.cardDetailsBackground,
        elevation: 0,
        child: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: AppDimensions.kSizeXSmall2,
            horizontal: AppDimensions.kSizeMedium,
          ),
          child: ListTile(
            contentPadding: EdgeInsets.zero,
            dense: true,
            title: SubtitleWidget(
              title,
              overflow: TextOverflow.ellipsis,
            ),
            trailing: RoundedButtonWidget(
              onPressed: onPressed,
              title: buttonTitle,
              disabledColor: AppColors.white,
            ),
          ),
        ),
      ),
    );
  }
}
