import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/data/repositories/quotations_repository_impl.dart';
import 'package:mdd/features/quotations/domain/repositories/quotations_repository.dart';

final quotationDetailsUseCaseProvider = Provider<QuotationDetails>(
    (ref) => QuotationDetails(ref.watch(quotationsRepositoryImpl)));

class QuotationDetails
    implements UseCase<QuotationsModel, QuotationDetailsParams> {
  final QuotationsRepository _quotationsRepository;

  QuotationDetails(this._quotationsRepository);

  @override
  Future<Either<Failure, QuotationsModel>> call(
      QuotationDetailsParams params) async {
    return await _quotationsRepository.getQuotationById(params);
  }
}

class QuotationDetailsParams {
  final String quotationID;
  const QuotationDetailsParams({
    required this.quotationID,
  });
}
