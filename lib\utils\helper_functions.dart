import 'package:intl/intl.dart';
import 'package:mdd/utils/constants/constants.dart';
import 'package:path/path.dart' as path;
import 'package:timeago/timeago.dart' as timeago;
import 'package:url_launcher/url_launcher.dart';
import 'package:uuid/uuid.dart';

class HelperFunctions {
  static String formatDate(String? date) {
    return date == null
        ? ''
        : DateFormat('yyyy/MM/dd').format(DateTime.parse(date));
  }

  static String generateId() {
    return const Uuid().v1().toLowerCase();
  }

  static bool isAttachmentAllowed(int size) {
    return size <= AppConstants.maxFileSize;
  }

  static String getTimeAgo(String date, String locale,
      {bool addTimeZone = false}) {
    timeago.setLocaleMessages(
        locale, locale == "ar" ? timeago.ArMessages() : timeago.EnMessages());
    DateTime dateTime = DateTime.parse(date);
    return timeago.format(
        addTimeZone ? dateTime.add(dateTime.timeZoneOffset) : dateTime,
        locale: locale,
        allowFromNow: false);
  }

  static bool hasExtension(String fileName) {
    final String ext = path.extension(fileName);
    return ext.isNotEmpty;
  }

  static Future<void> openLink(String? url) async {
    if (url == null) return;
    final Uri launchUri = Uri.parse(url);

    if (!await launchUrl(launchUri)) throw 'Could not launch link';
  }
}
