// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quotations_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuotationsModel _$QuotationsModelFromJson(Map<String, dynamic> json) =>
    QuotationsModel(
          quotationID: json['quotationID'] as String,
          quotationNo: json['quotationNo'] as String,
          orderID: json['orderID'] as String,
          orderNo: json['orderNo'] as int?,
          customerPurchaseOrderNo: json['customerPurchaseOrderNo'] as String?,
          fileName: json['fileName'] as String?,
          fileContent: json['fileContent'] as String?,
          fileContentType: json['fileContentType'] as String?,
          expiredDate: json['expiredDate'] as String?,
          submitDate: json['submitDate'] as String?,
          mustAprroveAll: json['mustAprroveAll'] as bool?,
          rejectDate: json['rejectDate'] as String?,
          rejectNotes: json['rejectNotes'] as String?,
          price: (json['price'] as num).toDouble(),
          priceWithNoVAT: (json['priceWithNoVAT'] as num).toDouble(),
          vat: (json['vat'] as num?)?.toDouble(),
          vatPer: (json['vatPer'] as num?)?.toDouble(),
          serviceFees: (json['serviceFees'] as num?)?.toDouble(),
          serviceFeesCalculated:
          (json['serviceFeesCalculated'] as num?)?.toDouble(),
          serviceFeesDiscount: (json['serviceFeesDiscount'] as num?)?.toDouble(),
          initialApproval: json['initialApproval'] as bool?,
          additionalFees: (json['additionalFees'] as num?)?.toDouble(),
          deliveryFees: (json['deliveryFees'] as num?)?.toDouble(),
          deliverTo: json['deliverTo'] as String?,
          location: json['location'] as String?,
          mobileNo: json['mobileNo'] as String?,
          total: (json['total'] as num).toDouble(),
          rejectReasonName: json['rejectReasonName'] as String?,
          quotationStatus: $enumDecodeNullable(
              _$QuotationsStatusTypeEnumMap, json['quotationStatus']),
          statusName: json['statusName'] as String?,
          employeeName: json['employeeName'] as String?,
          employeeMobile: json['employeeMobile'] as String?,
          organizationID: json['organizationID'] as String?,
          items: (json['items'] as List<dynamic>?)
              ?.map(
                  (e) => QuotationDetailsModel.fromJson(e as Map<String, dynamic>))
              .toList(),
          currentApprover: json['currentApprover'] as String?,
          approvals: (json['approvals'] as List<dynamic>?)
              ?.map((e) => ApprovalModel.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$QuotationsModelToJson(QuotationsModel instance) =>
    <String, dynamic>{
          'quotationID': instance.quotationID,
          'orderID': instance.orderID,
          'customerPurchaseOrderNo': instance.customerPurchaseOrderNo,
          'deliverTo': instance.deliverTo,
          'location': instance.location,
          'mobileNo': instance.mobileNo,
          'rejectNotes': instance.rejectNotes,
          'total': instance.total,
          'price': instance.price,
          'priceWithNoVAT': instance.priceWithNoVAT,
          'vat': instance.vat,
          'vatPer': instance.vatPer,
          'serviceFees': instance.serviceFees,
          'serviceFeesCalculated': instance.serviceFeesCalculated,
          'serviceFeesDiscount': instance.serviceFeesDiscount,
          'additionalFees': instance.additionalFees,
          'deliveryFees': instance.deliveryFees,
          'quotationNo': instance.quotationNo,
          'orderNo': instance.orderNo,
          'quotationStatus':
          _$QuotationsStatusTypeEnumMap[instance.quotationStatus],
          'statusName': instance.statusName,
          'expiredDate': instance.expiredDate,
          'submitDate': instance.submitDate,
          'fileName': instance.fileName,
          'fileContent': instance.fileContent,
          'fileContentType': instance.fileContentType,
          'employeeName': instance.employeeName,
          'employeeMobile': instance.employeeMobile,
          'organizationID': instance.organizationID,
          'currentApprover': instance.currentApprover,
          'initialApproval': instance.initialApproval,
          'mustAprroveAll': instance.mustAprroveAll,
          'approvals': instance.approvals,
          'rejectReasonName': instance.rejectReasonName,
          'rejectDate': instance.rejectDate,
          'items': instance.items,
    };

const _$QuotationsStatusTypeEnumMap = {
      QuotationsStatusType.created: 0,
      QuotationsStatusType.submitted: 1,
      QuotationsStatusType.accepted: 2,
      QuotationsStatusType.rejected: 3,
      QuotationsStatusType.expired: 4,
      QuotationsStatusType.canceled: 5,
};
