import 'package:flutter/material.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';

class ButtonWidget extends StatelessWidget {
  final VoidCallback? onPressed;
  final String title;
  final Color titleColor;
  final Color disabledColor;
  final Color backgroundColor;
  final Color borderColor;
  final Color disabledBackground;
  final double shapeRadius;
  final double horizontalPadding;
  const ButtonWidget({
    super.key,
    required this.onPressed,
    required this.title,
    this.titleColor = AppColors.white,
    this.disabledColor = AppColors.disabledTitleColor1,
    this.backgroundColor = AppColors.primary,
    this.borderColor = Colors.transparent,
    this.disabledBackground = AppColors.cardDetailsBackground,
    this.shapeRadius = AppDimensions.kSmallRadius,
    this.horizontalPadding = AppDimensions.kSizeLarge,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ButtonStyle(
        elevation: WidgetStateProperty.all(0),
        padding: WidgetStateProperty.all(
            EdgeInsets.symmetric(horizontal: horizontalPadding)),
        backgroundColor: WidgetStateProperty.resolveWith<Color>(
          (Set<WidgetState> states) {
            if (states.contains(WidgetState.pressed)) {
              return backgroundColor.withOpacity(0.7);
            } else if (states.contains(WidgetState.disabled)) {
              return disabledBackground;
            }
            return backgroundColor;
          },
        ),
        shape: WidgetStateProperty.resolveWith<RoundedRectangleBorder>(
          (Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return RoundedRectangleBorder(
                  side: const BorderSide(color: Colors.transparent),
                  borderRadius: BorderRadius.circular(shapeRadius));
            }
            return RoundedRectangleBorder(
                side: BorderSide(color: borderColor),
                borderRadius: BorderRadius.circular(shapeRadius));
          },
        ),
      ),
      child: TextWidget(
        title,
        color: onPressed == null ? disabledColor : titleColor,
      ),
    );
  }
}
