import 'package:mdd/utils/enums.dart';

class NotificationsEntity {
  final String notificationID;
  final String? objectID;
  final int notificationTemplateID;
  final String? title;
  final String? content;
  final String? type;
  final String? createdOn;
  final String? userID;
  final String? userName;
  final UserType? userType;
  final bool isEmailSent;
  final bool isPushSent;
  final bool isSMSSent;
  final bool isEmail;
  final bool isSMS;
  final bool isPush;
  final bool isViewed;
  final bool isSent;
  final String? email;
  final String? mobileNo;
  final String? language;

  NotificationsEntity(
      {required this.notificationID,
      required this.objectID,
      required this.notificationTemplateID,
      required this.title,
      required this.content,
      required this.type,
      required this.createdOn,
      required this.userID,
      required this.userName,
      required this.userType,
      required this.isEmailSent,
      required this.isPushSent,
      required this.isSMSSent,
      required this.isEmail,
      required this.isSMS,
      required this.isPush,
      required this.isViewed,
      required this.isSent,
      this.email,
      required this.mobileNo,
      required this.language});
}
