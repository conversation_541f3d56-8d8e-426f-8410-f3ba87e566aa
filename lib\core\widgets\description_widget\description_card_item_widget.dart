import 'package:flutter/material.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';

class DescriptionCardItemWidget extends StatelessWidget {
  final String title;
  final String trailing;
  const DescriptionCardItemWidget(
      {super.key, required this.title, required this.trailing});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.kSizeMedium,
        vertical: AppDimensions.kSizeSmall,
      ),
      child: Row(
        children: [
          Expanded(
            child: TextWidget(
              title,
              color: AppColors.listTileTitleTextGrey,
              fontSize: AppDimensions.kSizeMedium2,
            ),
          ),
          TextWidget(
            trailing,
            color: AppColors.listTileTrailingTextGrey,
            fontSize: AppDimensions.kSizeMedium2,
          ),
        ],
      ),
    );
  }
}
