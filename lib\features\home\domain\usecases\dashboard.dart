import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/home/<USER>/repositories/dashboard_impl.dart';
import 'package:mdd/features/home/<USER>/entities/dashboard_entity.dart';
import 'package:mdd/features/home/<USER>/repositories/dashboard_repository.dart';

final dashboardUserCaseProvider =
    Provider<Dashboard>((ref) => Dashboard(ref.watch(dashboardRepositoryImpl)));

class Dashboard implements UseCase<DashboardEntity, DashboardParams> {
  final DashboardRepository dashboardRepository;

  Dashboard(this.dashboardRepository);

  @override
  Future<Either<Failure, DashboardEntity>> call(DashboardParams params) async {
    return await dashboardRepository.getUserDashboard(params);
  }
}

class DashboardParams {
  final String startDate;
  final String endDate;

  DashboardParams({required this.startDate, required this.endDate});
}
