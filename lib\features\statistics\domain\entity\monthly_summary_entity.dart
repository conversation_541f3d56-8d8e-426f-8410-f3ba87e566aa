class MonthlySummaryEntity {
  final String? orgName;
  final String monthName;
  final String? supplychainer;
  final int ordersCount;
  final double ordersAmount;
  final double ordersProfits;
  final int directOrdersCount;
  final double directOrdersAmount;
  final double directOrdersProfits;
  final int tenderOrdersCount;
  final double tenderOrdersAmount;
  final double tenderOrdersProfits;

  MonthlySummaryEntity({
    required this.orgName,
    required this.monthName,
    required this.supplychainer,
    required this.ordersCount,
    required this.ordersAmount,
    required this.ordersProfits,
    required this.directOrdersCount,
    required this.directOrdersAmount,
    required this.directOrdersProfits,
    required this.tenderOrdersCount,
    required this.tenderOrdersAmount,
    required this.tenderOrdersProfits,
  });
}
