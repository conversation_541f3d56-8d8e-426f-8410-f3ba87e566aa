import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/home/<USER>/entities/dashboard_entity.dart';
import 'package:mdd/features/home/<USER>/usecases/dashboard.dart';
import 'package:mdd/utils/constants/constants.dart';

final dashboardProvider = StateNotifierProvider<DashboardProvider, ViewState>(
    (ref) => DashboardProvider(
          ref.watch(dashboardUserCaseProvider),
        ));

class DashboardProvider extends BaseProvider<DashboardEntity> {
  final Dashboard _dashboard;
  DashboardProvider(this._dashboard);

  Future<void> fetchDashboard() async {
    setLoadingState();
    final response = await _dashboard.call(
      DashboardParams(
        startDate: AppConstants.kStartDate,
        endDate: AppConstants.kEndDate,
      ),
    );
    response.fold((failure) {
      setErrorState(failure.message);
    }, (dashboard) {
      setLoadedState(dashboard);
    });
  }
}
