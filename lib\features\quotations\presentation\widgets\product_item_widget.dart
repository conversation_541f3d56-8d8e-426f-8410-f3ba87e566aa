import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';

class ProductItemWidget extends StatelessWidget {
  final String productName;
  final String price;
  final String unitName;
  final String qty;
  final bool isAccepted;
  final void Function(bool?)? onChange;

  const ProductItemWidget({
    super.key,
    required this.productName,
    required this.price,
    required this.unitName,
    required this.qty,
    this.isAccepted = false,
    this.onChange,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      color: AppColors.cardDetailsBackground,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(
            height: AppDimensions.kSizeMedium,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                width: AppDimensions.kSizeMedium,
              ),
              Expanded(
                child: TextWidget(
                  productName,
                  color: AppColors.tabBackgroundColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(
                width: AppDimensions.kSizeMedium,
              ),
              // Card(
              //   color: AppColors.tabBackgroundColor,
              //   child: Padding(
              //     padding: const EdgeInsets.symmetric(
              //       horizontal: AppDimensions.kSizeSmall,
              //       vertical: AppDimensions.kSizeXSmall / 2,
              //     ),
              //     child: TextWidget(
              //       qty,
              //       color: AppColors.white,
              //     ),
              //   ),
              // ),
              Card(
                color: AppColors.purple,
                margin: EdgeInsets.zero,
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: AppDimensions.kSizeSmall,
                    horizontal: AppDimensions.kSizeMedium,
                  ),
                  child: TextWidget(
                    '$qty x $unitName',
                    color: AppColors.white,
                  ),
                ),
              ),
              const SizedBox(
                width: AppDimensions.kSizeMedium,
              ),
            ],
          ),
          const SizedBox(
            height: AppDimensions.kSizeXXLarge,
          ),
          Row(
            children: [
              if (onChange != null) ...[
                Checkbox(
                  shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.circular(AppDimensions.kSmallRadius)),
                  value: isAccepted,
                  onChanged: onChange,
                ),
                TextWidget(
                  'accept'.tr(),
                  color: isAccepted ? AppColors.primary : null,
                ),
              ],
              const Spacer(),
              TextWidget(
                '${'price'.tr()} :',
                color: AppColors.grey2,
              ),
              TextWidget(price),
              const SizedBox(
                width: AppDimensions.kSizeMedium,
              ),
            ],
          ),
          const SizedBox(
            height: AppDimensions.kSizeLarge,
          ),
        ],
      ),
    );
  }
}
