import 'package:auto_route/annotations.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/notifications/domain/entities/notifications_entity.dart';
import 'package:mdd/features/notifications/presentation/provider/notifications_provider.dart';
import 'package:mdd/features/notifications/presentation/screens/notifications_view.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

@RoutePage()
class NotificationsScreen extends ConsumerStatefulWidget {
  const NotificationsScreen({
    super.key,
  });

  @override
  ConsumerState createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends ConsumerState<NotificationsScreen> {
  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      _fetch();
    });
    super.initState();
  }

  _fetch() {
    ref.read(notificationsProvider.notifier).getNotifications();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: const MainAppBar(),
        body: Column(
          children: [
            Row(
              children: [
                Expanded(child: DetailsCustomBar(title: 'notifications'.tr())),
                Consumer(builder: (context, ref, child) {
                  final state = ref.watch(notificationsProvider);
                  if (state is LoadedViewState<List<NotificationsEntity>>) {
                    return Card(
                      color: AppColors.primary,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppDimensions.kSizeSmall,
                        ),
                        child: TextWidget(
                          '${state.data.length}',
                          color: AppColors.white,
                        ),
                      ),
                    );
                  }
                  return const SizedBox();
                }),
              ],
            ),
            const NotificationsView(),
          ],
        ));
  }
}
