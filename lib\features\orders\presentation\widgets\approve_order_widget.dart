import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/form_field_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/core/widgets/title_widget.dart';
import 'package:mdd/features/orders/domain/usecases/approve_order.dart';
import 'package:mdd/features/orders/presentation/provider/approve_order_provider.dart';
import 'package:mdd/features/orders/presentation/provider/order_details_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class ApproveOrderWidget extends ConsumerStatefulWidget {
  const ApproveOrderWidget({
    super.key,
    required this.status,
    required this.orderID,
  });

  final String orderID;
  final int status;

  @override
  ConsumerState createState() => _ApproveOrderWidgetState();
}

class _ApproveOrderWidgetState extends ConsumerState<ApproveOrderWidget> {
  late final TextEditingController _notesController;

  @override
  void initState() {
    _notesController = TextEditingController();
    super.initState();
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(approveOrderProvider, (previous, state) {
      if (state is LoadingViewState) {
        UiHelper.showLoadingDialog(context);
      }
      if (state is LoadedViewState<ResultModel>) {
        Navigator.pop(context);
        Navigator.pop(context);
        UiHelper.showNotification(
          widget.status == 1
              ? 'approve_order_success'.tr()
              : 'reject_order_success'.tr(),
          notificationType: NotificationType.success,
        );
        ref
            .read(orderDetailsProvider.notifier)
            .fetchGetOrderById(widget.orderID);
      }
      if (state is ErrorViewState) {
        context.maybePop();
        UiHelper.showNotification(state.errorMessage);
      }
    });
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.kSizeLarge),
            child: TitleWidget(
              widget.status == 1 ? 'approve'.tr() : 'reject'.tr(),
              color: AppColors.disabledColor1,
              fontSize: AppDimensions.kSizeLarge2,
              fontWeight: FontWeight.bold,
            ),
          ),
          FormFieldWidget(
            controller: _notesController,
            borderColor: AppColors.cardDetailsBackground,
            fillColor: AppColors.cardDetailsBackground,
            hintText: 'notes'.tr(),
            textInputType: TextInputType.text,
            maxLines: 5,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.kSizeLarge,
              vertical: AppDimensions.kSizeLarge,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => context.maybePop(),
                  child: TextWidget('cancel'.tr()),
                ),
                ButtonWidget(
                  onPressed: () {
                    ref.read(approveOrderProvider.notifier).approveOrder(
                          ApproveOrderParams(
                            orderID: widget.orderID,
                            status: widget.status,
                            notes: _notesController.text,
                          ),
                        );
                  },
                  title: widget.status == 1 ? 'approve'.tr() : 'reject'.tr(),
                  backgroundColor:
                      widget.status == 1 ? AppColors.primary : AppColors.red,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
