import 'package:mdd/features/statistics/data/models/monthly_summary_model.dart';
import 'package:mdd/features/statistics/data/models/order_statuses_model.dart';
import 'package:mdd/features/statistics/data/models/quotation_statuses_model.dart';

class StatisticsEntity {
  final List<QuotationStatusesModel>? quotationStatuses;
  final List<OrderStatusesModel>? orderStatuses;
  final List<MonthlySummaryModel>? monthlySummary;

  StatisticsEntity(
      {required this.quotationStatuses,
      required this.orderStatuses,
      required this.monthlySummary});
}
