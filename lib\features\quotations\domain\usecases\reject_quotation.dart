import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/data/repositories/quotations_repository_impl.dart';
import 'package:mdd/features/quotations/domain/repositories/quotations_repository.dart';

final rejectQuotationUseCaseProvider = Provider<RejectQuotation>(
    (ref) => RejectQuotation(ref.watch(quotationsRepositoryImpl)));

class RejectQuotation implements UseCase<ResultModel, RejectQuotationParams> {
  final QuotationsRepository _quotationsRepository;

  RejectQuotation(this._quotationsRepository);

  @override
  Future<Either<Failure, ResultModel>> call(
      RejectQuotationParams params) async {
    return await _quotationsRepository.rejectQuotation(params);
  }
}

class RejectQuotationParams {
  final QuotationsModel model;

  const RejectQuotationParams(this.model);
}
