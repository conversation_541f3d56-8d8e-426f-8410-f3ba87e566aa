import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/customer/domain/usecases/create_new_customer.dart';

final createNewCustomerProvider =
    StateNotifierProvider.autoDispose<CreateNewCustomerProvider, ViewState>(
        (ref) {
  return CreateNewCustomerProvider(
    ref.watch(createNewCustomerUseCaseProvider),
  );
});

class CreateNewCustomerProvider extends BaseProvider<ResultModel> {
  final CreateNewCustomer _createNewCustomer;

  CreateNewCustomerProvider(this._createNewCustomer);

  Future<void> fetchCreateNewCustomer(CreateCustomerParams params) async {
    setLoadingState();
    final res = await _createNewCustomer.call(params);
    res.fold((failure) {
      setErrorState(failure.message);
    }, (data) {
      setLoadedState(data);
    });
  }
}
