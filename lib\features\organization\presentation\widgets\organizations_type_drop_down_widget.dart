// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:mdd/core/widgets/drop_down_widget.dart';
// import 'package:mdd/features/organization/presentation/provider/selected_organization_type_provider.dart';
// import 'package:mdd/theme/colors.dart';
// import 'package:mdd/theme/dimensions.dart';
// import 'package:mdd/utils/enums.dart';
//
// class OrgsTypeDropDownWidget extends ConsumerWidget {
//   const OrgsTypeDropDownWidget({
//     super.key,
//     this.value,
//   });
//
//   final OrganizationType? value;
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(
//         vertical: AppDimensions.kSizeSmall,
//         horizontal: AppDimensions.kSizeMedium,
//       ),
//       child: DropDownWidget<OrganizationType>(
//         title: 'organization_type'.tr(),
//         validatorMessage: 'organization_type_validate'.tr(),
//         onChanged: (orgType) {
//           ref.watch(selectedOrgTypeProvider.notifier).state = orgType;
//         },
//         value: value,
//         borderColor: AppColors.cardDetailsBackground,
//         itemAsString: (types) => types.translatedName,
//         items: OrganizationType.values,
//       ),
//     );
//   }
// }
