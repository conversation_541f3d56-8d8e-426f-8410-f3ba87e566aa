import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/home/<USER>/entities/dashboard_entity.dart';

part 'dashboard_model.g.dart';

@JsonSerializable()
class DashboardModel extends DashboardEntity {
  String? startDate;
  String? endDate;
  bool? isCustomDates;
  String? todayClients;
  String? todayOrders;
  String? todayQuotations;
  String? todayOperations;
  String? todayProfits;
  String? todayCommissions;
  String? monthClients;
  String? monthOrders;
  String? monthQuotations;
  String? monthOperations;
  String? monthOperationsCount;
  String? monthProfits;
  String? monthCommissions;
  String? yearClients;
  String? yearOrders;
  String? yearQuotations;
  String? yearProfits;
  String? yearCommissions;
  String? yearSupport;
  String? invoicePending;
  String? invoicePendingAmount;
  String? invoicePaid;
  String? invoicePaidAmount;
  String? invoiceOverDue;
  String? invoiceOverDueAmount;
  String? paymentWaitting;
  String? paymentWaittingAmount;
  String? paymentPending;
  String? paymentPendingAmount;
  String? paymentApproved;
  String? ordersNew;
  String? ordersWaitingForAprroval;
  String? ordersDeliverProcess;
  String? ordersDeliverProcessForCustomer;
  String? ordersWaitingForPayment;
  String? ordersCompleted;
  String? ordersDeclient;
  String? delayedOrders;
  String? quotationsNew;
  String? quotationsSubmitted;
  String? quotationsAccepted;
  String? quotationsRejected;
  String? quotationsExpired;
  String? allCommissions;
  String? waitingCommissions;
  String? unPaidCommissions;
  String? paidCommissions;
  String? targetSuppliers;
  String? targetClients;
  String? targetProfits;
  String? targetOperations;
  String? targetOperationsCount;
  String? monthSuppliers;
  String? monthSuppliersPer;
  String? monthProfitsPer;
  String? monthOperationsPer;
  String? monthOperationsCountPer;
  String? monthClientsPer;
  String? ordersAmount;
  String? paymentsAmount;

  DashboardModel({
    this.startDate,
    this.endDate,
    this.isCustomDates,
    this.todayClients,
    this.todayOrders,
    this.todayQuotations,
    this.todayOperations,
    this.todayProfits,
    this.todayCommissions,
    this.monthClients,
    this.monthOrders,
    this.monthQuotations,
    this.monthOperations,
    this.monthOperationsCount,
    this.monthProfits,
    this.monthCommissions,
    this.yearClients,
    this.yearOrders,
    this.yearQuotations,
    this.yearProfits,
    this.yearCommissions,
    this.yearSupport,
    this.invoicePending,
    this.invoicePendingAmount,
    this.invoicePaid,
    this.invoicePaidAmount,
    this.invoiceOverDue,
    this.invoiceOverDueAmount,
    this.paymentWaitting,
    this.paymentWaittingAmount,
    this.paymentPending,
    this.paymentPendingAmount,
    this.paymentApproved,
    this.ordersNew,
    this.ordersWaitingForAprroval,
    this.ordersDeliverProcess,
    this.ordersDeliverProcessForCustomer,
    this.ordersWaitingForPayment,
    this.ordersCompleted,
    this.ordersDeclient,
    this.delayedOrders,
    this.quotationsNew,
    this.quotationsSubmitted,
    this.quotationsAccepted,
    this.quotationsRejected,
    this.quotationsExpired,
    this.allCommissions,
    this.waitingCommissions,
    this.unPaidCommissions,
    this.paidCommissions,
    this.targetSuppliers,
    this.targetClients,
    this.targetProfits,
    this.targetOperations,
    this.targetOperationsCount,
    this.monthSuppliers,
    this.monthSuppliersPer,
    this.monthProfitsPer,
    this.monthOperationsPer,
    this.monthOperationsCountPer,
    this.monthClientsPer,
    this.ordersAmount,
    this.paymentsAmount,
    required super.owedAmount,
    required super.unUsedBalance,
    required super.creditBalance,
    required super.yearOperations,
    required super.paymentApprovedAmount,
  });

  factory DashboardModel.fromJson(Map<String, dynamic> json) =>
      _$DashboardModelFromJson(json);

  Map<String, dynamic> toJson() => _$DashboardModelToJson(this);
}
