import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/payments/data/models/payments_model.dart';
import 'package:mdd/features/payments/domain/usecases/create_new_payment.dart';

final createNewPaymentProvider =
    StateNotifierProvider.autoDispose<CreateNewPaymentProvider, ViewState>(
  (ref) => CreateNewPaymentProvider(
    ref.watch(createNewPaymentUseCaseProvider),
  ),
);

class CreateNewPaymentProvider extends BaseProvider<ResultModel> {
  final CreateNewPayment _createNewPayment;

  CreateNewPaymentProvider(this._createNewPayment);

  Future<void> fetchCreateNewPayment(PaymentsModel model) async {
    setLoadingState();
    final res = await _createNewPayment.call(CreateNewPaymentParams(model));
    res.fold((failure) {
      setErrorState(failure.message);
    }, (data) {
      setLoadedState(data);
    });
  }
}
