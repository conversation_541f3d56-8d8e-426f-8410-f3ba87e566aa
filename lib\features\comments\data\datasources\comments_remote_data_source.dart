import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/endPoints/end_points.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/features/comments/data/models/comments_model.dart';
import 'package:mdd/features/comments/domain/usecases/add_comments.dart';
import 'package:mdd/features/comments/domain/usecases/order_comments.dart';
import 'package:mdd/services/dio_client.dart';

abstract class CommentsRemoteDataSource {
  Future<ResultModel> addComment(AddCommentsParams params);
  Future<List<CommentsModel>> orderComments(OrderCommentsParams params);
}

final commentsRemoteDataSourceImpl = Provider<CommentsRemoteDataSourceImpl>(
    (ref) => CommentsRemoteDataSourceImpl(ref.watch(dioClientProvider)));

class CommentsRemoteDataSourceImpl implements CommentsRemoteDataSource {
  final DioClient _dioClient;

  CommentsRemoteDataSourceImpl(this._dioClient);
  @override
  Future<ResultModel> addComment(AddCommentsParams params) async {
    final response = await _dioClient.dio.post(
      EndPoints.addOrderComments,
      data: {
        "IsInternal": false,
        "orderID": params.orderId,
        "conetent": params.content,
        "mentions": params.mentions,
      },
    );
    return ResultModel.fromJson(response.data);
  }

  @override
  Future<List<CommentsModel>> orderComments(OrderCommentsParams params) async {
    final response = await _dioClient.dio.get(
      EndPoints.commentOrders,
      queryParameters: {
        "id": params.orderId,
      },
    );
    return (response.data['data'] as List)
        .map((comment) => CommentsModel.fromJson(comment))
        .toList();
  }
}
