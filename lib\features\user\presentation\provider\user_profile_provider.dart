import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/customer/data/models/customers_model.dart';
import 'package:mdd/features/user/domain/usecases/profile.dart';

final enableEditingProfile = StateProvider.autoDispose<bool>((ref) => false);

final userProfileProvider =
    StateNotifierProvider<UserProfileProvider, ViewState>(
  (ref) => UserProfileProvider(ref.watch(profileUserCaseProvider)),
);

class UserProfileProvider extends BaseProvider<CustomersModel> {
  final Profile _profile;

  UserProfileProvider(this._profile);

  Future<void> fetchProfile() async {
    setLoadingState();
    final response = await _profile.call(NoParams());
    response.fold(
      (failure) {
        setErrorState(failure.message);
      },
      setLoadedState,
    );
  }
}
