PODS:
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/CoreOnly (10.28.0):
    - FirebaseCore (= 10.28.0)
  - Firebase/Messaging (10.28.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.28.0)
  - firebase_core (3.2.0):
    - Firebase/CoreOnly (= 10.28.0)
    - Flutter
  - firebase_messaging (15.0.3):
    - Firebase/Messaging (= 10.28.0)
    - firebase_core
    - Flutter
  - FirebaseCore (10.28.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.28.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - Flutter (1.0.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - geocoding_ios (1.0.5):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - GoogleMaps (< 10.0, >= 8.4)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - open_filex (0.0.2):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - SDWebImage (5.19.4):
    - SDWebImage/Core (= 5.19.4)
  - SDWebImage/Core (5.19.4)
  - Sentry/HybridSDK (8.30.1)
  - sentry_flutter (8.4.0):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 8.30.1)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - geocoding_ios (from `.symlinks/plugins/geocoding_ios/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - sentry_flutter (from `.symlinks/plugins/sentry_flutter/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleMaps
    - GoogleUtilities
    - nanopb
    - PromisesObjC
    - ReachabilitySwift
    - SDWebImage
    - Sentry
    - SwiftyGif

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  geocoding_ios:
    :path: ".symlinks/plugins/geocoding_ios/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  sentry_flutter:
    :path: ".symlinks/plugins/sentry_flutter/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  device_info_plus: 97af1d7e84681a90d0693e63169a5d50e0839a0d
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: 09aa5ec1ab24135ccd7a1621c46c84134bfd6655
  Firebase: 5121c624121af81cbc81df3bda414b3c28c4f3c3
  firebase_core: a9d0180d5285527884d07a41eb4a9ec9ed12cdb6
  firebase_messaging: ccc82a143a74de75f440a4e413dbbb37ec3fddbc
  FirebaseCore: 857dc1c6dd1255675047404d8466f7dfaac5d779
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 087a7c7cadef7b9239f005bc4db823894844f323
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_local_notifications: 4cde75091f6327eb8517fa068a0a5950212d2086
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  geocoding_ios: d7460f56e80e118d57678efe5c2cdc888739ff18
  geolocator_apple: 6cbaf322953988e009e5ecb481f07efece75c450
  google_maps_flutter_ios: 5bc2be60ad012e79b182ce0fb0ef5030a50fb03e
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  open_filex: 6e26e659846ec990262224a12ef1c528bb4edbe4
  package_info_plus: 58f0028419748fad15bf008b270aaa8e54380b1c
  path_provider_foundation: 3784922295ac71e43754bd15e0653ccfd36a147c
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  SDWebImage: 066c47b573f408f18caa467d71deace7c0f8280d
  Sentry: 514a3ea653886e9a48c6287d8b7bf05ec24bf3be
  sentry_flutter: edc037f7af0dc1512d6c33a5c2c7c838bd0d6806
  share_plus: 8875f4f2500512ea181eef553c3e27dba5135aad
  shared_preferences_foundation: b4c3b4cddf1c21f02770737f147a3f5da9d39695
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  url_launcher_ios: 6116280ddcfe98ab8820085d8d76ae7449447586

PODFILE CHECKSUM: dfef653c02a2982528ce523b07004d4676a76ce5

COCOAPODS: 1.15.2
