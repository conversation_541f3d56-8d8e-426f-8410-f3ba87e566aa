import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/endPoints/end_points.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/features/attachments/domain/entites/attachments_entity.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/domain/usecases/accept_approval_quotation.dart';
import 'package:mdd/features/quotations/domain/usecases/accept_quotation.dart';
import 'package:mdd/features/quotations/domain/usecases/pdf_quotation.dart';
import 'package:mdd/features/quotations/domain/usecases/quotation_details.dart';
import 'package:mdd/features/quotations/domain/usecases/quotations.dart';
import 'package:mdd/features/quotations/domain/usecases/reject_quotation.dart';
import 'package:mdd/services/dio_client.dart';
import 'package:mdd/utils/constants/constants.dart';

abstract class QuotationsRemoteDataSource {
  Future<List<QuotationsModel>> getQuotations(QuotationsParams params);
  Future<QuotationsModel> getQuotationById(QuotationDetailsParams params);
  Future<ResultModel> rejectQuotation(RejectQuotationParams params);
  Future<ResultModel> acceptQuotation(AcceptQuotationParams params);
  Future<ResultModel> acceptApprovalQuotation(
      AcceptApprovalQuotationParams params);
  Future<AttachmentsEntity> getPdfQuotation(PdfQuotationParams params);
}

final quotationsRemoteDataSourceImpl = Provider<QuotationsRemoteDataSourceImpl>(
    (ref) => QuotationsRemoteDataSourceImpl(ref.watch(dioClientProvider)));

class QuotationsRemoteDataSourceImpl implements QuotationsRemoteDataSource {
  final DioClient _dioClient;

  QuotationsRemoteDataSourceImpl(this._dioClient);
  @override
  Future<List<QuotationsModel>> getQuotations(QuotationsParams params) async {
    final response = await _dioClient.dio.get(
      EndPoints.quotations,
      queryParameters: {
        'limit': AppConstants.paginationLimit,
        'page': params.page,
        'status': params.quotationStatus,
        'find': params.quotationNo,
        'sort': "OrderNoDesc",
      },
    );
    return (response.data['data'] as List)
        .map((quotation) => QuotationsModel.fromJson(quotation))
        .toList();
  }

  @override
  Future<QuotationsModel> getQuotationById(
      QuotationDetailsParams params) async {
    final response = await _dioClient.dio.get(
      '${EndPoints.quotations}/${params.quotationID}',
    );

    final model = QuotationsModel.fromJson(response.data['data']);
    model.approvals?.sort((a, b) => a.priority.compareTo(b.priority));
    return model;
  }

  @override
  Future<ResultModel> rejectQuotation(RejectQuotationParams params) async {
    final response = await _dioClient.dio.post(
      EndPoints.rejectQuotations,
      data: params.model.toJson(),
    );

    return ResultModel.fromJson(response.data);
  }

  @override
  Future<ResultModel> acceptQuotation(AcceptQuotationParams params) async {
    final response = await _dioClient.dio.post(
      EndPoints.approveQuotations,
      data: params.model.toJson()..removeWhere((key, value) => value == null),
    );

    return ResultModel.fromJson(response.data);
  }

  @override
  Future<ResultModel> acceptApprovalQuotation(
      AcceptApprovalQuotationParams params) async {
    final response = await _dioClient.dio.post(
      EndPoints.approveQuotations,
      data: params.quotation,
    );
    return ResultModel.fromJson(response.data);
  }

  @override
  Future<AttachmentsEntity> getPdfQuotation(PdfQuotationParams params) async {
    final response = await _dioClient.dio.get(
      "${EndPoints.quotationPdf}/${params.id}",
    );
    return AttachmentsEntity(
      fileContent: response.data['data'],
      fileName: "Quotation-${params.id}.pdf",
      orderAttachmentId: params.id,
    );
  }
}
