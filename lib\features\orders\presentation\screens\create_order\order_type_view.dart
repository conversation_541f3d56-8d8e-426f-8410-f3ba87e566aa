import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/drop_down_widget.dart';
import 'package:mdd/features/orders/presentation/provider/selected_order_type_provider.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enum_extensions.dart';
import 'package:mdd/utils/enums.dart';

class OrderTypeView extends ConsumerWidget {
  const OrderTypeView({
    super.key,
    this.value,
  });

  final OrderTypes? value;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: AppDimensions.kSizeSmall,
        horizontal: AppDimensions.kSizeMedium,
      ),
      child: DropDownWidget<OrderTypes>(
        title: 'order_type'.tr(),
        validatorMessage: 'order_type_validate'.tr(),
        onChanged: (orderType) {
          ref.watch(selectedOrderTypeProvider.notifier).state = orderType;
        },
        itemAsString: (types) => types.translatedName,
        items: const [
          OrderTypes.directPO,
          OrderTypes.tender,
          OrderTypes.payBill,
        ],
        value: value,
      ),
    );
  }
}
