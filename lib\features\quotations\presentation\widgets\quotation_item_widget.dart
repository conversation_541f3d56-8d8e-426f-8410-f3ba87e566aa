import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/description_widget/card_item_widget.dart';
import 'package:mdd/core/widgets/description_widget/description_card_item_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/presentation/provider/pdf_quotation_provider.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/utils/enum_extensions.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/helper_functions.dart';

class QuotationItemWidget extends ConsumerWidget {
  final QuotationsModel quotation;
  final bool enableOnTap;
  const QuotationItemWidget(
      {super.key, required this.quotation, this.enableOnTap = true});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return InkWell(
      onTap: enableOnTap
          ? () {
              context.pushRoute(
                  QuotationDetailsRoute(quotationId: quotation.quotationID));
            }
          : null,
      child: CardItemsWidget(
        title: quotation.total.toString().groupingSeparator(),
        subtitle: quotation.orderNo?.toString() ?? '',
        image: 'assets/images/price_image.png',
        imageBackground: AppColors.yellowCard,
        statusName: quotation.quotationStatus?.translatedName ?? '',
        statusColor: quotation.quotationStatus?.statusColor,
        menuItems: [
          PopupMenuItem(
            value: 1,
            child: TextWidget('print'.tr()),
          ),
        ],
        onSelected: (value) {
          if (value == 1) {
            ref
                .read(pdfQuotationProvider.notifier)
                .getPdfQuotation(quotation.quotationID);
          }
        },
        descriptionWidget: [
          DescriptionCardItemWidget(
            title: 'quotation_number'.tr(),
            trailing: quotation.quotationNo ?? '',
          ),
          DescriptionCardItemWidget(
            title: 'sent_date'.tr(),
            trailing: quotation.submitDate != null
                ? HelperFunctions.formatDate(quotation.submitDate!)
                : '',
          ),
          DescriptionCardItemWidget(
            title: 'expired_date'.tr(),
            trailing: quotation.expiredDate != null
                ? HelperFunctions.formatDate(quotation.expiredDate!)
                : '',
          ),
        ],
      ),
    );
  }
}
