import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/features/nav_bar/presentation/screens/drawer_view.dart';
import 'package:mdd/features/nav_bar/presentation/widgets/bottom_nav_bar_widget.dart';
import 'package:mdd/features/organization/data/models/organizations_model.dart';
import 'package:mdd/features/organization/presentation/provider/organization_details_provider.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/utils/enums.dart';

@RoutePage()
class BottomNavBarScreen extends StatelessWidget {
  const BottomNavBarScreen({super.key});

  @override
  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final orgState = ref.watch(orgDetailsProvider);

        return AutoTabsScaffold(
          appBarBuilder: (context, tabsRouter) => const MainAppBar(
            showDrawer: true,
          ),
          drawer: const DrawerView(),
          routes: const [
            HomeRoute(),
            OrdersRoute(),
            QuotationsRoute(),
            InvoicesRoute(),
            StatisticsRoute(),
          ],
          bottomNavigationBuilder: (_, tabsRouter) {
            if (orgState is LoadedViewState<OrgsModel> &&
                orgState.data.status == OrganizationStatus.aprroved) {
              return BottomNavBarWidget(tabsRouter: tabsRouter);
            }
            return const SizedBox();
          },
        );
      },
    );
  }
}
