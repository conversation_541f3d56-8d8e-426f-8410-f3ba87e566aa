import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/exceptions/exceptions.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/features/payments/data/datasources/payments_remote_data_source.dart';
import 'package:mdd/features/payments/domain/entities/payments_entity.dart';
import 'package:mdd/features/payments/domain/repositories/payments_repository.dart';
import 'package:mdd/features/payments/domain/usecases/payments.dart';

final paymentsRepositoryImpl =
    Provider<PaymentsRepositoryImpl>((ref) => PaymentsRepositoryImpl(
          ref.watch(paymentsRemoteDataSourceImpl),
        ));

class PaymentsRepositoryImpl implements PaymentsRepository {
  final PaymentsRemoteDataSource _paymentsRemoteDataSource;
  PaymentsRepositoryImpl(this._paymentsRemoteDataSource);
  @override
  Future<Either<Failure, List<PaymentsEntity>>> getPayments(
      PaymentsParams params) async {
    try {
      final res = await _paymentsRemoteDataSource.getPayments(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }

  @override
  Future<Either<Failure, ResultModel>> createNewPayment(params) async {
    try {
      final res = await _paymentsRemoteDataSource.createNewPayment(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }
}
