import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/comments/data/models/comments_model.dart';
import 'package:mdd/features/orders/data/models/orders_model.dart';
import 'package:mdd/features/orders/domain/usecases/order_details.dart';

final orderDetailsProvider =
    StateNotifierProvider<OrderDetailsProvider, ViewState>(
        (ref) => OrderDetailsProvider(
              ref.watch(orderDetailsUseCaseProvider),
            ));

class OrderDetailsProvider extends BaseProvider<OrdersModel> {
  final OrderDetails _orderDetails;

  OrderDetailsProvider(this._orderDetails);

  Future<void> fetchGetOrderById(String id) async {
    setLoadingState();

    final response = await _orderDetails.call(OrderDetailsParams(id: id));
    response.fold((failure) {
      setErrorState(failure.message);
    }, (order) {
      if (order != null) {
        setLoadedState(order);
      }
    });
  }

  OrdersModel? get order {
    final state = this.state;
    if (state is LoadedViewState<OrdersModel>) {
      return state.data;
    }
    return null;
  }

  void addComment(List<CommentsModel> comments) {
    final oldState = state as LoadedViewState<OrdersModel>;
    setLoadedState(oldState.data.copyWith(comments: comments));
  }
}
