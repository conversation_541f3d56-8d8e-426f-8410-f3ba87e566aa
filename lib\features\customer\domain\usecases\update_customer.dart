import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/customer/data/repositories/customers_repository_impl.dart';
import 'package:mdd/features/customer/domain/entities/customers_entity.dart';
import 'package:mdd/features/customer/domain/repositories/customers_repository.dart';

final updateCustomerUseCaseProvider = Provider<UpdateCustomer>((ref) {
  return UpdateCustomer(
    ref.watch(customersRepositoryImpl),
  );
});

class UpdateCustomer implements UseCase<ResultModel, UpdateCustomerParams> {
  final CustomersRepository updateCustomerRepository;

  UpdateCustomer(this.updateCustomerRepository);

  @override
  Future<Either<Failure, ResultModel>> call(UpdateCustomerParams params) async {
    return await updateCustomerRepository.updateCustomer(params);
  }
}

class UpdateCustomerParams extends Equatable {
  final CustomersEntity customer;

  const UpdateCustomerParams(this.customer);

  @override
  List<Object?> get props => [customer];
}
