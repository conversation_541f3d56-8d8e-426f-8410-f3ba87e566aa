import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/organization/data/models/organizations_model.dart';
import 'package:mdd/features/organization/domain/usecases/organization_details.dart';

final orgDetailsProvider =
    StateNotifierProvider.autoDispose<OrgDetailsProvider, ViewState>(
  (ref) => OrgDetailsProvider(
    ref.watch(orgDetailsUseCaseProvider),
  ),
);

class OrgDetailsProvider extends BaseProvider<OrgsModel> {
  final OrganizationDetails _orgDetails;

  OrgDetailsProvider(this._orgDetails);

  Future<void> fetchOrgDetails() async {
    setLoadingState();
    final response = await _orgDetails.call(NoParams());
    response.fold(
      (failure) {
        setErrorState(failure.message);
      },
      setLoadedState,
    );
  }
}
