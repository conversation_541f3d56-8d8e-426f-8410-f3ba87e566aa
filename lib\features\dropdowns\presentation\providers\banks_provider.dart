import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/domain/usecases/banks.dart';

final selectedBankProvider =
    StateProvider.autoDispose<DropDownEntity?>((ref) => null);

final banksProvider = StateNotifierProvider<BanksProvider, ViewState>(
  (ref) => BanksProvider(ref.watch(banksUseCaseProvider)),
);

class BanksProvider extends BaseProvider<List<DropDownEntity>> {
  final Banks _banks;
  BanksProvider(this._banks);

  Future<void> fetchBanks() async {
    setLoadingState();
    final response = await _banks.call(NoParams());
    response.fold(
      (failure) => setErrorState(failure.message),
      setLoadedState,
    );
  }
}
