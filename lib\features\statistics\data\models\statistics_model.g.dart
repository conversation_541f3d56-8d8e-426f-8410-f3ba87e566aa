// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'statistics_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StatisticsModel _$StatisticsModelFromJson(Map<String, dynamic> json) =>
    StatisticsModel(
      quotationStatuses: (json['quotationStatuses'] as List<dynamic>?)
          ?.map(
              (e) => QuotationStatusesModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      orderStatuses: (json['orderStatuses'] as List<dynamic>?)
          ?.map((e) => OrderStatusesModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      monthlySummary: (json['monthlySummary'] as List<dynamic>?)
          ?.map((e) => MonthlySummaryModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$StatisticsModelToJson(StatisticsModel instance) =>
    <String, dynamic>{
      'quotationStatuses': instance.quotationStatuses,
      'orderStatuses': instance.orderStatuses,
      'monthlySummary': instance.monthlySummary,
    };
