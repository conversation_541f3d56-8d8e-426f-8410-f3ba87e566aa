import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/attachments/domain/entites/attachments_entity.dart';
import 'package:mdd/features/quotations/domain/usecases/pdf_quotation.dart';

final pdfQuotationProvider =
    StateNotifierProvider.autoDispose<PdfQuotationProvider, ViewState>(
  (ref) => PdfQuotationProvider(
    ref.watch(pdfQuotationsUseCaseProvider),
  ),
);

class PdfQuotationProvider extends BaseProvider<AttachmentsEntity> {
  final PdfQuotation _pdfQuotation;

  PdfQuotationProvider(this._pdfQuotation);

  Future<void> getPdfQuotation(String quotationID) async {
    setLoadingState();
    final response =
        await _pdfQuotation.call(PdfQuotationParams(id: quotationID));
    response.fold(
      (failure) {
        setErrorState(failure.message);
      },
      setLoadedState,
    );
  }
}
