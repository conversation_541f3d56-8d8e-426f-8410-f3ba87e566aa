import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mdd/core/widgets/subtitle_widget.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';

class AttachmentItemWidget extends StatelessWidget {
  final String fileName;
  final VoidCallback? onPressed;
  const AttachmentItemWidget(
      {super.key, required this.fileName, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Card(
      key: key,
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.kSizeMedium,
        vertical: AppDimensions.kSizeMedium,
      ),
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.kMediumRadius),
          side: const BorderSide(color: AppColors.lightGrey3)),
      color: AppColors.white,
      elevation: 0,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: AppDimensions.kSizeXSmall2,
          horizontal: AppDimensions.kSizeMedium,
        ),
        child: Row(
          children: [
            CircleAvatar(
              backgroundColor: AppColors.lightGrey3,
              child: SvgPicture.asset('assets/icons/attachments_icon.svg'),
            ),
            const SizedBox(
              width: AppDimensions.kSizeSmall,
            ),
            SubtitleWidget(fileName),
            const Spacer(),
            IconButton(
              onPressed: onPressed,
              icon: SvgPicture.asset('assets/icons/delete_icon.svg'),
            )
          ],
        ),
      ),
    );
  }
}
