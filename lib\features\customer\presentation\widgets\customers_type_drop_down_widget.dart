import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/drop_down_widget.dart';
import 'package:mdd/features/customer/presentation/provider/selected_customer_type_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enum_extensions.dart';
import 'package:mdd/utils/enums.dart';

class CustomersTypeDropDownWidget extends ConsumerWidget {
  const CustomersTypeDropDownWidget({
    super.key,
    this.value,
  });

  final CustomerType? value;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: AppDimensions.kSizeSmall,
        horizontal: AppDimensions.kSizeMedium,
      ),
      child: DropDownWidget<CustomerType>(
        title: 'position'.tr(),
        icon: 'assets/icons/position_icon.svg',
        validatorMessage: 'position_validate'.tr(),
        onChanged: (customerType) {
          ref.watch(selectedCustomerTypeProvider.notifier).state = customerType;
        },
        value: value,
        borderColor: AppColors.cardDetailsBackground,
        itemAsString: (types) => types.translatedName,
        items: CustomerType.values,
      ),
    );
  }
}
