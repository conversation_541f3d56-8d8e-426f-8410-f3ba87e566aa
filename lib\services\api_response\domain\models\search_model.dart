import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/services/api_response/data/entities/search_entity.dart';

part 'search_model.g.dart';

@JsonSerializable()
class SearchModel extends SearchEntity {
  SearchModel({
    required super.pages,
    required super.itemsCount,
    required super.page,
  });

  factory SearchModel.fromJson(Map<String, dynamic> json) =>
      _$SearchModelFromJson(json);

  Map<String, dynamic> toJson() => _$SearchModelToJson(this);

  @override
  String toString() => '';
}
