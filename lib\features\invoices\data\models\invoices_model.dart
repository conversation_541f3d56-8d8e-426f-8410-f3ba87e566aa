import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/invoices/domain/entities/invoices_entity.dart';
import 'package:mdd/utils/enums.dart';

part 'invoices_model.g.dart';

@JsonSerializable()
class InvoicesModel extends InvoicesEntity {
  InvoicesModel({
    super.invoiceID,
    int? invoiceNo,
    super.orderID,
    String? quotationID,
    super.dueDate,
    super.createOn,
    super.amount,
    super.paidAmount,
    bool? isPricingFee,
    super.pendingAmount,
    super.status,
    String? employeeID,
    super.employeeName,
    String? customerID,
    String? customerName,
    String? organizationID,
    String? organizationName,
    int? organizationNo,
    super.orderNo,
    String? quotationNo,
    String? customerPurchaseOrderNo,
    super.statusName,
    super.invoicePath,
  });

  factory InvoicesModel.fromJson(Map<String, dynamic> json) =>
      _$InvoicesModelFromJson(json);

  Map<String, dynamic> toJson() => _$InvoicesModelToJson(this);
}
