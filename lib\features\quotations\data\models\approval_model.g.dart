// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'approval_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ApprovalModel _$ApprovalModelFromJson(Map<String, dynamic> json) =>
    ApprovalModel(
      orderApprovalID: json['orderApprovalID'] as String?,
      quotationApprovalID: json['quotationApprovalID'] as String?,
      orderID: json['orderID'] as String,
      quotationID: json['quotationID'] as String?,
      priority: json['priority'] as int,
      approver: json['approver'] as String,
      approverName: json['approverName'] as String,
      status: $enumDecode(_$ApprovalStatusTypeEnumMap, json['status']),
      approvedOn: json['approvedOn'] as String?,
      notes: json['notes'] as String?,
      approvalLimit: (json['approvalLimit'] as num).toDouble(),
    );

Map<String, dynamic> _$ApprovalModelToJson(ApprovalModel instance) =>
    <String, dynamic>{
      'orderApprovalID': instance.orderApprovalID,
      'quotationApprovalID': instance.quotationApprovalID,
      'orderID': instance.orderID,
      'quotationID': instance.quotationID,
      'priority': instance.priority,
      'approver': instance.approver,
      'approverName': instance.approverName,
      'status': _$ApprovalStatusTypeEnumMap[instance.status]!,
      'approvedOn': instance.approvedOn,
      'notes': instance.notes,
      'approvalLimit': instance.approvalLimit,
    };

const _$ApprovalStatusTypeEnumMap = {
  ApprovalStatusType.waiting: 0,
  ApprovalStatusType.approved: 1,
  ApprovalStatusType.rejected: 2,
};
