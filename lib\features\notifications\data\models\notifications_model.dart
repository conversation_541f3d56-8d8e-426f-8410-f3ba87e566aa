import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/notifications/domain/entities/notifications_entity.dart';
import 'package:mdd/utils/enums.dart';

part 'notifications_model.g.dart';

@JsonSerializable()
class NotificationsModel extends NotificationsEntity {
  NotificationsModel(
      {required super.notificationID,
      required super.objectID,
      required super.notificationTemplateID,
      required super.title,
      required super.content,
      required super.type,
      required super.createdOn,
      required super.userID,
      required super.userName,
      required super.userType,
      required super.isEmailSent,
      required super.isPushSent,
      required super.isSMSSent,
      required super.isEmail,
      required super.isSMS,
      required super.isPush,
      required super.isViewed,
      required super.isSent,
      required super.mobileNo,
      required super.language});

  factory NotificationsModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationsModelFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationsModelToJson(this);
}
