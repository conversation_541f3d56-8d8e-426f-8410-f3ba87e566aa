import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/core/repositories/token_repository.dart';
import 'package:mdd/features/user/domain/entities/user_entity.dart';
import 'package:mdd/features/user/domain/usecases/login.dart';

final userProvider = StateNotifierProvider<UserProvider, ViewState>((ref) {
  return UserProvider(
    ref.watch(loginProvider),
    ref.watch(tokenRepositoryProvider),
  );
});

class UserProvider extends BaseProvider<UserEntity> {
  final Login _login;
  final TokenRepository _tokenRepository;
  UserProvider(this._login, this._tokenRepository);

  Future<void> login({
    required String userName,
    required String password,
  }) async {
    setLoadingState();
    final response = await _login.call(LoginParams(
      userName: userName,
      password: password,
    ));
    response.fold((failure) {
      setErrorState(failure.message);
    }, (user) {
      setLoadedState(user);
    });
  }

  Future<void> logout() async {
    await _tokenRepository.deleteUserAccess();
  }
}
