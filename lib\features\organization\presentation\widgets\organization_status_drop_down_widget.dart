// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:mdd/core/widgets/drop_down_widget.dart';
// import 'package:mdd/features/organization/presentation/provider/selected_organization_status_provider.dart';
// import 'package:mdd/theme/colors.dart';
// import 'package:mdd/theme/dimensions.dart';
// import 'package:mdd/utils/enums.dart';
//
// class OrgsStatusDropDownWidget extends ConsumerWidget {
//   const OrgsStatusDropDownWidget({
//     super.key,
//     this.value,
//   });
//
//   final OrganizationStatus? value;
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(
//         vertical: AppDimensions.kSizeSmall,
//         horizontal: AppDimensions.kSizeMedium,
//       ),
//       child: DropDownWidget<OrganizationStatus>(
//         title: 'organization_status'.tr(),
//         validatorMessage: 'organization_status_validate'.tr(),
//         onChanged: (orgStatus) {
//           ref.watch(selectedOrgStatusProvider.notifier).state = orgStatus;
//         },
//         value: value,
//         borderColor: AppColors.cardDetailsBackground,
//         itemAsString: (status) => status.translatedName,
//         items: OrganizationStatus.values,
//       ),
//     );
//   }
// }
