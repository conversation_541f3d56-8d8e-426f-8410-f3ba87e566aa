import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/customer/domain/entities/customers_entity.dart';
import 'package:mdd/utils/enums.dart';

part 'customers_model.g.dart';

@JsonSerializable(explicitToJson: true)
class CustomersModel extends CustomersEntity {
  CustomersModel({
    super.customerID,
    required super.fullName,
    required super.mobile,
    required super.email,
    super.userName,
    required super.address,
    super.password,
    required super.language,
    required super.organizationID,
    super.organizationName,
    required super.cityID,
    super.cityName,
    super.jobtitleID,
    super.jobtitleName,
    super.departmentID,
    super.departmentName,
    super.isEmailConfrimd,
    super.isActive,
    super.isPasswordNeedToChange,
    super.lastPasswordChage,
    super.isMfaEnabled,
    super.mfaCode,
    super.image,
    super.imageFullPath,
    required super.type,
    super.typeName,
    super.creditBalance,
    super.isCredit,
    super.isSMSNotificationActive,
    super.isEmailNotificationActive,
  });

  factory CustomersModel.fromJson(Map<String, dynamic> json) =>
      _$CustomersModelFromJson(json);

  Map<String, dynamic> toJson() => _$CustomersModelToJson(this);

  @override
  CustomersModel copyWith({
    String? customerID,
    String? fullName,
    String? mobile,
    String? email,
    String? userName,
    String? address,
    String? password,
    String? language,
    String? organizationID,
    String? organizationName,
    String? cityID,
    String? cityName,
    String? jobtitleID,
    String? jobtitleName,
    String? departmentID,
    String? departmentName,
    bool? isEmailConfrimd,
    bool? isActive,
    bool? isPasswordNeedToChange,
    DateTime? lastPasswordChage,
    bool? isMfaEnabled,
    String? mfaCode,
    String? image,
    String? imageFullPath,
    CustomerType? type,
    String? typeName,
    double? creditBalance,
    bool? isCredit,
    bool? isSMSNotificationActive,
    bool? isEmailNotificationActive,
  }) {
    return CustomersModel(
      customerID: customerID ?? this.customerID,
      fullName: fullName ?? this.fullName,
      mobile: mobile ?? this.mobile,
      email: email ?? this.email,
      address: address ?? this.address,
      password: password ?? this.password,
      language: language ?? this.language,
      organizationID: organizationID ?? this.organizationID,
      organizationName: organizationName ?? this.organizationName,
      cityID: cityID ?? this.cityID,
      cityName: cityName ?? this.cityName,
      jobtitleID: jobtitleID ?? this.jobtitleID,
      jobtitleName: jobtitleName ?? this.jobtitleName,
      departmentID: departmentID ?? this.departmentID,
      departmentName: departmentName ?? this.departmentName,
      isEmailConfrimd: isEmailConfrimd ?? this.isEmailConfrimd,
      isActive: isActive ?? this.isActive,
      isPasswordNeedToChange:
      isPasswordNeedToChange ?? this.isPasswordNeedToChange,
      lastPasswordChage: lastPasswordChage ?? this.lastPasswordChage,
      isMfaEnabled: isMfaEnabled ?? this.isMfaEnabled,
      mfaCode: mfaCode ?? this.mfaCode,
      image: image ?? this.image,
      imageFullPath: imageFullPath ?? this.imageFullPath,
      type: type ?? this.type,
      typeName: typeName ?? this.typeName,
      creditBalance: creditBalance ?? this.creditBalance,
      isCredit: isCredit ?? this.isCredit,
      isSMSNotificationActive:
      isSMSNotificationActive ?? this.isSMSNotificationActive,
      isEmailNotificationActive:
      isEmailNotificationActive ?? this.isEmailNotificationActive,
    );
  }
}