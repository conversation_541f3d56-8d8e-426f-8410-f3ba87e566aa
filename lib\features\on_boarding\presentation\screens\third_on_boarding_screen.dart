import 'dart:math' as math;

import 'package:animate_do/animate_do.dart';
import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:responsive_framework/responsive_framework.dart';

@RoutePage()
class ThirdOnBoardingScreen extends StatelessWidget {
  const ThirdOnBoardingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        bottom: false,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            InkWell(
              onTap: () => context.replaceRoute(const FourthOnBoardingRoute()),
              child: <PERSON>(
                tag: 'on_boarding_button',
                child: Transform.rotate(
                  angle: context.locale.languageCode == 'en' ? math.pi : 0,
                  child: Image.asset(
                    'assets/images/rounded_button_ar.png',
                    height: 150,
                  ),
                ),
              ),
            ),
            Column(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.kSize6XLarge,
                  ),
                  child: RichText(
                    textAlign: TextAlign.center,
                    text: TextSpan(
                      text: "${'on_boarding_title3_1'.tr()} ",
                      style: context.textTheme.headlineMedium?.copyWith(
                        color: AppColors.darkGrey,
                        fontWeight: FontWeight.w500,
                      ),
                      children: [
                        TextSpan(
                          text: "${'on_boarding_title3_2'.tr()} ",
                          style: context.textTheme.headlineMedium?.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        TextSpan(
                          text: 'on_boarding_title3_3'.tr(),
                          style: context.textTheme.headlineMedium?.copyWith(
                            color: AppColors.darkGrey,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: AppDimensions.kSize3XLarge),
                const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircleAvatar(
                      radius: 5,
                      backgroundColor: AppColors.activeIndicatorColor,
                    ),
                    SizedBox(width: AppDimensions.kSizeSmall),
                    CircleAvatar(
                      radius: 5,
                      backgroundColor: AppColors.activeIndicatorColor,
                    ),
                    SizedBox(width: AppDimensions.kSizeSmall),
                    CircleAvatar(
                      radius: 5,
                      backgroundColor: AppColors.activeIndicatorColor,
                    ),
                    SizedBox(width: AppDimensions.kSizeSmall),
                    CircleAvatar(
                      radius: 5,
                      backgroundColor: AppColors.unActiveIndicatorColor,
                    ),
                  ],
                ),
              ],
            ),
            SlideInUp(
              child: Image.asset(
                'assets/images/on_boarding_image3.png',
                height: context.widthR(
                  ResponsiveBreakpoints.of(context).smallerThan(TABLET)
                      ? 0.8
                      : 0.4,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
