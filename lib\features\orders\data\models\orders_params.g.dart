// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'orders_params.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrdersParams _$OrdersParamsFromJson(Map<String, dynamic> json) => OrdersParams(
      page: json['page'] as int,
      status: $enumDecodeNullable(_$OrderStatusTypeEnumMap, json['status']),
      find: json['find'] as String?,
      orderType: json['orderType'] as String?,
      sort: json['sort'] as String? ?? 'CreatedOnDesc',
      limit: json['limit'] as int? ?? AppConstants.paginationLimit,
      includeQuotations: json['includeQuotations'] as bool? ?? true,
      includeDetails: json['includeDetails'] as bool? ?? true,
    );

Map<String, dynamic> _$OrdersParamsToJson(OrdersParams instance) =>
    <String, dynamic>{
      'page': instance.page,
      'status': _$OrderStatusTypeEnumMap[instance.status],
      'find': instance.find,
      'orderType': instance.orderType,
      'sort': instance.sort,
      'limit': instance.limit,
      'includeQuotations': instance.includeQuotations,
      'includeDetails': instance.includeDetails,
    };

const _$OrderStatusTypeEnumMap = {
  OrderStatusType.none: 0,
  OrderStatusType.created: 1,
  OrderStatusType.submitted: 2,
  OrderStatusType.underProcess: 3,
  OrderStatusType.quotationSubmitted: 4,
  OrderStatusType.quotationApproved: 5,
  OrderStatusType.quotationDeclient: 6,
  OrderStatusType.paymentWaiting: 7,
  OrderStatusType.paymentDone: 8,
  OrderStatusType.paymentApproved: 9,
  OrderStatusType.payemntDeclinet: 10,
  OrderStatusType.deliverProcess: 11,
  OrderStatusType.deliveryNoteSent: 12,
  OrderStatusType.completed: 13,
  OrderStatusType.closed: 14,
  OrderStatusType.canceled: 15,
  OrderStatusType.poWaitingForRequest: 16,
  OrderStatusType.ipoSubmitted: 17,
  OrderStatusType.ipoClosed: 18,
  OrderStatusType.quotationReturned: 19,
  OrderStatusType.quotationExpired: 20,
  OrderStatusType.quotationReNewed: 21,
  OrderStatusType.pricingFeeWaiting: 22,
  OrderStatusType.quotationReturnedAfterApproval: 23,
  OrderStatusType.ipoReturnd: 24,
  OrderStatusType.ipoRepoend: 25,
  OrderStatusType.waiting: 26,
  OrderStatusType.rejected: 27,
  OrderStatusType.preparingShipment: 28,
  OrderStatusType.ipoMissingDocs: 29,
  OrderStatusType.ipoInComplete: 30,
  OrderStatusType.invocing: 31,
};
