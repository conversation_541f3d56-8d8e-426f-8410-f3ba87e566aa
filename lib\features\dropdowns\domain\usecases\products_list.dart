import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/dropdowns/data/repositories/drop_downs_repository_impl.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/domain/repositories/drop_downs_repository.dart';

final productsListUseCaseProvider = Provider<ProductsList>(
  (ref) => ProductsList(ref.watch(dropDownsRepositoryImpl)),
);

class ProductsList
    implements UseCase<List<DropDownEntity>, ProductsListParams> {
  final DropDownsRepository _repository;

  ProductsList(this._repository);

  @override
  Future<Either<Failure, List<DropDownEntity>>> call(
      ProductsListParams params) async {
    return _repository.productsList(params);
  }
}

class ProductsListParams {
  final String? find;
  const ProductsListParams({this.find});
}
