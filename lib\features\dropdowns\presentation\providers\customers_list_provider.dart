import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/domain/usecases/customers_list.dart';

final selectedCustomerProvider =
    StateProvider.autoDispose<DropDownEntity?>((ref) => null);

final customersListProvider =
    StateNotifierProvider.autoDispose<CustomersListProvider, ViewState>(
  (ref) => CustomersListProvider(
    ref.watch(customersListUseCaseProvider),
  ),
);

class CustomersListProvider extends BaseProvider<List<DropDownEntity>> {
  final CustomersList _customers;

  CustomersListProvider(this._customers);

  Future<void> fetchCustomersList(String? orgID) async {
    setLoadingState();
    final response = await _customers.call(CustomersListParams(orgID: orgID));
    response.fold(
      (failure) => setErrorState(failure.message),
      setLoadedState,
    );
  }
}
