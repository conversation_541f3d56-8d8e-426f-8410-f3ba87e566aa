import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/endPoints/end_points.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/features/projects/data/models/projects_model.dart';
import 'package:mdd/features/projects/domain/usecases/create_new_project.dart';
import 'package:mdd/features/projects/domain/usecases/projects.dart';
import 'package:mdd/features/projects/domain/usecases/update_project.dart';
import 'package:mdd/services/dio_client.dart';
import 'package:mdd/utils/constants/constants.dart';

abstract class ProjectsRemoteDataSource {
  Future<List<ProjectsModel>> getProjects(ProjectsParams params);
  Future<ResultModel> createNewProject(CreateNewProjectParams params);
  Future<ResultModel> updateProject(UpdateProjectParams params);
}

final projectsRemoteDataSourceImpl = Provider<ProjectsRemoteDataSourceImpl>(
  (ref) {
    return ProjectsRemoteDataSourceImpl(ref.watch(dioClientProvider));
  },
);

class ProjectsRemoteDataSourceImpl implements ProjectsRemoteDataSource {
  final DioClient _dioClient;

  ProjectsRemoteDataSourceImpl(this._dioClient);
  @override
  Future<List<ProjectsModel>> getProjects(ProjectsParams params) async {
    final response = await _dioClient.dio.get(
      EndPoints.projects,
      queryParameters: {
        "organizationID": params.organizationId,
        "limit": AppConstants.paginationLimit,
        "find": params.find,
        'page': params.page,
      },
    );
    return (response.data['data'] as List)
        .map((project) => ProjectsModel.fromJson(project))
        .toList();
  }

  @override
  Future<ResultModel> createNewProject(CreateNewProjectParams params) async {
    final response = await _dioClient.dio.post(
      EndPoints.projects,
      data: params.createNewProject.toJson()
        ..removeWhere(
          (key, value) => value == null,
        ),
    );
    return ResultModel.fromJson(response.data);
  }

  @override
  Future<ResultModel> updateProject(UpdateProjectParams params) async {
    final response = await _dioClient.dio.put(
      EndPoints.projects,
      data: params.project,
    );
    return ResultModel.fromJson(response.data);
  }
}
