import 'package:auto_route/annotations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';

@RoutePage()
class CustomerSupportScreen extends ConsumerStatefulWidget {
  const CustomerSupportScreen({
    super.key,
  });

  @override
  ConsumerState createState() => _CustomerSupportScreenState();
}

class _CustomerSupportScreenState extends ConsumerState<CustomerSupportScreen> {
  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      appBar: MainAppBar(),
      body: Column(
        children: [],
      ),
    );
  }
}
