import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/customer/data/models/customers_model.dart';
import 'package:mdd/features/user/data/repositories/user_repository_impl.dart';
import 'package:mdd/features/user/domain/repositories/user_repository.dart';

final updateProfileUserCaseProvider = Provider<UpdateProfile>(
    (ref) => UpdateProfile(ref.watch(userRepositoryImpl)));

class UpdateProfile implements UseCase<ResultModel, UpdateProfileParams> {
  final UserRepository userRepository;

  UpdateProfile(this.userRepository);

  @override
  Future<Either<Failure, ResultModel>> call(UpdateProfileParams params) async {
    return await userRepository.updateProfile(params);
  }
}

class UpdateProfileParams extends Equatable {
  final CustomersModel customerModel;

  const UpdateProfileParams(this.customerModel);

  @override
  List<Object?> get props => [customerModel];
}
