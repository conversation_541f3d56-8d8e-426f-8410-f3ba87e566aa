import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:map_picker/map_picker.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/location_picker/data/location_model.dart';
import 'package:mdd/features/location_picker/presentation/provider/current_location_provider.dart';
import 'package:mdd/features/location_picker/presentation/provider/selected_location_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class LocationPickerView extends ConsumerStatefulWidget {
  const LocationPickerView({
    super.key,
  });

  @override
  ConsumerState createState() => _LocationPickerViewState();
}

class _LocationPickerViewState extends ConsumerState<LocationPickerView> {
  final _controller = Completer<GoogleMapController>();
  final _mapPickerController = MapPickerController();
  final _textController = TextEditingController();
  late CameraPosition _cameraPosition;

  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      ref.read(currentLocationProvider.notifier).fetchCurrentLocation();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(currentLocationProvider);
    if (state is LoadingViewState) {
      return const LoaderWidget();
    }
    if (state is LoadedViewState<Position>) {
      _cameraPosition = CameraPosition(
          target: LatLng(state.data.latitude, state.data.longitude), zoom: 15);
      return Stack(
        alignment: Alignment.topCenter,
        children: [
          MapPicker(
            // pass icon widget
            iconWidget: SvgPicture.asset(
              "assets/icons/location_icon.svg",
              height: context.widthR(0.1),
              colorFilter:
                  const ColorFilter.mode(AppColors.blue, BlendMode.srcIn),
            ),
            //add map picker controller
            mapPickerController: _mapPickerController,
            child: GoogleMap(
              myLocationEnabled: true,
              zoomControlsEnabled: false,
              // hide location button
              myLocationButtonEnabled: true,
              mapType: MapType.normal,
              //  camera position
              initialCameraPosition: _cameraPosition,
              onMapCreated: (GoogleMapController controller) {
                if (!_controller.isCompleted) {
                  _controller.complete(controller);
                }
              },
              onCameraMoveStarted: () {
                // notify map is moving
                _mapPickerController.mapMoving!();
              },
              onCameraMove: (cameraPosition) {
                _cameraPosition = cameraPosition;
              },
              onCameraIdle: () async {
                // notify map stopped moving
                _mapPickerController.mapFinishedMoving!();
                //get address name from camera positi/mon
                List<Placemark> placemarks = await placemarkFromCoordinates(
                  _cameraPosition.target.latitude,
                  _cameraPosition.target.longitude,
                );
                setState(() {
                  _textController.text = [
                    placemarks[0].subThoroughfare?.trim(),
                    placemarks[0].thoroughfare?.trim(),
                    placemarks[0].subLocality?.trim(),
                    placemarks[0].locality?.trim(),
                    placemarks[0].subAdministrativeArea?.trim(),
                    placemarks[0].administrativeArea?.trim(),
                  ]
                      .where((element) => (element?.isNotEmpty ?? false))
                      .join(', ');
                });

                // update the ui with the address
              },
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).viewPadding.top + 20,
            width: MediaQuery.of(context).size.width - 50,
            child: Card(
              color: AppColors.cardDetailsBackground,
              elevation: 0,
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: AppDimensions.kSizeSmall,
                  horizontal: AppDimensions.kSizeMedium,
                ),
                child: TextWidget(
                  _textController.text,
                  color: AppColors.blue,
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          Positioned(
            bottom: MediaQuery.of(context).viewPadding.bottom +
                AppDimensions.kSizeMedium,
            right: context.widthR(0.3),
            left: context.widthR(0.3),
            child: ButtonWidget(
              title: 'اختيار',
              onPressed: () {
                ref.read(selectedLocationProvider.state).state = LocationModel(
                    latitude: _cameraPosition.target.latitude,
                    longitude: _cameraPosition.target.longitude,
                    address: _textController.text);
                context.maybePop();
              },
              horizontalPadding: AppDimensions.kSize4XLarge,
              shapeRadius: AppDimensions.kMediumRadius,
              backgroundColor: AppColors.blue,
            ),
          ),
        ],
      );
    }
    return const SizedBox();
  }
}
