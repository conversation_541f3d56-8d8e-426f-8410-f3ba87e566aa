import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:mdd/features/attachments/domain/entites/attachments_entity.dart';
import 'package:mdd/utils/helper_functions.dart';
import 'package:mdd/utils/sentry_reporter.dart';
import 'package:mdd/utils/ui/ui_helper.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';

class FilesHandler {
  final AttachmentsEntity _attachment;
  FilesHandler(this._attachment);

  Future<void> openFile() async {
    try {
      if (_attachment.fileContent != null && _attachment.fileName != null) {
        await _open();
      } else if (_attachment.url != null && _attachment.fileName != null) {
        await _openURL();
      } else {

        UiHelper.showNotification('لا يمكن فتح الملف');
      }
    } catch (e, stackTrace) {
      print(e);
      print(stackTrace);
      SentryReporter.genericThrow(e, stackTrace: stackTrace);
      // UiHelper.showNotification('لا يمكن فتح الملف');
    }
  }

  Future<void> _open() async {
    final bytes = base64.decode(_attachment.fileContent!);
    final directory = await getApplicationDocumentsDirectory();
    String path;
    if (HelperFunctions.hasExtension(_attachment.fileName!)) {
      path = '${directory.path}/${_attachment.fileName}';
    } else {
      if (_attachment.fileContentType != null) {
        path =
            '${directory.path}/${_attachment.fileName}.${_attachment.fileContentType!.split('/').last}';
      } else {
        path = '${directory.path}/${_attachment.fileName}.pdf';
      }
    }
    final file = File(path);
    if (!file.existsSync()) await file.create();
    final fileSaved = await file.writeAsBytes(bytes);
    await OpenFilex.open(fileSaved.path);
  }

  Future<void> _openURL() async {
    final url = _attachment.url;
    if (url == null) return;
    final res = await Dio().get(
      url,
      options: Options(responseType: ResponseType.bytes),
    );
    final bytes = res.data;
    final directory = await getApplicationDocumentsDirectory();
    String path = '${directory.path}/${_attachment.fileName ?? 'file'}';
    final file = File(path);
    if (!file.existsSync()) await file.create();
    final fileSaved = await file.writeAsBytes(bytes);
    await OpenFilex.open(fileSaved.path);
  }
}
