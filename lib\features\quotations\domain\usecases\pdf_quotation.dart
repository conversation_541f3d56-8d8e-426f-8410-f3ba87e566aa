import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/attachments/domain/entites/attachments_entity.dart';
import 'package:mdd/features/quotations/data/repositories/quotations_repository_impl.dart';
import 'package:mdd/features/quotations/domain/repositories/quotations_repository.dart';

final pdfQuotationsUseCaseProvider = Provider<PdfQuotation>(
  (ref) => PdfQuotation(ref.watch(quotationsRepositoryImpl)),
);

class PdfQuotation implements UseCase<AttachmentsEntity, PdfQuotationParams> {
  final QuotationsRepository _quotationsRepository;

  PdfQuotation(this._quotationsRepository);

  @override
  Future<Either<Failure, AttachmentsEntity>> call(
      PdfQuotationParams params) async {
    return _quotationsRepository.getPdfQuotation(params);
  }
}

class PdfQuotationParams {
  final String? id;
  PdfQuotationParams({required this.id});
}
