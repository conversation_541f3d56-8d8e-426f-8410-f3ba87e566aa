import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';

@RoutePage()
class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({
    super.key,
  });

  @override
  ConsumerState createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MainAppBar(),
      body: Column(
        children: [
          DetailsCustomBar(
            title: 'settings'.tr(),
          ),
          ListTile(
            dense: true,
            title: TextWidget('language'.tr()),
            trailing: ToggleButtons(
              borderRadius: BorderRadius.circular(AppDimensions.kDefaultRadius),
              fillColor: AppColors.primary,
              onPressed: (index) {
                if (index == 0 && context.locale.languageCode == "en") {
                  context.setLocale(const Locale('ar'));
                  context.router.replaceAll([const SplashRoute()]);
                } else if (index == 1 && context.locale.languageCode == "ar") {
                  context.setLocale(const Locale('en'));
                  context.router.replaceAll([const SplashRoute()]);
                }
              },
              isSelected: [
                context.locale.languageCode == "ar",
                context.locale.languageCode == "en"
              ],
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.kSizeLarge),
                  child: TextWidget('arabic'.tr()),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.kSizeLarge),
                  child: TextWidget('english'.tr()),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
