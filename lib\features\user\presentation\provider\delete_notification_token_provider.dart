import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/user/domain/usecases/delete_notification_token.dart';

final deleteNotificationTokenProvider =
    StateNotifierProvider<DeleteNotificationTokenProvider, ViewState>(
        (ref) => DeleteNotificationTokenProvider(
              ref.watch(deleteNotificationTokenUseCaseProvider),
            ));

class DeleteNotificationTokenProvider extends BaseProvider<ResultModel> {
  final DeleteNotificationToken _notificationToken;

  DeleteNotificationTokenProvider(
    this._notificationToken,
  );

  Future<void> deleteNotificationToken(
      {required String notificationToken}) async {
    setLoadingState();
    final response = await _notificationToken.call(notificationToken);
    response.fold((failure) {
      //TODO : handle internal server error
      if (failure is InternalServerErrorFailure) {
        setLoadedState(ResultModel());
        return;
      }
      setErrorState(failure.message);
    }, (response) {
      setLoadedState(response);
    });
  }
}
