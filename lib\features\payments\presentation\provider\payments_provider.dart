import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/paginated_provider.dart';
import 'package:mdd/features/payments/domain/entities/payments_entity.dart';
import 'package:mdd/features/payments/domain/usecases/payments.dart';
import 'package:mdd/features/payments/presentation/provider/payment_text_field_provider.dart';

final paymentStatusProvider = StateProvider.autoDispose<int?>((ref) => null);

final paymentsProvider =
    StateNotifierProvider.autoDispose<PaymentsProvider, ViewState>(
        (ref) => PaymentsProvider(
              ref.watch(paymentsUseCaseProvider),
              ref.watch(paymentStatusProvider),
              ref.watch(paymentSearchControllerProvider),
            ));

class PaymentsProvider extends PaginatedProvider<PaymentsEntity> {
  final Payments _payments;
  final int? _paymentStatus;
  final TextEditingController? _paymentSearchController;

  PaymentsProvider(
    this._payments,
    this._paymentStatus,
    this._paymentSearchController,
  ) : super(InitialViewState());

  Future<void> fetchPayments() async {
    state = LoadingViewState();
    final response = await fetchList();
    response.fold((failure) {
      state = ErrorViewState(errorMessage: failure.message);
    }, (orders) {
      if (orders != null) {
        state = LoadedViewState(orders);
      }
    });
  }

  @override
  Future<Either<Failure, List<PaymentsEntity>?>> fetchList() {
    return _payments.call(PaymentsParams(
      page: pageNumber++,
      paymentNo: _paymentSearchController?.text,
      paymentStatus: _paymentStatus,
    ));
  }
}
