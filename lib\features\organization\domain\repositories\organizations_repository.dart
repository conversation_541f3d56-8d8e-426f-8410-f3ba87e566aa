import 'package:dartz/dartz.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/organization/data/models/organizations_model.dart';
import 'package:mdd/features/organization/domain/usecases/update_organization.dart';

abstract class OrgsRepository {
  Future<Either<Failure, OrgsModel>> orgDetails(NoParams params);
  Future<Either<Failure, ResultModel>> update(UpdateOrgParams params);
}
