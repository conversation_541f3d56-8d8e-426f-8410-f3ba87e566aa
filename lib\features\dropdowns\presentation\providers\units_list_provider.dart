import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/domain/usecases/units_list.dart';

final selectedUnitProvider =
    StateProvider.autoDispose<DropDownEntity?>((ref) => null);

final unitsListProvider = StateNotifierProvider<UnitsListProvider, ViewState>(
  (ref) => UnitsListProvider(
    ref.watch(unitsListUseCaseProvider),
  ),
);

class UnitsListProvider extends BaseProvider<List<DropDownEntity>> {
  final UnitsList _units;

  UnitsListProvider(this._units);

  Future<void> fetchUnits() async {
    setLoadingState();
    final response = await _units.call(NoParams());
    response.fold(
      (failure) => setErrorState(failure.message),
      setLoadedState,
    );
  }
}
