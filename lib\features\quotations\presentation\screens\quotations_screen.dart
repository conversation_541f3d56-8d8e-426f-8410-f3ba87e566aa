import 'package:auto_route/annotations.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/search_bar_widget.dart';
import 'package:mdd/core/widgets/tap_menu_widget.dart';
import 'package:mdd/features/quotations/presentation/provider/quotation_state_provider.dart';
import 'package:mdd/features/quotations/presentation/provider/quotations_provider.dart';
import 'package:mdd/features/quotations/presentation/screens/quotations_view.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enum_extensions.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/extensions.dart';

@RoutePage()
class QuotationsScreen extends ConsumerStatefulWidget {
  const QuotationsScreen({
    super.key,
  });

  @override
  ConsumerState createState() => _QuotationsScreenState();
}

class _QuotationsScreenState extends ConsumerState<QuotationsScreen> {
  void _fetch() {
    ref.read(quotationsProvider.notifier).fetchQuotations();
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final quotationStatusState = ref.watch(quotationStatusProvider.state);
    final quotationSearchController =
        ref.watch(quotationSearchControllerProvider);
    final quotationProvider = ref.read(quotationsProvider.notifier);
    return Column(
      children: [
        const SizedBox(
          height: AppDimensions.kSizeLarge,
        ),
        SearchBarWidget(
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          keyboardType: TextInputType.number,
          showClearIcon: ref.watch(quotationSuffixIconProvider),
          onPressedSearch: () {
            if (quotationSearchController.text.isNotEmpty) {
              quotationProvider.resetPagination();
              _fetch();
            }
          },
          onPressedClear: () {
            ref.read(quotationSuffixIconProvider.state).state = false;
            quotationSearchController.clear();
            quotationProvider.resetPagination();
            _fetch();
          },
          textController: quotationSearchController,
          onChange: (String value) {
            if (value.isEmpty) {
              ref.read(quotationSuffixIconProvider.state).state = false;
              ref.read(quotationsProvider.notifier).resetPagination();
              _fetch();
              return;
            }
            ref.read(quotationSuffixIconProvider.state).state = true;
          },
        ),
        SizedBox(
          height: context.heightR(.05),
          child: ListView(
            padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.kSizeMedium),
            scrollDirection: Axis.horizontal,
            children: [
              TapMenuWidget(
                isActive: quotationStatusState.state == null,
                tabTitle: 'all_status'.tr(),
                onTap: () {
                  if (quotationStatusState.state != null) {
                    quotationStatusState.state = null;
                    _fetch();
                  }
                },
              ),
              TapMenuWidget(
                isActive: quotationStatusState.state == 1,
                tabTitle: QuotationsStatusType.submitted.translatedName,
                onTap: () {
                  if (quotationStatusState.state != 1) {
                    quotationStatusState.state = 1;
                    _fetch();
                  }
                },
              ),
              TapMenuWidget(
                isActive: quotationStatusState.state == 2,
                tabTitle: QuotationsStatusType.accepted.translatedName,
                onTap: () {
                  if (quotationStatusState.state != 2) {
                    quotationStatusState.state = 2;
                    _fetch();
                  }
                },
              ),
              TapMenuWidget(
                isActive: quotationStatusState.state == 3,
                tabTitle: QuotationsStatusType.rejected.translatedName,
                onTap: () {
                  if (quotationStatusState.state != 3) {
                    quotationStatusState.state = 3;
                    _fetch();
                  }
                },
              ),
              TapMenuWidget(
                isActive: quotationStatusState.state == 4,
                tabTitle: QuotationsStatusType.expired.translatedName,
                onTap: () {
                  if (quotationStatusState.state != 4) {
                    quotationStatusState.state = 4;
                    _fetch();
                  }
                },
              ),
              TapMenuWidget(
                isActive: quotationStatusState.state == 5,
                tabTitle: QuotationsStatusType.canceled.translatedName,
                onTap: () {
                  if (quotationStatusState.state != 5) {
                    quotationStatusState.state = 5;
                    _fetch();
                  }
                },
              ),
            ],
          ),
        ),
        const Expanded(child: QuotationsView()),
      ],
    );
  }
}
