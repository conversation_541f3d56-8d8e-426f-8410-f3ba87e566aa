import 'dart:convert';

import 'package:flutter/services.dart';

abstract class ConfigReader {
  static late Map<String, dynamic> _config;

  static Future<String> _loadJsonData() async {
    return await rootBundle.loadString('config/app_config.json');
  }

  static Future<void> initialize() async {
    final configString = await _loadJsonData();
    _config = json.decode(configString);
  }

  static String? getSentryDns() {
    return _config['sentryDns'] as String?;
  }

  static String? getUsername() {
    return _config['username'] as String?;
  }

  static String? getPassword() {
    return _config['password'] as String?;
  }
}
