import 'dart:convert';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/features/attachments/data/models/attachments_model.dart';
import 'package:mdd/utils/helper_functions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

final attachmentsProvider =
    ChangeNotifierProvider((ref) => AttachmentsProvider());

class AttachmentsProvider with ChangeNotifier {
  final Map<String, AttachmentsModel> _attachments = {};

  List<AttachmentsModel> get attachments => _attachments.values.toList();

  set attachments(List<AttachmentsModel> attachments) {
    _attachments.addAll({
      for (var attachment in attachments)
        HelperFunctions.generateId(): attachment
    });
    _attachments.length;
    notifyListeners();
  }

  void addAttachments(PlatformFile? file) {
    if (isFileExist(file?.name)) {
      UiHelper.showNotification('الملف موجود مسبقاً');
      return;
    }
    final key = HelperFunctions.generateId();
    final attachment = AttachmentsModel(
        fileName: file?.name,
        fileContent: base64.encode(File(file?.path ?? '').readAsBytesSync()),
        fileContentType: file?.extension,
        orderAttachmentId: key);
    if (!_attachments.containsKey(key)) {
      _attachments.putIfAbsent(key, () => attachment);
    }

    notifyListeners();
  }

  bool isFileExist(String? fileName) {
    return _attachments.values.any(
      (element) => element.fileName == fileName,
    );
  }

  void removeAttachment(String? orderAttachmentId) {
    if (orderAttachmentId != null) {
      _attachments.removeWhere((key, value) {
        return key == orderAttachmentId;
      });
      notifyListeners();
    }
  }

  int get totalAttachments => _attachments.length;

  void clearAll() {
    _attachments.clear();
  }
}
