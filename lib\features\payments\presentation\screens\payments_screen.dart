import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/core/widgets/search_bar_widget.dart';
import 'package:mdd/core/widgets/tap_menu_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/attachments/presentation/provider/attachments_provider.dart';
import 'package:mdd/features/payments/presentation/provider/payment_text_field_provider.dart';
import 'package:mdd/features/payments/presentation/provider/payments_provider.dart';
import 'package:mdd/features/payments/presentation/screens/payments_view.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enum_extensions.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/extensions.dart';

@RoutePage()
class PaymentsScreen extends ConsumerStatefulWidget {
  const PaymentsScreen({
    super.key,
  });

  @override
  ConsumerState createState() => _PaymentsScreenState();
}

class _PaymentsScreenState extends ConsumerState<PaymentsScreen> {
  void _fetch() {
    ref.read(paymentsProvider.notifier).fetchPayments();
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final paymentStatusState = ref.watch(paymentStatusProvider.state);
    final paymentSearchController = ref.watch(paymentSearchControllerProvider);
    final paymentProvider = ref.read(paymentsProvider.notifier);
    return Scaffold(
      appBar: const MainAppBar(),
      floatingActionButton: FloatingActionButton.extended(
        heroTag: 'floating_action_payments_screen',
        onPressed: () {
          context
              .pushRoute(CreatePaymentRoute())
              .then((value) => ref.read(attachmentsProvider).clearAll());
        },
        label: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.kSizeSmall,
          ),
          child: TextWidget(
            '+ ${'create_payment'.tr()}',
            color: AppColors.white,
          ),
        ),
      ),
      body: Column(
        children: [
          DetailsCustomBar(title: 'payments'.tr()),
          SearchBarWidget(
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            keyboardType: TextInputType.number,
            showClearIcon: ref.watch(paymentSuffixIconProvider),
            onPressedSearch: () {
              if (paymentSearchController.text.isNotEmpty) {
                paymentProvider.resetPagination();
                _fetch();
              }
            },
            onPressedClear: () {
              ref.read(paymentSuffixIconProvider.state).state = false;
              paymentSearchController.clear();
              paymentProvider.resetPagination();
              _fetch();
            },
            textController: paymentSearchController,
            onChange: (String value) {
              if (value.isEmpty) {
                ref.read(paymentSuffixIconProvider.state).state = false;
                paymentProvider.resetPagination();
                _fetch();
                return;
              }
              ref.read(paymentSuffixIconProvider.state).state = true;
            },
          ),
          SizedBox(
            height: context.heightR(.05),
            child: ListView(
              padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.kSizeMedium),
              scrollDirection: Axis.horizontal,
              children: [
                TapMenuWidget(
                  isActive: paymentStatusState.state == null,
                  tabTitle: 'all_status'.tr(),
                  onTap: () {
                    if (paymentStatusState.state != null) {
                      paymentStatusState.state = null;
                      _fetch();
                    }
                  },
                ),
                TapMenuWidget(
                  isActive: paymentStatusState.state == 0,
                  tabTitle: PaymentsStatusType.waiting.translatedName,
                  onTap: () {
                    if (paymentStatusState.state != 0) {
                      paymentStatusState.state = 0;
                      _fetch();
                    }
                  },
                ),
                TapMenuWidget(
                  isActive: paymentStatusState.state == 1,
                  tabTitle: PaymentsStatusType.pending.translatedName,
                  onTap: () {
                    if (paymentStatusState.state != 1) {
                      paymentStatusState.state = 1;
                      _fetch();
                    }
                  },
                ),
                TapMenuWidget(
                  isActive: paymentStatusState.state == 2,
                  tabTitle: PaymentsStatusType.approved.translatedName,
                  onTap: () {
                    if (paymentStatusState.state != 2) {
                      paymentStatusState.state = 2;
                      _fetch();
                    }
                  },
                ),
                TapMenuWidget(
                  isActive: paymentStatusState.state == 3,
                  tabTitle: PaymentsStatusType.partiallyApproved.translatedName,
                  onTap: () {
                    if (paymentStatusState.state != 3) {
                      paymentStatusState.state = 3;
                      _fetch();
                    }
                  },
                ),
                TapMenuWidget(
                  isActive: paymentStatusState.state == 4,
                  tabTitle: PaymentsStatusType.rejected.translatedName,
                  onTap: () {
                    if (paymentStatusState.state != 4) {
                      paymentStatusState.state = 4;
                      _fetch();
                    }
                  },
                ),
              ],
            ),
          ),
          const Expanded(child: PaymentsView()),
        ],
      ),
    );
  }
}
