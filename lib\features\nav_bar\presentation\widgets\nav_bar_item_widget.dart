import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/extensions.dart';

class NavBarButtonWidget extends StatelessWidget {
  const NavBarButtonWidget({
    super.key,
    required this.svgUrl,
    required this.text,
    required this.destination,
    required this.onTap,
    required this.isSelected,
  });
  final String svgUrl;
  final String text;
  final BottomNavBarDestination destination;
  final VoidCallback onTap;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      return SizedBox(
        height: kBottomNavigationBarHeight,
        child: Material(
            type: MaterialType.transparency,
            child: InkWell(
              onTap: onTap,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  SizedBox(
                    child: SvgPicture.asset(
                      svgUrl,
                      colorFilter: ColorFilter.mode(
                          isSelected
                              ? context.colorScheme.primary
                              : AppColors.grey,
                          BlendMode.srcIn),
                      width: 22,
                      height: 22,
                    ),
                  ),
                  const SizedBox(height: AppDimensions.kSizeXSmall),
                  Text(
                    text,
                    style: TextStyle(
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.normal,
                      fontSize: 11,
                      color: isSelected
                          ? context.colorScheme.primary
                          : AppColors.grey,
                    ),
                  )
                ],
              ),
            )),
      );
    });
  }
}
