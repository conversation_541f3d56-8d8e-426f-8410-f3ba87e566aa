import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/presentation/providers/units_list_provider.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class UnitsView extends ConsumerStatefulWidget {
  const UnitsView({
    super.key,
  });

  @override
  ConsumerState createState() => _UnitsViewState();
}

class _UnitsViewState extends ConsumerState<UnitsView> {
  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      _fetch();
    });
    super.initState();
  }

  Future<void> _fetch() async {
    await ref.read(unitsListProvider.notifier).fetchUnits();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(unitsListProvider);
    if (state is LoadingViewState) {
      return const Center(child: LoaderWidget());
    }
    if (state is EmptyViewState) {
      return Center(
        child: TextWidget('empty_results'.tr()),
      );
    }
    if (state is LoadedViewState<List<DropDownEntity>>) {
      return Scrollbar(
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: state.data.length,
          padding:
              const EdgeInsets.symmetric(vertical: AppDimensions.kSizeSmall),
          itemBuilder: (context, index) {
            return InkWell(
              onTap: () {
                ref.watch(selectedUnitProvider.notifier).state =
                    state.data[index];
                context.popRoute();
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: AppDimensions.kSizeSmall,
                  horizontal: AppDimensions.kSizeMedium,
                ),
                child: TextWidget(
                  (context.locale.languageCode == 'en'
                          ? state.data[index].textEn ?? state.data[index].textAr
                          : state.data[index].textAr ??
                              state.data[index].textEn) ??
                      '',
                ),
              ),
            );
          },
        ),
      );
    }
    return const SizedBox();
  }
}
