import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/user/presentation/provider/delete_notification_token_provider.dart';
import 'package:mdd/features/user/presentation/provider/user_provider.dart';
import 'package:mdd/features/user/presentation/screens/user_profile_view.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/services/firebase_cloud_messaging/firebase_cloud_messaging.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

@RoutePage()
class UserProfileScreen extends ConsumerStatefulWidget {
  const UserProfileScreen({
    super.key,
  });

  @override
  ConsumerState<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends ConsumerState<UserProfileScreen> {
  @override
  Widget build(BuildContext context) {
    ref.listen(deleteNotificationTokenProvider, (previous, state) {
      if (state is LoadingViewState) {
        UiHelper.showLoadingDialog(context);
      }
      if (state is LoadedViewState<ResultModel>) {
        ref.read(userProvider.notifier).logout();
        Navigator.pop(context);
        context.router.replaceAll([const UserLoginRoute()]);
      }
      if (state is ErrorViewState) {
        Navigator.pop(context);
        UiHelper.showNotification('try_again'.tr());
      }
    });
    return Scaffold(
      appBar: const MainAppBar(
        showProfile: false,
      ),
      body: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(child: DetailsCustomBar(title: 'my_account'.tr())),
              Consumer(builder: (context, ref, child) {
                return CupertinoButton(
                    padding: const EdgeInsets.symmetric(
                      vertical: AppDimensions.kSizeSmall,
                      horizontal: AppDimensions.kSizeMedium,
                    ),
                    child: TextWidget(
                      'sign_out'.tr(),
                      color: AppColors.red,
                    ),
                    onPressed: () {
                      if (ref.read(firebaseMessagingProvider).token != null) {
                        ref
                            .read(deleteNotificationTokenProvider.notifier)
                            .deleteNotificationToken(
                                notificationToken:
                                    ref.read(firebaseMessagingProvider).token!);
                      } else {
                        context.router.replaceAll([const UserLoginRoute()]);
                      }
                    });
              }),
            ],
          ),
          const Expanded(
            child: SingleChildScrollView(child: UserProfileView()),
          ),
        ],
      ),
    );
  }
}
