import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/description_widget/description_card_item_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/core/widgets/title_item_widget.dart';
import 'package:mdd/features/organization/data/models/organizations_model.dart';
import 'package:mdd/features/organization/presentation/provider/organization_details_provider.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/presentation/screens/accept_quotation_screen.dart';
import 'package:mdd/features/quotations/presentation/screens/reject_reasons_screen.dart';
import 'package:mdd/features/quotations/presentation/widgets/product_item_widget.dart';
import 'package:mdd/features/quotations/presentation/widgets/quotation_item_widget.dart';
import 'package:mdd/features/quotations/presentation/widgets/quotation_status_reject_card_widget.dart';
import 'package:mdd/features/user/domain/entities/user_entity.dart';
import 'package:mdd/features/user/presentation/provider/user_provider.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/extensions.dart';

class QuotationDetailsTapView extends ConsumerStatefulWidget {
  const QuotationDetailsTapView({
    super.key,
    required this.quotation,
  });

  final QuotationsModel quotation;
  @override
  ConsumerState createState() => _QuotationDetailsTapViewState();
}

class _QuotationDetailsTapViewState
    extends ConsumerState<QuotationDetailsTapView> {
  late bool isCurrentApprover;

  @override
  void initState() {
    super.initState();
    final user = (ref.read(userProvider) as LoadedViewState<UserEntity>).data;
    widget.quotation.currentApprover == user.userID
        ? isCurrentApprover = true
        : isCurrentApprover = false;
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        children: [
          Expanded(
            child: Scrollbar(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    QuotationStatusRejectCardWidget(widget.quotation),
                    Consumer(builder: (context, ref, child) {
                      final state = ref.watch(orgDetailsProvider);

                      if (state is LoadedViewState<OrgsModel>) {
                        if (state.data.isAllowedApproveQuotation == false) {
                          return Card(
                            color: AppColors.red,
                            margin: const EdgeInsets.symmetric(
                              horizontal: AppDimensions.kSizeMedium,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(15),
                              child: Column(
                                children: [
                                  TextWidget(
                                    'the_request_cannot_be_accepted_Please_contact_the_supply_chain'
                                        .tr(),
                                    color: AppColors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  const SizedBox(
                                    height: AppDimensions.kSizeSmall,
                                  ),
                                  TextWidget(
                                    '${'employee_name'.tr()}: ${widget.quotation.employeeName}',
                                    color: AppColors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  TextWidget(
                                    '${'phone_number'.tr()}: ${widget.quotation.employeeMobile}',
                                    color: AppColors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ],
                              ),
                            ),
                          );
                        }
                      }
                      return const SizedBox();
                    }),
                    QuotationItemWidget(
                      quotation: widget.quotation,
                      enableOnTap: false,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: AppDimensions.kSizeMedium,
                      ),
                      child: TitleItemWidget(title: 'products'.tr()),
                    ),
                    ListView.builder(
                      itemCount: widget.quotation.items?.length,
                      shrinkWrap: true,
                      primary: false,
                      padding: const EdgeInsets.only(
                        bottom: AppDimensions.kSizeMedium,
                      ),
                      itemBuilder: (context, index) {
                        final product = widget.quotation.items?[index];
                        return Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppDimensions.kSizeMedium,
                          ),
                          child: ProductItemWidget(
                            unitName: product?.unitName ?? '',
                            productName: product?.productName ?? '',
                            price: product?.price == null
                                ? ''
                                : product!.price!
                                    .toString()
                                    .groupingSeparator(),
                            qty: product?.qty.toString() ?? '',
                          ),
                        );
                      },
                    ),
                    const Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppDimensions.kSizeMedium,
                      ),
                      child: Divider(color: AppColors.listTileTrailingTextGrey),
                    ),
                    DescriptionCardItemWidget(
                      title: 'total_products'.tr(),
                      trailing:
                          widget.quotation.price.toString().groupingSeparator(),
                    ),
                    DescriptionCardItemWidget(
                      title: 'service_fee'.tr(),
                      trailing: widget.quotation.serviceFees == null
                          ? ''
                          : widget.quotation.serviceFees
                              .toString()
                              .groupingSeparator(),
                    ),
                    if (widget.quotation.deliveryFees != 0.0)
                      DescriptionCardItemWidget(
                        title: 'delivery_fees'.tr(),
                        trailing: widget.quotation.deliveryFees == null
                            ? ''
                            : widget.quotation.deliveryFees
                                .toString()
                                .groupingSeparator(),
                      ),
                    if (widget.quotation.additionalFees != 0.0)
                      DescriptionCardItemWidget(
                        title: 'additional_fees'.tr(),
                        trailing: widget.quotation.additionalFees == null
                            ? ''
                            : widget.quotation.additionalFees
                                .toString()
                                .groupingSeparator(),
                      ),
                    DescriptionCardItemWidget(
                      title: '${'vat'.tr()} ${widget.quotation.vatPer}%',
                      trailing: widget.quotation.vat == null
                          ? ''
                          : widget.quotation.vat.toString().groupingSeparator(),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Card(
            color: AppColors.white,
            elevation: 10,
            margin: EdgeInsets.zero,
            child: Padding(
              padding: const EdgeInsets.symmetric(
                vertical: AppDimensions.kSizeXXLarge,
              ),
              child: Row(
                children: [
                  Row(
                    children: [
                      const SizedBox(
                        width: AppDimensions.kSizeMedium,
                      ),
                      TextWidget(
                        '${'total_amount'.tr()} :',
                        color: AppColors.listTileTitleTextGrey,
                      ),
                      TextWidget(
                        widget.quotation.total.toString().groupingSeparator(),
                      ),
                    ],
                  ),
                  const Spacer(),
                  Visibility(
                    visible: (widget.quotation.quotationStatus ==
                            QuotationsStatusType.submitted) &&
                        isCurrentApprover,
                    child: ButtonWidget(
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (_) => Dialog(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                AppDimensions.kMediumRadius,
                              ),
                            ),
                            insetPadding: const EdgeInsets.symmetric(
                              horizontal: AppDimensions.kSizeXXLarge,
                            ),
                            child: RejectReasonsScreen(widget.quotation),
                          ),
                        );
                      },
                      title: 'reject'.tr(),
                      backgroundColor: AppColors.red,
                    ),
                  ),
                  const SizedBox(width: AppDimensions.kSizeXSmall),
                  Consumer(builder: (context, ref, child) {
                    final state = ref.watch(orgDetailsProvider);

                    if (state is LoadedViewState<OrgsModel>) {
                      return Visibility(
                        visible: (widget.quotation.quotationStatus ==
                                QuotationsStatusType.submitted) &&
                            isCurrentApprover &&
                            state.data.isAllowedApproveQuotation == true,
                        child: ButtonWidget(
                          onPressed: () {
                            if (widget.quotation.initialApproval == true) {
                              showDialog(
                                context: context,
                                builder: (_) => Dialog(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(
                                      AppDimensions.kMediumRadius,
                                    ),
                                  ),
                                  insetPadding: const EdgeInsets.symmetric(
                                    horizontal: AppDimensions.kSizeXXLarge,
                                  ),
                                  child: AcceptApprovalQuotationScreen(
                                    widget.quotation,
                                  ),
                                ),
                              );
                            } else {
                              context
                                  .pushRoute(const QuotationAcceptanceRoute());
                            }
                          },
                          title: 'approve'.tr(),
                        ),
                      );
                    }
                    return const SizedBox();
                  }),
                  const SizedBox(width: AppDimensions.kSizeMedium),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
