import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/extensions.dart';

class CacheImageNetworkWidget extends StatelessWidget {
  final String imageUrl;
  final String? base64Image;
  final Color? imageColor;
  final double? height;
  final BoxFit? boxFit;

  const CacheImageNetworkWidget(
      {super.key,
      required this.imageUrl,
      this.base64Image,
      this.imageColor,
      this.height,
      this.boxFit});

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
        imageUrl: imageUrl,
        imageBuilder: (context, imageProvider) => Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                image: DecorationImage(
                  image: imageProvider,
                  fit: BoxFit.contain,
                ),
              ),
            ),
        placeholder: (context, url) => FittedBox(
              child: SvgPicture.asset(
                "assets/icons/avatar_icon.svg",
                height: height ?? AppDimensions.kSizeMedium,
              ),
            ),
        errorWidget: (context, url, error) => FittedBox(
              child: SvgPicture.asset(
                "assets/icons/avatar_icon.svg",
                height: height ?? AppDimensions.kSizeMedium,
              ),
            ),
        height: height ?? context.widthR(0.25),
        fit: boxFit ?? BoxFit.cover,
        color: imageColor);
  }
}
