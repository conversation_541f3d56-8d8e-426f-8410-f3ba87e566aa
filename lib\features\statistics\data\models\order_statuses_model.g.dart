// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_statuses_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderStatusesModel _$OrderStatusesModelFromJson(Map<String, dynamic> json) =>
    OrderStatusesModel(
      status: $enumDecode(_$OrderStatusTypeEnumMap, json['status']),
      statusName: json['statusName'] as String,
      ordersCount: json['ordersCount'] as int,
      ordersAmount: (json['ordersAmount'] as num).toDouble(),
    );

Map<String, dynamic> _$OrderStatusesModelToJson(OrderStatusesModel instance) =>
    <String, dynamic>{
      'status': _$OrderStatusTypeEnumMap[instance.status]!,
      'statusName': instance.statusName,
      'ordersCount': instance.ordersCount,
      'ordersAmount': instance.ordersAmount,
    };

const _$OrderStatusTypeEnumMap = {
  OrderStatusType.none: 0,
  OrderStatusType.created: 1,
  OrderStatusType.submitted: 2,
  OrderStatusType.underProcess: 3,
  OrderStatusType.quotationSubmitted: 4,
  OrderStatusType.quotationApproved: 5,
  OrderStatusType.quotationDeclient: 6,
  OrderStatusType.paymentWaiting: 7,
  OrderStatusType.paymentDone: 8,
  OrderStatusType.paymentApproved: 9,
  OrderStatusType.payemntDeclinet: 10,
  OrderStatusType.deliverProcess: 11,
  OrderStatusType.deliveryNoteSent: 12,
  OrderStatusType.completed: 13,
  OrderStatusType.closed: 14,
  OrderStatusType.canceled: 15,
  OrderStatusType.poWaitingForRequest: 16,
  OrderStatusType.ipoSubmitted: 17,
  OrderStatusType.ipoClosed: 18,
  OrderStatusType.quotationReturned: 19,
  OrderStatusType.quotationExpired: 20,
  OrderStatusType.quotationReNewed: 21,
  OrderStatusType.pricingFeeWaiting: 22,
  OrderStatusType.quotationReturnedAfterApproval: 23,
  OrderStatusType.ipoReturnd: 24,
  OrderStatusType.ipoRepoend: 25,
  OrderStatusType.waiting: 26,
  OrderStatusType.rejected: 27,
  OrderStatusType.preparingShipment: 28,
  OrderStatusType.ipoMissingDocs: 29,
  OrderStatusType.ipoInComplete: 30,
  OrderStatusType.invocing: 31,
};
