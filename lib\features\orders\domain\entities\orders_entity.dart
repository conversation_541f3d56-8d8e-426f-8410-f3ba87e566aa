import 'package:mdd/core/models/paginated_model.dart';
import 'package:mdd/features/attachments/data/models/attachments_model.dart';
import 'package:mdd/features/comments/data/models/comments_model.dart';
import 'package:mdd/features/orders/data/models/order_details_model.dart';
import 'package:mdd/features/quotations/data/models/approval_model.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/utils/enums.dart';

class OrdersEntity implements PaginatedModel {
  final String? orderID;
  final String? projectID;
  final String? projectName;
  final int? orderNo;
  final String? createdOn;
  final OrderStatusType? orderStatus;
  final OrderTypes? orderType;
  final String? orderTypeName;
  final String? organizationID;
  final String? organizationName;
  final String? employeeID;
  final String? employeeName;
  final String? customerID;
  final String? customerName;
  final String? cancelNote;
  final bool? isSAPAccepted;
  final String? sapFileContent;
  final String? sapFileContentType;
  final String? sapFileName;
  final String? sapAcceptedOn;
  final String? currentApprover;
  final String? currentApproverName;
  final List<OrderDetailsModel>? details;
  final List<QuotationsModel>? quotations;
  final List<AttachmentsModel>? attachments;
  final List<ApprovalModel>? approvals;
  final List<CommentsModel>? comments;

  OrdersEntity({
    this.orderID,
    this.projectID,
    this.projectName,
    this.orderNo,
    this.createdOn,
    this.orderStatus,
    this.orderType,
    this.orderTypeName,
    this.organizationID,
    this.organizationName,
    this.employeeID,
    this.employeeName,
    this.customerID,
    this.customerName,
    this.cancelNote,
    this.isSAPAccepted,
    this.sapFileContent,
    this.sapFileContentType,
    this.sapFileName,
    this.sapAcceptedOn,
    this.currentApprover,
    this.currentApproverName,
    this.details,
    this.quotations,
    this.attachments,
    this.approvals,
    this.comments,
  });
}
