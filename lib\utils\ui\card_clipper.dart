import 'package:flutter/material.dart';

class CardClipper extends CustomClipper<Path> {
  double punchRadius;
  bool top;
  CardClipper(this.punchRadius, {this.top = false});

  @override
  Path getClip(Size size) {
    final path = Path();
    path.lineTo(0.0, size.height);
    path.lineTo(size.width, size.height);
    path.lineTo(size.width, 0.0);
    if (top) {
      path.addOval(Rect.fromCircle(
          center: Offset(size.width / 2, 0), radius: punchRadius));
    } else {
      path.addOval(Rect.fromCircle(
          center: Offset(0, size.height / 2), radius: punchRadius));
    }

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => true;
}
