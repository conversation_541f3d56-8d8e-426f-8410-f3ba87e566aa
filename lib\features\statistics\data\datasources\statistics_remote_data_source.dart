import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/endPoints/end_points.dart';
import 'package:mdd/features/statistics/data/models/statistics_model.dart';
import 'package:mdd/features/statistics/domain/usecases/statistics.dart';
import 'package:mdd/services/dio_client.dart';

abstract class StatisticsRemoteDataSource {
  Future<StatisticsModel?> getStatistics(StatisticsParams params);
}

final statisticsRemoteDataSourceImpl = Provider<StatisticsRemoteDataSourceImpl>(
    (ref) => StatisticsRemoteDataSourceImpl(ref.watch(dioClientProvider)));

class StatisticsRemoteDataSourceImpl implements StatisticsRemoteDataSource {
  final DioClient _dioClient;

  StatisticsRemoteDataSourceImpl(this._dioClient);
  @override
  Future<StatisticsModel?> getStatistics(StatisticsParams params) async {
    final response = await _dioClient.dio.get(
      EndPoints.customerStatistics,
      queryParameters: {
        "startDate": params.startDate,
        "endDate": params.endDate,
      },
    );
    return StatisticsModel.fromJson(response.data['data']);
  }
}
