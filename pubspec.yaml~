name: mdd
description: A new Flutter project.

publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 2.0.2+42
# check UpgradeAlert widget

environment:
  sdk: '>=3.4.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # UI
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.10+1
  overlay_support: ^2.1.0
  loading_indicator: ^3.1.1
  intl: ^0.19.0
  animate_do: ^3.3.4
  easy_debounce: ^2.0.3
  cached_network_image: ^3.3.1
  bottom_picker: ^2.8.0
  app_popup_menu: ^1.0.0
  responsive_framework: ^1.4.0
  syncfusion_flutter_charts: ^26.1.42
  dropdown_search: ^5.0.6
  # MISC
  share_plus: ^7.0.2

  # Location
  google_maps_flutter: ^2.7.0
  map_picker: ^0.0.3
  geocoding: ^3.0.0
  geolocator: ^12.0.0

  # Routing
  auto_route: ^8.1.4

  #pickers
  file_picker: ^8.0.6

  # API
  dio: ^5.5.0+1
  json_annotation: ^4.9.0
  equatable: ^2.0.5
  dartz: ^0.10.1
  internet_connection_checker: ^1.0.0+1

  # functions
  timeago: ^3.7.0

  # Localization
  easy_localization: ^3.0.7

  # State Management
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5

  # Firebase
  firebase_core: ^3.9.0
  firebase_messaging: ^15.1.6
  flutter_local_notifications: ^18.0.1

  # generation
  uuid: ^4.4.2

  # device & app info
  device_info_plus: ^10.1.0
  package_info_plus: ^8.0.0
  upgrader: ^10.3.0
  version: ^3.0.2

  # data
  flutter_secure_storage: ^9.2.2
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.3
  path_provider: ^2.1.3
  open_filex: ^4.4.0
  url_launcher: ^6.3.0

  # Debugging and testing
  logging: ^1.2.0
  flutter_flavor: ^3.1.3
  sentry_flutter: ^8.4.0
  sentry_dio: ^8.4.0

  path: any
dev_dependencies:
  flutter_test:
    sdk: flutter

  custom_lint:
  riverpod_lint:

  flutter_lints: ^4.0.0
  auto_route_generator: ^8.0.0
  riverpod_generator: ^2.4.0
  build_runner: ^2.4.11
  change_app_package_name: ^1.3.0
  flutter_launcher_icons: ^0.13.1
  hive_generator: ^2.0.1
  json_serializable: ^6.8.0


flutter:
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/localization/
    - assets/images/
    - assets/icons/
    - config/

  fonts:
#    - family: Din
#      fonts:
#        - asset: assets/fonts/din-next-lt-w23-bold.ttf
#          weight: 700
#        - asset: assets/fonts/din-next-lt-w23-medium.ttf
#          weight: 500
#        - asset: assets/fonts/din-next-lt-w23-regular.ttf
#          weight: 400
#        - asset: assets/fonts/din-next-lt-w23-light.ttf
#          weight: 300
     - family: IBMPlexSansArabic
       fonts:
            - asset: assets/fonts/IBMPlexSansArabic-Bold.ttf
              weight: 700
            - asset: assets/fonts/IBMPlexSansArabic-SemiBold.ttf
              weight: 600
            - asset: assets/fonts/IBMPlexSansArabic-Medium.ttf
              weight: 500
            - asset: assets/fonts/IBMPlexSansArabic-Regular.ttf
              weight: 400
            - asset: assets/fonts/IBMPlexSansArabic-Light.ttf
              weight: 300
            - asset: assets/fonts/IBMPlexSansArabic-ExtraLight.ttf
              weight: 200
            - asset: assets/fonts/IBMPlexSansArabic-Thin.ttf
              weight: 100

flutter_icons:
  android: true
  ios: true
  image_path: "assets/images/appstore_logo.png"
  image_path_android: "assets/images/playstore_logo.png"
  image_path_ios: "assets/images/appstore_logo.png"
  adaptive_icon_background: #ffffff
  remove_alpha_ios: true
