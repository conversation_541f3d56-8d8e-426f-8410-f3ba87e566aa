import 'package:flutter/material.dart';
import 'package:mdd/core/widgets/subtitle_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/core/widgets/timeago_widget.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enums.dart';

class CommentsWidget extends StatelessWidget {
  final UserType commentUserType;
  final String content;
  final String timeAgo;
  final String senderName;
  const CommentsWidget({
    super.key,
    required this.commentUserType,
    required this.content,
    required this.timeAgo,
    required this.senderName,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Card(
            elevation: 0,
            color: commentUserType == UserType.customer
                ? AppColors.cardDetailsBackground
                : AppColors.purple,
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.kSizeMedium,
                vertical: AppDimensions.kSizeMedium,
              ),
              child: TextWidget(
                content,
                color: commentUserType == UserType.customer
                    ? AppColors.disabledColor1
                    : AppColors.white,
                textAlign: TextAlign.start,
              ),
            )),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.kSizeMedium,
              ),
              child: SubtitleWidget(senderName),
            ),
            TimeagoWidget(
              createdOn: timeAgo,
            ),
          ],
        ),
        const SizedBox(
          height: AppDimensions.kSizeSmall,
        ),
      ],
    );
  }
}
