import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/statistics/data/models/monthly_summary_model.dart';
import 'package:mdd/features/statistics/data/models/order_statuses_model.dart';
import 'package:mdd/features/statistics/data/models/quotation_statuses_model.dart';
import 'package:mdd/features/statistics/domain/entity/statisctics_entity.dart';

part 'statistics_model.g.dart';

@JsonSerializable()
class StatisticsModel extends StatisticsEntity {
  StatisticsModel({
    required super.quotationStatuses,
    required super.orderStatuses,
    required super.monthlySummary,
  });

  factory StatisticsModel.fromJson(Map<String, dynamic> json) =>
      _$StatisticsModelFromJson(json);

  Map<String, dynamic> toJson() => _$StatisticsModelToJson(this);
}
