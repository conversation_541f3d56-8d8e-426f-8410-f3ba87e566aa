import 'package:mdd/core/models/paginated_model.dart';
import 'package:mdd/utils/enums.dart';

class CustomersEntity implements PaginatedModel {
  String? customerID;
  String? fullName;
  String? mobile;
  String? email;
  String? userName;
  String? address;
  String? password;
  String? language;
  String? organizationID;
  String? organizationName;
  String? cityID;
  String? cityName;
  String? jobtitleID;
  String? jobtitleName;
  String? departmentID;
  String? departmentName;
  bool? isEmailConfrimd;
  bool? isActive;
  bool? isPasswordNeedToChange;
  DateTime? lastPasswordChage;
  bool? isMfaEnabled;
  String? mfaCode;
  String? image;
  String? imageFullPath;
  CustomerType? type;
  String? typeName;
  double? creditBalance;
  bool? isCredit;
  bool? isSMSNotificationActive;
  bool? isEmailNotificationActive;

  CustomersEntity({
    this.customerID,
    required this.fullName,
    required this.mobile,
    required this.email,
    this.userName,
    required this.address,
    this.password,
    required this.language,
    this.organizationID,
    this.organizationName,
    required this.cityID,
    this.cityName,
    this.jobtitleID,
    this.jobtitleName,
    this.departmentID,
    this.departmentName,
    this.isEmailConfrimd,
    this.isActive,
    this.isPasswordNeedToChange,
    this.lastPasswordChage,
    this.isMfaEnabled,
    this.mfaCode,
    this.image,
    this.imageFullPath,
    required this.type,
    this.typeName,
    this.creditBalance,
    this.isCredit,
    this.isSMSNotificationActive,
    this.isEmailNotificationActive,
  });

  CustomersEntity copyWith({
    String? customerID,
    String? fullName,
    String? mobile,
    String? email,
    String? userName,
    String? address,
    String? password,
    String? language,
    String? organizationID,
    String? organizationName,
    String? cityID,
    String? cityName,
    String? jobtitleID,
    String? jobtitleName,
    String? departmentID,
    String? departmentName,
    bool? isEmailConfrimd,
    bool? isActive,
    bool? isPasswordNeedToChange,
    DateTime? lastPasswordChage,
    bool? isMfaEnabled,
    String? mfaCode,
    String? image,
    String? imageFullPath,
    CustomerType? type,
    String? typeName,
    double? creditBalance,
    bool? isCredit,
    bool? isSMSNotificationActive,
    bool? isEmailNotificationActive,
  }) {
    return CustomersEntity(
      customerID: customerID ?? this.customerID,
      fullName: fullName ?? this.fullName,
      mobile: mobile ?? this.mobile,
      email: email ?? this.email,
      userName: userName ?? this.userName,
      address: address ?? this.address,
      password: password ?? this.password,
      language: language ?? this.language,
      organizationID: organizationID ?? this.organizationID,
      organizationName: organizationName ?? this.organizationName,
      cityID: cityID ?? this.cityID,
      cityName: cityName ?? this.cityName,
      jobtitleID: jobtitleID ?? this.jobtitleID,
      jobtitleName: jobtitleName ?? this.jobtitleName,
      departmentID: departmentID ?? this.departmentID,
      departmentName: departmentName ?? this.departmentName,
      isEmailConfrimd: isEmailConfrimd ?? this.isEmailConfrimd,
      isActive: isActive ?? this.isActive,
      isPasswordNeedToChange:
      isPasswordNeedToChange ?? this.isPasswordNeedToChange,
      lastPasswordChage: lastPasswordChage ?? this.lastPasswordChage,
      isMfaEnabled: isMfaEnabled ?? this.isMfaEnabled,
      mfaCode: mfaCode ?? this.mfaCode,
      image: image ?? this.image,
      imageFullPath: imageFullPath ?? this.imageFullPath,
      type: type ?? this.type,
      typeName: typeName ?? this.typeName,
      creditBalance: creditBalance ?? this.creditBalance,
      isCredit: isCredit ?? this.isCredit,
      isSMSNotificationActive:
      isSMSNotificationActive ?? this.isSMSNotificationActive,
      isEmailNotificationActive:
      isEmailNotificationActive ?? this.isEmailNotificationActive,
    );
  }}
