import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/form_field_widget.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/features/dropdowns/presentation/providers/cities_provider.dart';
import 'package:mdd/features/dropdowns/presentation/widgets/cities_list_drop_down_widget.dart';
import 'package:mdd/features/location_picker/presentation/provider/selected_location_provider.dart';
import 'package:mdd/features/organization/data/models/organizations_model.dart';
import 'package:mdd/features/organization/presentation/provider/organization_details_provider.dart';
import 'package:mdd/features/organization/presentation/provider/update_organization_provider.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/constants/regex_constants.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

@RoutePage()
class UpdateOrgScreen extends ConsumerStatefulWidget {
  const UpdateOrgScreen({
    super.key,
    required this.org,
  });

  final OrgsModel org;

  @override
  ConsumerState createState() => _UpdateOrgScreenState();
}

class _UpdateOrgScreenState extends ConsumerState<UpdateOrgScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _crController = TextEditingController();
  final _emailController = TextEditingController();
  final _landlineController = TextEditingController();
  final _zipCodeController = TextEditingController();
  final _websiteController = TextEditingController();
  final _vatNumberController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _nameController.text = widget.org.name ?? '';
    _crController.text = widget.org.cr ?? '';
    _emailController.text = widget.org.email ?? '';
    _landlineController.text = widget.org.landLine ?? '';
    _zipCodeController.text = widget.org.zipCode ?? '';
    _websiteController.text = widget.org.website ?? '';
    _vatNumberController.text = widget.org.vatNumber ?? '';
  }

  @override
  void dispose() {
    _nameController.dispose();
    _crController.dispose();
    _emailController.dispose();
    _landlineController.dispose();
    _zipCodeController.dispose();
    _websiteController.dispose();
    _vatNumberController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(updateOrgProvider, (previous, state) async {
      if (state is LoadingViewState) {
        UiHelper.showLoadingDialog(context);
      } else if (state is LoadedViewState<ResultModel>) {
        Navigator.pop(context);
        Navigator.pop(context);
        UiHelper.showNotification(
          'edit_org_success'.tr(),
          notificationType: NotificationType.success,
        );
        await ref.read(orgDetailsProvider.notifier).fetchOrgDetails();
      } else if (state is ErrorViewState) {
        Navigator.pop(context);
        UiHelper.showNotification(state.errorMessage);
      }
    });
    return Scaffold(
      appBar: const MainAppBar(),
      body: SingleChildScrollView(
        child: SafeArea(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                DetailsCustomBar(title: 'edit_organization'.tr()),
                FormFieldWidget(
                  controller: _nameController,
                  fillColor: AppColors.cardDetailsBackground,
                  borderColor: AppColors.cardDetailsBackground,
                  textInputType: TextInputType.text,
                  icon: 'assets/icons/name_icon.svg',
                  validator: (value) {
                    if (value?.isEmpty ?? false) {
                      return 'enter_valid_name'.tr();
                    }
                    return null;
                  },
                  hintText: 'name'.tr(),
                ),
                FormFieldWidget(
                  controller: _emailController,
                  fillColor: AppColors.cardDetailsBackground,
                  borderColor: AppColors.cardDetailsBackground,
                  textInputType: TextInputType.emailAddress,
                  validator: (value) {
                    if (value != null &&
                        !RegexConstants.kEmailRegex.hasMatch(value)) {
                      return 'enter_valid_email'.tr();
                    }
                    return null;
                  },
                  icon: 'assets/icons/email_icon.svg',
                  hintText: 'email'.tr(),
                ),
                FormFieldWidget(
                  controller: _landlineController,
                  fillColor: AppColors.cardDetailsBackground,
                  borderColor: AppColors.cardDetailsBackground,
                  textInputType: TextInputType.phone,
                  icon: 'assets/icons/phone_icon.svg',
                  validator: (value) {
                    if (value != null && value.length == 10) {
                      return null;
                    }
                    return 'enter_valid_landLine'.tr();
                  },
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(10),
                  ],
                  hintText: 'landLine'.tr(),
                ),
                CitiesListDropDownWidget(
                  value:
                      ref.watch(selectedCityProvider)?.id ?? widget.org.cityID,
                ),
                FormFieldWidget(
                  controller: TextEditingController(
                    text: ref.watch(selectedLocationProvider)?.address ??
                        widget.org.street,
                  ),
                  onTap: () => context.pushRoute(const LocationPickerRoute()),
                  fillColor: AppColors.cardDetailsBackground,
                  borderColor: AppColors.cardDetailsBackground,
                  icon: 'assets/icons/location_icon.svg',
                  validator: (value) {
                    if (value?.isEmpty ?? false) {
                      return 'address_validate'.tr();
                    }
                    return null;
                  },
                  hintText: 'address'.tr(),
                ),
                FormFieldWidget(
                  controller: _crController,
                  fillColor: AppColors.cardDetailsBackground,
                  borderColor: AppColors.cardDetailsBackground,
                  textInputType: TextInputType.number,
                  validator: (value) {
                    if (value?.isEmpty ?? false) {
                      return 'enter_valid_name'.tr();
                    }
                    return null;
                  },
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                      RegexConstants.kNumberRegex,
                    )
                  ],
                  hintText: 'commercial_registration_no'.tr(),
                ),
                FormFieldWidget(
                  controller: _zipCodeController,
                  fillColor: AppColors.cardDetailsBackground,
                  borderColor: AppColors.cardDetailsBackground,
                  textInputType: TextInputType.number,
                  validator: (value) {
                    if (value?.isEmpty ?? false) {
                      return 'enter_valid_zipCode'.tr();
                    }
                    return null;
                  },
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                        RegexConstants.kNumberRegex)
                  ],
                  hintText: 'zipCode'.tr(),
                ),
                FormFieldWidget(
                  controller: _websiteController,
                  fillColor: AppColors.cardDetailsBackground,
                  borderColor: AppColors.cardDetailsBackground,
                  textInputType: TextInputType.url,
                  validator: (value) {
                    if (value?.isEmpty ?? false) {
                      return 'enter_valid_website'.tr();
                    }
                    return null;
                  },
                  hintText: 'website'.tr(),
                ),
                FormFieldWidget(
                  controller: _vatNumberController,
                  fillColor: AppColors.cardDetailsBackground,
                  borderColor: AppColors.cardDetailsBackground,
                  textInputType: const TextInputType.numberWithOptions(
                    decimal: true,
                  ),
                  validator: (value) {
                    if (value?.isEmpty ?? false) {
                      return 'enter_valid_vatNumber'.tr();
                    }
                    return null;
                  },
                  hintText: 'vatNumber'.tr(),
                ),
                const SizedBox(height: AppDimensions.kSizeMedium2),
                ButtonWidget(
                  onPressed: _callEditOrg,
                  title: 'edit'.tr(),
                  horizontalPadding: AppDimensions.kSizeXXLarge,
                ),
                const SizedBox(height: AppDimensions.kSizeMedium2),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _callEditOrg() async {
    if (_formKey.currentState!.validate()) {
      await ref.read(updateOrgProvider.notifier).updateOrg(
            widget.org.copyWith(
              name: _nameController.text,
              email: _emailController.text,
              cityID: ref.read(selectedCityProvider)?.id,
              street:
                  ref.read(selectedLocationProvider.notifier).state?.address,
              cr: _crController.text.arabicNumberConverter(),
              landLine: _landlineController.text.arabicNumberConverter(),
              zipCode: _zipCodeController.text.arabicNumberConverter(),
              website: _websiteController.text,
              vatNumber: _vatNumberController.text.arabicNumberConverter(),
            ),
          );
    }
  }
}
