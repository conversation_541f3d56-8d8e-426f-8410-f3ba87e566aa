import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/search_bar_widget.dart';
import 'package:mdd/core/widgets/tap_menu_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/orders/presentation/provider/order_text_field_provider.dart';
import 'package:mdd/features/orders/presentation/provider/orders_provider.dart';
import 'package:mdd/features/orders/presentation/screens/orders_view.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enum_extensions.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/extensions.dart';

@RoutePage()
class OrdersScreen extends ConsumerStatefulWidget {
  const OrdersScreen({
    super.key,
  });

  @override
  ConsumerState createState() => _OrdersScreenState();
}

class _OrdersScreenState extends ConsumerState<OrdersScreen> {
  void _fetch() {
    ref.read(ordersProvider.notifier).fetchGetOrders();
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final orderStatusState = ref.watch(orderStatusProvider.state);
    final orderSearchController = ref.watch(orderSearchControllerProvider);
    final orderProvider = ref.read(ordersProvider.notifier);
    return Scaffold(
      floatingActionButton: FloatingActionButton.extended(
          heroTag: 'floating_action_orders_screen',
          onPressed: () => context.pushRoute(CreateOrderRoute()),
          label: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.kSizeSmall,
            ),
            child: TextWidget(
              '+ ${'new_order'.tr()}',
              color: AppColors.white,
            ),
          )),
      body: Column(
        children: [
          const SizedBox(
            height: AppDimensions.kSizeLarge,
          ),
          SearchBarWidget(
            showClearIcon: ref.watch(orderSuffixIconProvider),
            onPressedSearch: () {
              if (orderSearchController.text.isNotEmpty) {
                orderProvider.resetPagination();
                _fetch();
              }
            },
            onPressedClear: () {
              ref.read(orderSuffixIconProvider.notifier).state = false;
              orderSearchController.clear();
              orderProvider.resetPagination();
              _fetch();
            },
            textController: orderSearchController,
            onChange: (String value) {
              if (value.isEmpty) {
                ref.read(orderSuffixIconProvider.notifier).state = false;
                ref.read(ordersProvider.notifier).resetPagination();
                _fetch();
                return;
              }
              ref.read(orderSuffixIconProvider.notifier).state = true;
            },
          ),
          SizedBox(
            height: context.heightR(.05),
            child: ListView(
              padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.kSizeMedium),
              scrollDirection: Axis.horizontal,
              children: [
                TapMenuWidget(
                  isActive: orderStatusState.state == null,
                  tabTitle: 'all_status'.tr(),
                  onTap: () {
                    if (orderStatusState.state != null) {
                      orderStatusState.state = null;
                      _fetch();
                    }
                  },
                ),
                TapMenuWidget(
                  isActive: orderStatusState.state == OrderStatusType.created,
                  tabTitle: OrderStatusType.created.translatedName,
                  onTap: () {
                    if (orderStatusState.state != OrderStatusType.created) {
                      orderStatusState.state = OrderStatusType.created;
                      _fetch();
                    }
                  },
                ),
                TapMenuWidget(
                  isActive: orderStatusState.state == OrderStatusType.submitted,
                  tabTitle: OrderStatusType.submitted.translatedName,
                  onTap: () {
                    if (orderStatusState.state != OrderStatusType.submitted) {
                      orderStatusState.state = OrderStatusType.submitted;
                      _fetch();
                    }
                  },
                ),
                TapMenuWidget(
                  isActive: orderStatusState.state == OrderStatusType.completed,
                  tabTitle: OrderStatusType.completed.translatedName,
                  onTap: () {
                    if (orderStatusState.state != OrderStatusType.completed) {
                      orderStatusState.state = OrderStatusType.completed;
                      _fetch();
                    }
                  },
                ),
                TapMenuWidget(
                  isActive:
                      orderStatusState.state == OrderStatusType.underProcess,
                  tabTitle: OrderStatusType.underProcess.translatedName,
                  onTap: () {
                    if (orderStatusState.state !=
                        OrderStatusType.underProcess) {
                      orderStatusState.state = OrderStatusType.underProcess;
                      _fetch();
                    }
                  },
                ),
                TapMenuWidget(
                  isActive: orderStatusState.state ==
                      OrderStatusType.quotationSubmitted,
                  tabTitle: OrderStatusType.quotationSubmitted.translatedName,
                  onTap: () {
                    if (orderStatusState.state !=
                        OrderStatusType.quotationSubmitted) {
                      orderStatusState.state =
                          OrderStatusType.quotationSubmitted;
                      _fetch();
                    }
                  },
                ),
                TapMenuWidget(
                  isActive: orderStatusState.state ==
                      OrderStatusType.quotationApproved,
                  tabTitle: OrderStatusType.quotationApproved.translatedName,
                  onTap: () {
                    if (orderStatusState.state !=
                        OrderStatusType.quotationApproved) {
                      orderStatusState.state =
                          OrderStatusType.quotationApproved;
                      _fetch();
                    }
                  },
                ),
                TapMenuWidget(
                  isActive: orderStatusState.state ==
                      OrderStatusType.quotationDeclient,
                  tabTitle: OrderStatusType.quotationDeclient.translatedName,
                  onTap: () {
                    if (orderStatusState.state !=
                        OrderStatusType.quotationDeclient) {
                      orderStatusState.state =
                          OrderStatusType.quotationDeclient;
                      _fetch();
                    }
                  },
                ),
                TapMenuWidget(
                  isActive:
                      orderStatusState.state == OrderStatusType.paymentWaiting,
                  tabTitle: OrderStatusType.paymentWaiting.translatedName,
                  onTap: () {
                    if (orderStatusState.state !=
                        OrderStatusType.paymentWaiting) {
                      orderStatusState.state = OrderStatusType.paymentWaiting;
                      _fetch();
                    }
                  },
                ),
                TapMenuWidget(
                  isActive:
                      orderStatusState.state == OrderStatusType.deliverProcess,
                  tabTitle: OrderStatusType.deliverProcess.translatedName,
                  onTap: () {
                    if (orderStatusState.state !=
                        OrderStatusType.deliverProcess) {
                      orderStatusState.state = OrderStatusType.deliverProcess;
                      _fetch();
                    }
                  },
                ),
                TapMenuWidget(
                  isActive: orderStatusState.state == OrderStatusType.rejected,
                  tabTitle: OrderStatusType.rejected.translatedName,
                  onTap: () {
                    if (orderStatusState.state != OrderStatusType.rejected) {
                      orderStatusState.state = OrderStatusType.rejected;
                      _fetch();
                    }
                  },
                ),
                TapMenuWidget(
                  isActive: orderStatusState.state == OrderStatusType.invocing,
                  tabTitle: OrderStatusType.invocing.translatedName,
                  onTap: () {
                    if (orderStatusState.state != OrderStatusType.invocing) {
                      orderStatusState.state = OrderStatusType.invocing;
                      _fetch();
                    }
                  },
                ),
              ],
            ),
          ),
          const Expanded(child: OrdersView()),
        ],
      ),
    );
  }
}
