import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/ip_model.dart';
import 'package:mdd/services/package_info.dart';
import 'package:mdd/utils/sentry_reporter.dart';
import 'package:package_info_plus/package_info_plus.dart';

final deviceInfoRepoProvider = Provider(
  (ref) => DeviceInfoRepo(
    ref.watch(packageInfoProvider),
  ),
);

class DeviceInfoRepo {
  late final AndroidDeviceInfo _androidInfo;
  late final IosDeviceInfo _iosInfo;
  final PackageInfo? _packageInfo;
  IpModel? ipInfo;

  DeviceInfoRepo(this._packageInfo);

  Future<void> init() async {
    await _getDeviceInfo();
    await _getIpInfo();
  }

  Future<void> _getDeviceInfo() async {
    if (Platform.isAndroid) {
      _androidInfo = await DeviceInfoPlugin().androidInfo;
    } else if (Platform.isIOS) {
      _iosInfo = await DeviceInfoPlugin().iosInfo;
    }
  }

  Future<void> _getIpInfo() async {
    try {
      final response = await Dio(
        BaseOptions(
          connectTimeout: const Duration(seconds: 30),
          receiveTimeout: const Duration(seconds: 20),
          sendTimeout: const Duration(seconds: 20),
        ),
      ).get('https://ipinfo.io/?token=e24c8b75ca365f');
      if (response.statusCode == 200) ipInfo = IpModel.fromJson(response.data);
    } catch (e, stackTrace) {
      SentryReporter.genericThrow(
        "Error getting IP info $e",
        stackTrace: stackTrace,
      );
    }
  }

  Map<String, dynamic> get deviceInfoHeaders {
    return {
      'appVersion': _packageInfo?.version,
      'deviceID':
          Platform.isAndroid ? _androidInfo.id : _iosInfo.identifierForVendor,
      'ipAddress': ipInfo?.ip ?? '',
      'ipOrg': ipInfo?.org ?? '',
      'ipLocation': "${ipInfo?.city ?? ''}, ${ipInfo?.region ?? ''}",
      'ipTimezone': ipInfo?.timezone ?? '',
      'platformType': Platform.isAndroid ? '2' : '1',
      'device': Platform.isAndroid
          ? '${_androidInfo.manufacturer}-${_androidInfo.brand}, ${_androidInfo.model}, (${_androidInfo.version.release})'
          : '${_iosInfo.name}, ${_iosInfo.model}, (${_iosInfo.systemVersion})',
    };
  }
}
