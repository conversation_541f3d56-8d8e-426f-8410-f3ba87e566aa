import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/core/models/result_model.dart';

part 'base_response.g.dart';

@JsonSerializable()
class BaseResponse {
  final int? totalRecords;
  final ResultModel? result;

  BaseResponse({
    required this.totalRecords,
    required this.result,
  });

  factory BaseResponse.fromJson(Map<String, dynamic> json) =>
      _$BaseResponseFromJson(json);

  Map<String, dynamic> toJson() => _$BaseResponseToJson(this);
}
