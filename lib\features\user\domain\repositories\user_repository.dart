import 'package:dartz/dartz.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/customer/data/models/customers_model.dart';
import 'package:mdd/features/user/domain/entities/user_entity.dart';
import 'package:mdd/features/user/domain/usecases/notification_token.dart';
import 'package:mdd/features/user/domain/usecases/update_password.dart';
import 'package:mdd/features/user/domain/usecases/update_profile.dart';

abstract class UserRepository {
  Future<Either<Failure, UserEntity>> login(String userName, String password);
  Future<Either<Failure, CustomersModel>> getProfile(NoParams params);
  Future<Either<Failure, ResultModel>> updateProfile(
      UpdateProfileParams params);
  Future<Either<Failure, ResultModel>> updatePassword(
      UpdatePasswordParams params);
  Future<Either<Failure, ResultModel>> addNotificationToken(
      NotificationTokenParams params);
  Future<Either<Failure, ResultModel>> deleteNotificationToken(
      String notificationToken);
}
