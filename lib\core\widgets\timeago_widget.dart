import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:mdd/core/widgets/subtitle_widget.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/helper_functions.dart';

class TimeagoWidget extends StatelessWidget {
  final String createdOn;
  const TimeagoWidget({super.key, required this.createdOn});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.kSizeMedium,
      ),
      child: SubtitleWidget(
        HelperFunctions.getTimeAgo(createdOn, context.locale.languageCode),
        textAlign: TextAlign.end,
      ),
    );
  }
}
