import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/description_widget/card_item_widget.dart';
import 'package:mdd/core/widgets/description_widget/description_card_item_widget.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/customer/data/models/customers_model.dart';
import 'package:mdd/features/customer/presentation/provider/customers_provider.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/utils/constants/constants.dart';
import 'package:mdd/utils/constants/keys.dart';
import 'package:mdd/utils/enum_extensions.dart';
import 'package:mdd/utils/extensions.dart';

class CustomersView extends ConsumerWidget {
  const CustomersView({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Consumer(builder: (context, ref, child) {
      final state = ref.watch(customersProvider);

      if (state is LoadingViewState) {
        return const LoaderWidget();
      }
      if (state is ErrorViewState) {
        return Text(state.errorMessage);
      }
      if (state is LoadedViewState<List<CustomersModel>>) {
        final provider = ref.read(customersProvider.notifier);
        return ListView.builder(
          itemCount: state.data.length + 1,
          key: const PageStorageKey(AppKeys.customerListKey),
          padding: EdgeInsets.only(
            bottom: context.mediaQuery.padding.bottom + 70,
          ),
          itemBuilder: (BuildContext context, int index) {
            if (index == state.data.length) {
              if (provider.hasMoreData &&
                  state.data.length >= AppConstants.paginationLimit) {
                provider.loadMore();
                return const Padding(
                  padding: EdgeInsets.all(10),
                  child: SizedBox(
                    child: LoaderWidget(),
                  ),
                );
              } else {
                return const SizedBox();
              }
            }
            return CardItemsWidget(
              title: state.data[index].fullName ?? '',
              subtitle: state.data[index].type?.translatedName ?? '',
              image: 'assets/images/customers_image.png',
              imageBackground: AppColors.blueCuriousCard,
              menuItems: [
                PopupMenuItem(
                  value: 1,
                  child: TextWidget('edit'.tr()),
                ),
              ],
              onSelected: (value) {
                if (value == 1) {
                  context.pushRoute(
                    EditCustomerRoute(customer: state.data[index]),
                  );
                }
              },
              descriptionWidget: [
                DescriptionCardItemWidget(
                  title: 'city'.tr(),
                  trailing: state.data[index].cityName ?? '',
                ),
                DescriptionCardItemWidget(
                  title: 'phone_number'.tr(),
                  trailing: state.data[index].mobile ?? '',
                ),
                DescriptionCardItemWidget(
                  title: 'email'.tr(),
                  trailing: state.data[index].email ?? '',
                ),
              ],
            );
          },
        );
      }
      return const SizedBox();
    });
  }
}
