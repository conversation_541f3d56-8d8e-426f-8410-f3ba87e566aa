import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/endPoints/end_points.dart';
import 'package:mdd/features/attachments/domain/entites/attachments_entity.dart';
import 'package:mdd/features/invoices/data/models/invoices_model.dart';
import 'package:mdd/features/invoices/domain/usecases/invoice_file.dart';
import 'package:mdd/features/invoices/domain/usecases/invoices.dart';
import 'package:mdd/services/dio_client.dart';
import 'package:mdd/utils/constants/constants.dart';

abstract class InvoicesRemoteDataSource {
  Future<List<InvoicesModel>> getInvoices(InvoicesParams params);
  Future<AttachmentsEntity> getInvoiceById(InvoiceFileParams params);
}

final invoicesRemoteDataSourceImpl = Provider<InvoicesRemoteDataSourceImpl>(
  (ref) => InvoicesRemoteDataSourceImpl(ref.watch(dioClientProvider)),
);

class InvoicesRemoteDataSourceImpl implements InvoicesRemoteDataSource {
  final DioClient _dioClient;

  InvoicesRemoteDataSourceImpl(this._dioClient);
  @override
  Future<List<InvoicesModel>> getInvoices(InvoicesParams params) async {
    final response = await _dioClient.dio.get(
      EndPoints.invoices,
      queryParameters: {
        'limit': AppConstants.paginationLimit,
        'page': params.page,
        if (params.invoiceStatus != null) 'status': params.invoiceStatus,
        if (params.invoiceNo?.isNotEmpty ?? false) 'find': params.invoiceNo,
      },
    );
    return (response.data['data'] as List)
        .map((invoice) => InvoicesModel.fromJson(invoice))
        .toList();
  }

  @override
  Future<AttachmentsEntity> getInvoiceById(InvoiceFileParams params) async {
    final re = await _dioClient.dio.get("${EndPoints.invoicePDF}/${params.id}");
    return AttachmentsEntity(
      fileContent: re.data['data'],
      fileName: "Invoice-${params.id}.pdf",
      orderAttachmentId: params.id,
    );
  }
}
