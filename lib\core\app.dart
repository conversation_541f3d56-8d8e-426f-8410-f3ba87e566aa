import 'dart:developer';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_flavor/flutter_flavor.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/routes/app_router.dart';
import 'package:mdd/services/firebase_cloud_messaging/firebase_cloud_messaging.dart';
import 'package:mdd/theme/app_theme.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:responsive_framework/responsive_framework.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

final appRouterProvider = Provider<AppRouter>((ref) => AppRouter());

class App extends ConsumerStatefulWidget {
  const App({
    super.key,
  });

  @override
  ConsumerState<App> createState() => _AppState();
}

class _AppState extends ConsumerState<App> {
  @override
  void initState() {
    super.initState();
    ref.read(firebaseMessagingProvider).registerNotification();
  }

  @override
  Widget build(BuildContext context) {
    log(FlavorConfig.instance.name ?? "No Flavor", name: "Flavor");
    return OverlaySupport(
      child: EasyLocalization(
        supportedLocales: const [Locale('ar'), Locale('en')],
        path: 'assets/localization',
        saveLocale: true,
        fallbackLocale: const Locale('ar'),
        child: Builder(
          builder: (context) => GestureDetector(
            onTap: () {
              context.unFocusRequest();
            },
            child: Consumer(builder: (context, ref, _) {
              final router = ref.read(appRouterProvider);
              //* Watch app event bus provider to keep it alive as long as the app is on
              return MaterialApp.router(
                // showSemanticsDebugger: true,
                title: "MDD",
                localizationsDelegates: context.localizationDelegates,
                supportedLocales: context.supportedLocales,
                locale: context.locale,
                builder: (context, navigator) {
                  // Update scale for text
                  final scale = context.mediaQuery.textScaler.clamp(
                    minScaleFactor: 0.8,
                    maxScaleFactor: 1.0,
                  );
                  return Builder(
                    builder: (context) {
                      return ResponsiveBreakpoints.builder(
                        child: MediaQuery(
                          data: context.mediaQuery.copyWith(textScaler: scale),
                          child: MDDTheme(
                            navigator: navigator,
                          ),
                        ),
                        breakpoints: [
                          const Breakpoint(start: 0, end: 450, name: MOBILE),
                          const Breakpoint(start: 451, end: 800, name: TABLET),
                          const Breakpoint(
                              start: 801, end: 1920, name: DESKTOP),
                        ],
                      );
                    },
                  );
                },
                routerDelegate: AutoRouterDelegate(
                  router,
                  navigatorObservers: () => [SentryNavigatorObserver()],
                ),
                routeInformationParser: router.defaultRouteParser(),
              );
            }),
          ),
        ),
      ),
    );
  }
}
