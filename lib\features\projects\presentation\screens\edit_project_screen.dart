import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/form_field_widget.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/customer/presentation/screens/add_customer_dialog.dart';
import 'package:mdd/features/dropdowns/presentation/providers/customers_list_provider.dart';
import 'package:mdd/features/dropdowns/presentation/widgets/customers_list_drop_down_widget.dart';
import 'package:mdd/features/location_picker/presentation/provider/selected_location_provider.dart';
import 'package:mdd/features/projects/data/models/projects_model.dart';
import 'package:mdd/features/projects/presentation/provider/projects_provider.dart';
import 'package:mdd/features/projects/presentation/provider/update_project.provider.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

@RoutePage()
class EditProjectScreen extends ConsumerStatefulWidget {
  const EditProjectScreen(
    this.project, {
    super.key,
  });

  final ProjectsModel project;
  @override
  ConsumerState createState() => _EditProjectScreenState();
}

class _EditProjectScreenState extends ConsumerState<EditProjectScreen> {
  final _projectNameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    _projectNameController.text = widget.project.name ?? '';
    _descriptionController.text = widget.project.description ?? '';
    super.initState();
  }

  @override
  void dispose() {
    _projectNameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(updateProjectProvider, (previous, state) {
      if (state is LoadingViewState) {
        return UiHelper.showLoadingDialog(context);
      }
      if (state is LoadedViewState<ResultModel>) {
        Navigator.pop(context);
        Navigator.pop(context);
        ref.read(projectsProvider.notifier).resetState();
        ref.read(projectsProvider.notifier).fetchProjects();
        UiHelper.showNotification(
          'edit_project_success'.tr(),
          notificationType: NotificationType.success,
        );
      }
      if (state is ErrorViewState) {
        Navigator.pop(context);
        UiHelper.showNotification('edit_project_failure'.tr());
      }
    });
    return Scaffold(
      appBar: const MainAppBar(),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DetailsCustomBar(title: 'edit_project'.tr()),
              FormFieldWidget(
                controller: _projectNameController,
                hintText: 'project_name'.tr(),
                fillColor: AppColors.cardDetailsBackground,
                borderColor: AppColors.cardDetailsBackground,
                textInputType: TextInputType.text,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    return null;
                  }
                  return 'project_name_validate'.tr();
                },
              ),
              FormFieldWidget(
                controller: TextEditingController(
                  text: ref.watch(selectedLocationProvider)?.address ??
                      widget.project.location,
                ),
                readOnly: true,
                onTap: () => context.pushRoute(const LocationPickerRoute()),
                fillColor: AppColors.cardDetailsBackground,
                borderColor: AppColors.cardDetailsBackground,
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'enter_valid_location'.tr();
                  }
                  return null;
                },
                hintText: 'location'.tr(),
              ),
              CustomersListDropDownWidget(
                value: widget.project.customerID,
              ),
              CupertinoButton(
                child: TextWidget(
                  'add_a_contact'.tr(),
                  color: AppColors.purple,
                ),
                onPressed: () => showDialog(
                  context: context,
                  builder: (_) => Dialog(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppDimensions.kMediumRadius,
                      ),
                    ),
                    insetPadding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.kSizeXXLarge,
                    ),
                    child: const AddCustomerDialog(),
                  ),
                ),
              ),
              FormFieldWidget(
                controller: _descriptionController,
                fillColor: AppColors.cardDetailsBackground,
                borderColor: AppColors.cardDetailsBackground,
                hintText: 'description'.tr(),
                textInputType: TextInputType.text,
                maxLines: 5,
              ),
              const SizedBox(height: AppDimensions.kSizeMedium2),
              ButtonWidget(
                onPressed: _updateProject,
                title: 'edit'.tr(),
                horizontalPadding: AppDimensions.kSizeXXLarge,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _updateProject() async {
    if (_formKey.currentState!.validate()) {
      await ref.read(updateProjectProvider.notifier).updateProject(
            widget.project.copyWith(
              name: _projectNameController.text,
              description: _descriptionController.text,
              location:
                  ref.read(selectedLocationProvider.notifier).state?.address,
              customerID: ref.read(selectedCustomerProvider.notifier).state?.id,
            ),
          );
    }
  }
}
