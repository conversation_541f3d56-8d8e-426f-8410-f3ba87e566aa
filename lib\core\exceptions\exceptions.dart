import 'package:dio/dio.dart';

class BadCertificateException extends DioException {
  BadCertificateException({required super.requestOptions, super.error});

  @override
  String toString() {
    return error?.toString() ?? 'Bad certificate';
  }
}

class BadRequestException extends DioException {
  BadRequestException({required super.requestOptions, super.error});

  @override
  String toString() {
    return error?.toString() ?? 'Invalid request';
  }
}

class InternalServerErrorException extends DioException {
  InternalServerErrorException({required super.requestOptions, super.error});

  @override
  String toString() {
    return error?.toString() ??
        'Unknown error occurred, please try again later.';
  }
}

class ConflictException extends DioException {
  ConflictException({required super.requestOptions, super.error});
  @override
  String toString() {
    return 'Conflict occurred';
  }
}

class UnauthorizedException extends DioException {
  UnauthorizedException({required super.requestOptions, super.error});
  @override
  String toString() {
    return 'Access denied';
  }
}

class NotFoundException extends DioException {
  NotFoundException({required super.requestOptions, super.error});
  @override
  String toString() {
    return 'The requested information could not be found';
  }
}

class OtherException extends DioException {
  OtherException({required super.requestOptions, super.error});

  @override
  String toString() {
    return error?.toString() ??
        'No internet connection detected, please try again...';
  }
}

class InvalidAccessTokenException extends DioException {
  InvalidAccessTokenException({required super.requestOptions, super.error});
  @override
  String toString() {
    return error?.toString() ?? "Invalid access token.";
  }
}

class DeadlineExceededException extends DioException {
  DeadlineExceededException({required super.requestOptions, super.error});

  @override
  String toString() {
    return error?.toString() ??
        'The connection has timed out, please try again.';
  }
}

class NoBalanceException extends DioException {
  NoBalanceException({required super.requestOptions, super.error});
}
