import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/customer/data/models/customers_model.dart';
import 'package:mdd/features/user/domain/usecases/update_profile.dart';

final updateUserProvider = StateNotifierProvider<UpdateUserProvider, ViewState>(
  (ref) => UpdateUserProvider(
    ref.watch(updateProfileUserCaseProvider),
  ),
);

class UpdateUserProvider extends BaseProvider<ResultModel> {
  final UpdateProfile _updateProfile;

  UpdateUserProvider(this._updateProfile);

  Future<void> updateProfile(CustomersModel model) async {
    setLoadingState();
    final res = await _updateProfile.call(UpdateProfileParams(model));
    res.fold(
      (failure) {
        setErrorState(failure.message);
      },
      setLoadedState,
    );
  }
}
