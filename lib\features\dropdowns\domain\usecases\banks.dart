import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/dropdowns/data/repositories/drop_downs_repository_impl.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/domain/repositories/drop_downs_repository.dart';

final banksUseCaseProvider = Provider<Banks>(
  (ref) => Banks(ref.watch(dropDownsRepositoryImpl)),
);

class Banks implements UseCase<List<DropDownEntity>, NoParams> {
  final DropDownsRepository _repository;

  Banks(this._repository);

  @override
  Future<Either<Failure, List<DropDownEntity>>> call(NoParams params) async {
    return _repository.banksList(params);
  }
}
