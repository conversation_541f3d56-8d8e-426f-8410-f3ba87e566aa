import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';

import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/payments/data/repositories/payments_repository_impl.dart';
import 'package:mdd/features/payments/domain/entities/payments_entity.dart';
import 'package:mdd/features/payments/domain/repositories/payments_repository.dart';

final paymentsUseCaseProvider =
    Provider<Payments>((ref) => Payments(ref.watch(paymentsRepositoryImpl)));

class Payments implements UseCase<List<PaymentsEntity>?, PaymentsParams> {
  final PaymentsRepository _paymentsRepository;

  Payments(this._paymentsRepository);

  @override
  Future<Either<Failure, List<PaymentsEntity>?>> call(
      PaymentsParams params) async {
    return await _paymentsRepository.getPayments(params);
  }
}

class PaymentsParams {
  final int page;
  final int? paymentStatus;
  final String? paymentNo;
  const PaymentsParams(
      {required this.page, this.paymentStatus, this.paymentNo});
}
