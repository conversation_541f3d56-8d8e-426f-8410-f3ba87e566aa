import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/endPoints/end_points.dart';
import 'package:mdd/features/home/<USER>/models/dashboard_model.dart';
import 'package:mdd/features/home/<USER>/usecases/dashboard.dart';
import 'package:mdd/services/dio_client.dart';

abstract class DashboardRemoteDataSource {
  Future<DashboardModel?> getUserDashboard(DashboardParams params);
}

final dashboardRemoteDataSourceImpl = Provider<DashboardRemoteDataSourceImpl>(
    (ref) => DashboardRemoteDataSourceImpl(ref.watch(dioClientProvider)));

class DashboardRemoteDataSourceImpl implements DashboardRemoteDataSource {
  final DioClient _dioClient;

  DashboardRemoteDataSourceImpl(this._dioClient);

  @override
  Future<DashboardModel> getUserDashboard(DashboardParams params) async {
    final response = await _dioClient.dio.get(
      EndPoints.customerDashboard,
      queryParameters: {
        "startDate": params.startDate,
        "endDate": params.endDate,
      },
    );
    return DashboardModel.fromJson(response.data['data']);
  }
}
