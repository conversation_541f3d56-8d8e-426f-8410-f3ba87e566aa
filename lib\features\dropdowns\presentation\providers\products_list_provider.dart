import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/dropdowns/data/models/drop_down_model.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/domain/usecases/products_list.dart';

final productsSearchControllerProvider =
    StateProvider<TextEditingController>((ref) => TextEditingController());

final selectedProductProvider =
    StateProvider.autoDispose<DropDownEntity?>((ref) => null);

final productsListProvider =
    StateNotifierProvider<ProductsListProvider, ViewState>(
  (ref) => ProductsListProvider(
    ref.watch(productsListUseCaseProvider),
    ref.watch(productsSearchControllerProvider),
  ),
);

class ProductsListProvider extends BaseProvider<List<DropDownEntity>> {
  final ProductsList _products;
  final TextEditingController _textEditingController;

  ProductsListProvider(
    this._products,
    this._textEditingController,
  );

  Future<void> fetchProducts() async {
    if (_textEditingController.text.isEmpty) {
      setLoadingState();
    }
    final response = await _products.call(
      ProductsListParams(find: _textEditingController.text),
    );
    response.fold((failure) => setErrorState(failure.message), (products) {
      if (products.isEmpty) {
        products.add(
          DropDownModel(
            id: null,
            textEn: _textEditingController.text,
            textAr: _textEditingController.text,
          ),
        );
        // setEmptyState();
        // return;
      }

      setLoadedState(products);
    });
  }
}
