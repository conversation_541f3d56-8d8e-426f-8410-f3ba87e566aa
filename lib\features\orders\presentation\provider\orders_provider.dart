import 'package:dartz/dartz.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/paginated_provider.dart';
import 'package:mdd/features/orders/data/models/orders_params.dart';
import 'package:mdd/features/orders/domain/entities/orders_entity.dart';
import 'package:mdd/features/orders/domain/usecases/orders.dart';
import 'package:mdd/features/orders/presentation/provider/order_text_field_provider.dart';
import 'package:mdd/utils/enums.dart';

final orderStatusProvider = StateProvider<OrderStatusType?>((ref) => null);

final ordersProvider = StateNotifierProvider<OrdersProvider, ViewState>((ref) =>
    OrdersProvider(
        ref.watch(ordersUseCaseProvider),
        ref.watch(orderStatusProvider),
        ref.watch(orderSearchControllerProvider)));

class OrdersProvider extends PaginatedProvider<OrdersEntity> {
  final Orders _orders;
  final OrderStatusType? _orderStatus;
  final TextEditingController? _orderSearchController;
  OrdersProvider(this._orders, this._orderStatus, this._orderSearchController)
      : super(InitialViewState());
  Future<void> fetchGetOrders() async {
    state = LoadingViewState();

    final response = await fetchList();
    response.fold((failure) {
      state = ErrorViewState(errorMessage: failure.message);
    }, (orders) {
      if (orders != null) {
        state = LoadedViewState(orders);
      }
    });
  }

  @override
  Future<Either<Failure, List<OrdersEntity>?>> fetchList() {
    return _orders.call(OrdersParams(
      page: pageNumber++,
      status: _orderStatus,
      find: _orderSearchController?.text,
    ));
  }
}
