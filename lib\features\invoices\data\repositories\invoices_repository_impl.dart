import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/exceptions/exceptions.dart';
import 'package:mdd/features/attachments/domain/entites/attachments_entity.dart';
import 'package:mdd/features/invoices/data/datasources/invoices_remote_data_source.dart';
import 'package:mdd/features/invoices/domain/entities/invoices_entity.dart';
import 'package:mdd/features/invoices/domain/repositories/invoices_repository.dart';
import 'package:mdd/features/invoices/domain/usecases/invoice_file.dart';
import 'package:mdd/features/invoices/domain/usecases/invoices.dart';

final invoicesRepositoryImpl = Provider<InvoicesRepositoryImpl>(
  (ref) => InvoicesRepositoryImpl(
    ref.watch(invoicesRemoteDataSourceImpl),
  ),
);

class InvoicesRepositoryImpl implements InvoicesRepository {
  final InvoicesRemoteDataSource _invoicesRemoteDataSource;
  InvoicesRepositoryImpl(this._invoicesRemoteDataSource);
  @override
  Future<Either<Failure, List<InvoicesEntity>>> getInvoices(
      InvoicesParams params) async {
    try {
      final res = await _invoicesRemoteDataSource.getInvoices(params);

      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }

  @override
  Future<Either<Failure, AttachmentsEntity>> getInvoiceFileByID(
      InvoiceFileParams params) async {
    try {
      final invoiceResponse =
          await _invoicesRemoteDataSource.getInvoiceById(params);

      return Right(invoiceResponse);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException catch (e) {
      return Left(InternalServerErrorFailure(message: e.error?.toString()));
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.error?.toString()));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }
}
