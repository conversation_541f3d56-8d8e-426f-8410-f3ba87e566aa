// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDpSnTV2nx1Xfygtbthr2XClpWZtbtp3xk',
    appId: '1:139340554529:android:0028e82c304de5c5675e60',
    messagingSenderId: '139340554529',
    projectId: 'mdd-app-51658',
    storageBucket: 'mdd-app-51658.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyD-lwZpkCEH6DA9-Qm2-ojFqaqvFMbi8kQ',
    appId: '1:139340554529:ios:f765d82ac54b707f675e60',
    messagingSenderId: '139340554529',
    projectId: 'mdd-app-51658',
    storageBucket: 'mdd-app-51658.appspot.com',
    androidClientId: '139340554529-03rru0ctf96lk78b2atehhuascjjakrl.apps.googleusercontent.com',
    iosClientId: '139340554529-7ev077eq7jfjkuioc3f5od1rj6curlue.apps.googleusercontent.com',
    iosBundleId: 'sa.mdd.mdd',
  );
}
