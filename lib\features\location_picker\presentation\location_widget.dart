import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/location_picker/presentation/provider/selected_location_provider.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';

class LocationWidget extends ConsumerWidget {
  const LocationWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final address = ref.watch(selectedLocationProvider)?.address;
    return InkWell(
      onTap: () => context.pushRoute(const LocationPickerRoute()),
      child: Card(
        elevation: 0,
        margin: const EdgeInsets.symmetric(
          horizontal: AppDimensions.kSizeMedium,
          vertical: AppDimensions.kSizeSmall,
        ),
        color: AppColors.cardDetailsBackground,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.kMediumRadius),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: AppDimensions.kSizeLarge,
            horizontal: AppDimensions.kSizeLarge,
          ),
          child: TextWidget(
            address ?? 'location'.tr(),
            color: address == null
                ? AppColors.tabTextColor
                : AppColors.disabledColor1,
          ),
        ),
      ),
    );
  }
}
