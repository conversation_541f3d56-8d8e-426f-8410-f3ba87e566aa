import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/search_bar_widget.dart';
import 'package:mdd/features/dropdowns/presentation/providers/products_list_provider.dart';
import 'package:mdd/features/products/presentation/screens/products_view.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/constants/keys.dart';

class ProductsScreen extends ConsumerStatefulWidget {
  const ProductsScreen({
    super.key,
  });

  @override
  ConsumerState createState() => _ProductsScreenState();
}

class _ProductsScreenState extends ConsumerState<ProductsScreen> {
  _fetch() async {
    await ref.read(productsListProvider.notifier).fetchProducts();
  }

  @override
  void dispose() {
    EasyDebounce.cancel(AppKeys.searchDebouncerKey);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: AppDimensions.kSizeSmall,
          ),
          child: SearchBarWidget(
            onChange: (String value) {
              EasyDebounce.debounce(
                AppKeys.searchDebouncerKey,
                const Duration(milliseconds: 500),
                () => _fetch(),
              );
            },
            textController: ref.read(productsSearchControllerProvider),
          ),
        ),
        const Expanded(child: ProductsView()),
      ],
    );
  }
}
