// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'comments_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CommentsModel _$CommentsModelFromJson(Map<String, dynamic> json) =>
    CommentsModel(
      orderCommentID: json['orderCommentID'] as String,
      orderID: json['orderID'] as String,
      orderNo: json['orderNo'] as int?,
      conetent: json['conetent'] as String,
      createdOn: json['createdOn'] as String,
      fullName: json['fullName'] as String?,
      createdBy: json['createdBy'] as String?,
      userType: $enumDecode(_$UserTypeEnumMap, json['userType']),
      isInternal: json['isInternal'] as bool,
    );

Map<String, dynamic> _$CommentsModelToJson(CommentsModel instance) =>
    <String, dynamic>{
      'orderCommentID': instance.orderCommentID,
      'orderID': instance.orderID,
      'orderNo': instance.orderNo,
      'conetent': instance.conetent,
      'createdOn': instance.createdOn,
      'fullName': instance.fullName,
      'createdBy': instance.createdBy,
      'userType': _$UserTypeEnumMap[instance.userType]!,
      'isInternal': instance.isInternal,
    };

const _$UserTypeEnumMap = {
  UserType.customer: 'C',
  UserType.employee: 'E',
};
