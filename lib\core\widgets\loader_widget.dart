import 'package:flutter/material.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mdd/theme/colors.dart';

class LoaderWidget extends StatelessWidget {
  final Color color;

  const LoaderWidget({super.key, this.color = AppColors.primary});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
          // color: Colors.transparent,
          height: 30,
          width: 30,
          child: LoadingIndicator(
            indicatorType: Indicator.ballPulse,
            colors: [color],
            strokeWidth: 5,
          )),
    );
  }
}
