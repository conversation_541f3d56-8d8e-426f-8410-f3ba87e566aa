import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/customer/data/repositories/customers_repository_impl.dart';
import 'package:mdd/features/customer/domain/repositories/customers_repository.dart';
import 'package:mdd/utils/enums.dart';

final createNewCustomerUseCaseProvider = Provider<CreateNewCustomer>((ref) {
  return CreateNewCustomer(
    ref.watch(customersRepositoryImpl),
  );
});

class CreateNewCustomer implements UseCase<ResultModel, CreateCustomerParams> {
  final CustomersRepository createNewCustomerRepository;

  CreateNewCustomer(this.createNewCustomerRepository);

  @override
  Future<Either<Failure, ResultModel>> call(CreateCustomerParams params) async {
    return await createNewCustomerRepository.createNewCustomer(params);
  }
}

class CreateCustomerParams {
  final String? fullName;
  final String? mobile;
  final String? email;
  final String? address;
  final String? organizationID;
  final String? cityID;
  final CustomerType? type;
  final bool? isActive;
  final bool? isEmailNotificationActive;
  final bool? isSMSNotificationActive;

  CreateCustomerParams({
    this.fullName,
    this.mobile,
    this.email,
    this.address,
    this.organizationID,
    this.cityID,
    this.type,
    this.isActive,
    this.isEmailNotificationActive,
    this.isSMSNotificationActive,
  });

  Map<String, dynamic> toJson() => {
        "fullName": fullName,
        "mobile": mobile,
        "email": email,
        "address": address,
        "organizationID": organizationID,
        "cityID": cityID,
        "type": type?.index,
        "isActive": isActive,
        "isEmailNotificationActive": isEmailNotificationActive,
        "isSMSNotificationActive": isSMSNotificationActive,
      };
}
