import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_flavor/flutter_flavor.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/app.dart';
import 'package:mdd/core/exceptions/exceptions.dart';
import 'package:mdd/core/models/api_response.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/repositories/device_info_repository.dart';
import 'package:mdd/core/repositories/token_repository.dart';
import 'package:mdd/routes/app_router.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/utils/sentry_reporter.dart';

import 'package:sentry_dio/sentry_dio.dart';

final dioClientProvider = ChangeNotifierProvider<DioClient>(
  (ref) {
    return DioClient(
      tokenRepo: ref.watch(tokenRepositoryProvider),
      appRouter: ref.watch(appRouterProvider),
      deviceInfoRepo: ref.watch(deviceInfoRepoProvider),
    );
  },
);

class DioClient with ChangeNotifier {
  final Dio _dio;
  Dio get dio => _dio;

  DioClient({
    required TokenRepository tokenRepo,
    required AppRouter appRouter,
    required DeviceInfoRepo deviceInfoRepo,
  }) : _dio = Dio(
          BaseOptions(
            baseUrl: FlavorConfig.instance.variables['baseUrl'].toString(),
            connectTimeout: const Duration(seconds: 30),
            receiveTimeout: const Duration(seconds: 30),
            contentType: Headers.jsonContentType,
          ),
        )
          ..interceptors.addAll(
            [
              AppInterceptors(
                tokenRepo: tokenRepo,
                appRouter: appRouter,
                deviceInfoRepo: deviceInfoRepo,
              ),

            ],
          )
          ..addSentry();
}

class AppInterceptors extends Interceptor {
  final TokenRepository tokenRepo;
  final DeviceInfoRepo deviceInfoRepo;
  final AppRouter appRouter;

  AppInterceptors({
    required this.tokenRepo,
    required this.appRouter,
    required this.deviceInfoRepo,
  });

  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    final accessToken = tokenRepo.authToken;
    if (accessToken != null) {
      options.headers['Authorization'] = 'Bearer $accessToken';
    }
    options.headers.addAll(deviceInfoRepo.deviceInfoHeaders);
    return handler.next(options);
  }

  void _handleTokenExpired() {
    tokenRepo.deleteUserAccess();
    appRouter.replaceAll([const UserLoginRoute()]);
  }

  void _handleError(Response response, ResponseInterceptorHandler handler,
      ResultModel? result, String? message) {
    SentryReporter.genericThrow(response.data.toString());

    switch (result!.status) {
      case 2:
        return handler.reject(
          BadRequestException(
            requestOptions: response.requestOptions,
            error: message,
          ),
        );
      case 3:
        return handler.reject(
          InternalServerErrorException(
            requestOptions: response.requestOptions,
            error: message,
          ),
        );
      case 4:
        return handler.reject(
          NotFoundException(
            requestOptions: response.requestOptions,
            error: message,
          ),
        );
      case 5:
        if (tokenRepo.isTokenExpired) _handleTokenExpired();
        return handler.reject(
          UnauthorizedException(
            requestOptions: response.requestOptions,
            error: message,
          ),
        );
      case 6:
        return handler.reject(
          ConflictException(
            requestOptions: response.requestOptions,
            error: message,
          ),
        );
      case 7:
        return handler.reject(
          NoBalanceException(
            requestOptions: response.requestOptions,
            error: message,
          ),
        );
      case 9:
        _handleTokenExpired();
        return handler.reject(
          InvalidAccessTokenException(
            requestOptions: response.requestOptions,
            error: message,
          ),
        );
      default:
        return handler.reject(
          InternalServerErrorException(
            requestOptions: response.requestOptions,
          ),
        );
    }
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    final locale = appRouter.navigatorKey.currentContext?.locale;
    final isEn = locale?.languageCode == 'en';

    if (kDebugMode) print(response.realUri);
    if (kDebugMode) log('response.data ${response.data}');

    String? errMsg;
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx and is also not 304.

    if (response.data['totalRecords'] != null) {
      final api = ApiResponse.fromJson(response.data);
      if (api.result?.status == 1) return handler.next(response);
      errMsg = isEn ? api.result?.messageEn : api.result?.messageAr;
      return _handleError(response, handler, api.result, errMsg);
    } else if (response.data['messageAr']?.toString() != null &&
        response.data['messageEn']?.toString() != null) {
      final result = ResultModel.fromJson(response.data);
      if (result.status == 1) return handler.next(response);
      errMsg = isEn ? result.messageEn : result.messageAr;
      return _handleError(response, handler, result, errMsg);
    }

    return super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    final locale = appRouter.navigatorKey.currentContext?.locale;
    final isEn = locale?.languageCode == 'en';

    debugPrint(
      'ERROR[${err.response?.statusCode}] => PATH: ${err.requestOptions.path}',
    );
    debugPrint('err.error ${err.error}');
    log('err.response!.data ${err.response?.data}');
    debugPrint('statusMessage ${err.response?.statusMessage}');
    debugPrint('err.message ${err.message}');

    String? errMsg;
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx and is also not 304.
    if (err.response?.data != null && err.response?.data is Map) {
      final result = ResultModel.fromJson(err.response!.data);
      errMsg = isEn ? result.messageEn : result.messageAr;
    }

    switch (err.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        throw DeadlineExceededException(
          requestOptions: err.requestOptions,
          error: err.error.toString(),
        );
      case DioExceptionType.badResponse:
        switch (err.response?.statusCode) {
          case 400:
            throw BadRequestException(
              requestOptions: err.requestOptions,
              error: errMsg ?? err.error?.toString(),
            );
          case 401:
            //TODO : deleteNotificationToken
            tokenRepo.deleteUserAccess();
            appRouter.replaceAll([const UserLoginRoute()]);
            throw UnauthorizedException(
              requestOptions: err.requestOptions,
            );
          case 404:
            throw NotFoundException(
              requestOptions: err.requestOptions,
              error: errMsg ?? err.error?.toString(),
            );
          case 405:
            throw NotFoundException(
              requestOptions: err.requestOptions,
              error: errMsg ?? err.error?.toString(),
            );
          case 409:
            throw ConflictException(
              requestOptions: err.requestOptions,
              error: errMsg ?? err.error?.toString(),
            );
          case 500:
            throw InternalServerErrorException(
              requestOptions: err.requestOptions,
              error: errMsg ?? err.error?.toString(),
            );
          default:
            throw InternalServerErrorException(
              requestOptions: err.requestOptions,
            );
        }
      case DioExceptionType.cancel:
        break;
      case DioExceptionType.unknown:
        throw OtherException(
          requestOptions: err.requestOptions,
          error: errMsg ?? err.error?.toString(),
        );
      case DioExceptionType.badCertificate:
        throw BadCertificateException(
          requestOptions: err.requestOptions,
          error: null,
        );
      case DioExceptionType.connectionError:
        throw InternalServerErrorException(
          requestOptions: err.requestOptions,
          error: null,
        );
    }

    return handler.next(err);
  }
}

@override
String toString() {
  return 'The connection has timed out, please try again.';
}
