import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/payments/data/models/payments_model.dart';
import 'package:mdd/features/payments/data/repositories/payments_repository_impl.dart';
import 'package:mdd/features/payments/domain/repositories/payments_repository.dart';

final createNewPaymentUseCaseProvider = Provider<CreateNewPayment>((ref) {
  return CreateNewPayment(ref.watch(paymentsRepositoryImpl));
});

class CreateNewPayment implements UseCase<ResultModel, CreateNewPaymentParams> {
  final PaymentsRepository createNewPaymentRepository;

  CreateNewPayment(this.createNewPaymentRepository);

  @override
  Future<Either<Failure, ResultModel>> call(
      CreateNewPaymentParams params) async {
    return await createNewPaymentRepository.createNewPayment(params);
  }
}

class CreateNewPaymentParams {
  final PaymentsModel createNewPayment;

  CreateNewPaymentParams(this.createNewPayment);
}
