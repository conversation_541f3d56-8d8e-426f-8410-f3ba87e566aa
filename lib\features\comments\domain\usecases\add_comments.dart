import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/comments/data/repositories/comments_repository_impl.dart';
import 'package:mdd/features/comments/domain/repositories/comments_repository.dart';

final addCommentsUseCaseProvider = Provider<AddComments>(
  (ref) => AddComments(ref.watch(commentsRepositoryImpl)),
);

class AddComments implements UseCase<ResultModel, AddCommentsParams> {
  final CommentsRepository _commentsRepository;

  AddComments(this._commentsRepository);

  @override
  Future<Either<Failure, ResultModel>> call(AddCommentsParams params) async {
    return await _commentsRepository.addComment(params);
  }
}

class AddCommentsParams {
  final String orderId;
  final String content;
  final List<String> mentions;

  AddCommentsParams({
    required this.orderId,
    required this.content,
    this.mentions = const [],
  });
}
