import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/paginated_provider.dart';
import 'package:mdd/features/customer/data/models/customers_model.dart';
import 'package:mdd/features/customer/domain/usecases/customers.dart';
import 'package:mdd/features/projects/presentation/provider/customer_text_field_provider.dart';

final customersProvider =
    StateNotifierProvider.autoDispose<CustomersProvider, ViewState>((ref) {
  return CustomersProvider(
    ref.watch(customersUseCaseProvider),
    ref.watch(customerSearchControllerProvider),
  );
});

class CustomersProvider extends PaginatedProvider<CustomersModel> {
  final Customers _customers;
  final TextEditingController? _customerSearchController;

  CustomersProvider(
    this._customers,
    this._customerSearchController,
  ) : super(InitialViewState());

  Future<void> fetchCustomers([int? limit]) async {
    state = LoadingViewState();
    final response = await fetchList(limit);
    response.fold((failure) {
      state = ErrorViewState(errorMessage: failure.message);
    }, (customers) {
      state = LoadedViewState(customers);
    });
  }

  @override
  Future<Either<Failure, List<CustomersModel>>> fetchList([int? limit]) {
    return _customers.call(CustomersParams(
      limit: limit,
      page: pageNumber++,
      find: _customerSearchController?.text,
    ));
  }
}
