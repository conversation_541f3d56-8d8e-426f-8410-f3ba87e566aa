import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/user/domain/usecases/update_password.dart';

final updateUserPasswordProvider =
    StateNotifierProvider<UpdateUserPasswordProvider, ViewState>(
  (ref) => UpdateUserPasswordProvider(
    ref.watch(updatePasswordUseCaseProvider),
  ),
);

class UpdateUserPasswordProvider extends BaseProvider<ResultModel> {
  final UpdatePassword _updatePassword;
  UpdateUserPasswordProvider(
    this._updatePassword,
  );

  Future<void> updatePassword({
    required String oldPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    setLoadingState();
    final response = await _updatePassword.call(
      UpdatePasswordParams(
        oldPassword: oldPassword,
        newPassword: newPassword,
        confirmPassword: confirmPassword,
      ),
    );
    response.fold(
      (failure) {
        setErrorState(failure.message);
      },
      setLoadedState,
    );
  }
}
