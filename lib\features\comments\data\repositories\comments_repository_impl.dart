import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/exceptions/exceptions.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/features/comments/data/datasources/comments_remote_data_source.dart';
import 'package:mdd/features/comments/data/models/comments_model.dart';
import 'package:mdd/features/comments/domain/repositories/comments_repository.dart';
import 'package:mdd/features/comments/domain/usecases/add_comments.dart';
import 'package:mdd/features/comments/domain/usecases/order_comments.dart';

final commentsRepositoryImpl = Provider<CommentsRepositoryImpl>(
  (ref) => CommentsRepositoryImpl(
    ref.watch(commentsRemoteDataSourceImpl),
  ),
);

class CommentsRepositoryImpl implements CommentsRepository {
  final CommentsRemoteDataSource _commentsRemoteDataSource;
  CommentsRepositoryImpl(this._commentsRemoteDataSource);
  @override
  Future<Either<Failure, ResultModel>> addComment(
      AddCommentsParams params) async {
    try {
      final res = await _commentsRemoteDataSource.addComment(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }

  @override
  Future<Either<Failure, List<CommentsModel>>> orderComments(
      OrderCommentsParams params) async {
    try {
      final res = await _commentsRemoteDataSource.orderComments(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }
}
