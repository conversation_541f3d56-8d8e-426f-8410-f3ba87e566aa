import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/drop_down_widget.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/presentation/providers/customers_list_provider.dart';
import 'package:mdd/features/user/domain/entities/user_entity.dart';
import 'package:mdd/features/user/presentation/provider/user_provider.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class CustomersListDropDownWidget extends ConsumerStatefulWidget {
  const CustomersListDropDownWidget({
    Key? key,
    this.value,
    this.validatorMessage,
    this.title,
  }) : super(key: key);

  final String? value;
  final String? validatorMessage;
  final String? title;

  @override
  ConsumerState createState() => _CustomersDropDownWidgetState();
}

class _CustomersDropDownWidgetState
    extends ConsumerState<CustomersListDropDownWidget> {
  @override
  void initState() {
    final organizationID =
        (ref.read(userProvider) as LoadedViewState<UserEntity>)
            .data
            .customer
            ?.organizationID;

    UiHelper.postBuildCallback((p0) {
      ref
          .read(customersListProvider.notifier)
          .fetchCustomersList(organizationID);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(customersListProvider);
    if (state is LoadingViewState) {
      return const LoaderWidget();
    }
    if (state is LoadedViewState<List<DropDownEntity>>) {
      return Padding(
        padding: const EdgeInsets.symmetric(
          vertical: AppDimensions.kSizeSmall,
          horizontal: AppDimensions.kSizeMedium,
        ),
        child: DropDownWidget<DropDownEntity>(
          title: widget.title ?? 'contact'.tr(),
          validatorMessage: widget.validatorMessage ?? 'contact_validate'.tr(),
          onChanged: (customer) {
            ref.watch(selectedCustomerProvider.notifier).state = customer;
          },
          showSelectedItems: true,
          compareFn: (item, sItem) => item.id == sItem.id,
          value: widget.value != null ? getSelectedCustomer(state.data) : null,
          itemAsString: (customer) => customer.textEn ?? customer.textAr ?? '',
          items: state.data,
        ),
      );
    }
    return const SizedBox();
  }

  DropDownEntity? getSelectedCustomer(List<DropDownEntity> customers) {
    for (final customer in customers) {
      final curValue = ref.read(selectedCustomerProvider)?.id ?? widget.value;
      if (customer.id == curValue) return customer;
    }
    return null;
  }
}
