import 'package:mdd/core/models/paginated_model.dart';
import 'package:mdd/utils/enums.dart';

class PaymentsEntity implements PaginatedModel {
  final String paymentID;
  final double paidAmount;
  final double approvedAmount;
  final String? transactionNo;
  final String? bankName;
  final String? paymentDate;
  final int? orderNo;
  final PaymentsStatusType status;

  PaymentsEntity({
    required this.paymentID,
    required this.paidAmount,
    required this.approvedAmount,
    this.transactionNo,
    this.bankName,
    this.paymentDate,
    this.orderNo,
    required this.status,
  });
}
