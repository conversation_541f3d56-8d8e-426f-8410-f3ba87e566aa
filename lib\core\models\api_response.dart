import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/core/models/base_response.dart';
import 'package:mdd/core/models/result_model.dart';

part 'api_response.g.dart';

@JsonSerializable(
  explicitToJson: true,
  genericArgumentFactories: true,
  createToJson: false,
)
class ApiResponse<T> extends BaseResponse {
  final T data; // Change List<T> to T

  ApiResponse({
    required this.data,
    super.totalRecords, // Remove the required keyword for totalRecords
    super.result,
  });

  factory ApiResponse.fromJson(Map<String, dynamic> json,
          [T Function(Object? json)? fromJsonT]) =>
      _$ApiResponseFromJson<T>(json, fromJsonT ?? ((d) => d as T));

  ApiResponse<T> copyWith({
    T? data, // Change List<T>? to T?
    int? totalRecords,
    ResultModel? result,
  }) {
    return ApiResponse<T>(
      data: data ?? this.data,
      totalRecords: totalRecords ?? this.totalRecords,
      result: result ?? this.result,
    );
  }
}
