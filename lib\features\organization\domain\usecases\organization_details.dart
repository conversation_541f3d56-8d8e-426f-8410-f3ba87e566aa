import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/organization/data/models/organizations_model.dart';
import 'package:mdd/features/organization/data/repositories/organizations_repository_impl.dart';
import 'package:mdd/features/organization/domain/repositories/organizations_repository.dart';

final orgDetailsUseCaseProvider = Provider<OrganizationDetails>(
  (ref) => OrganizationDetails(ref.watch(orgsRepositoryImpl)),
);

class OrganizationDetails implements UseCase<OrgsModel, NoParams> {
  final OrgsRepository _orgsRepository;

  OrganizationDetails(this._orgsRepository);

  @override
  Future<Either<Failure, OrgsModel>> call(NoParams params) async {
    return _orgsRepository.orgDetails(params);
  }
}
