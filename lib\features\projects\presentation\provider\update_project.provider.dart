import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/projects/domain/entities/projects_entity.dart';
import 'package:mdd/features/projects/domain/usecases/update_project.dart';

final updateProjectProvider =
    StateNotifierProvider.autoDispose<UserProjectProvider, ViewState>(
  (ref) => UserProjectProvider(
    ref.watch(updateProjectUseCaseProvider),
  ),
);

class UserProjectProvider extends BaseProvider<ResultModel> {
  final UpdateProject _updateProject;

  UserProjectProvider(this._updateProject);

  Future<void> updateProject(ProjectsEntity projectsEntity) async {
    setLoadingState();
    final response = await _updateProject.call(
      UpdateProjectParams(projectsEntity),
    );
    response.fold((failure) {
      setErrorState(failure.message);
    }, (user) {
      setLoadedState(user);
    });
  }
}
