import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final quotationSearchControllerProvider =
    StateProvider<TextEditingController>((ref) => TextEditingController());
final quotationSuffixIconProvider = StateProvider<bool>((ref) => false);

// for accept quotation
final purchaseNumberProvider = StateProvider<String?>((ref) => null);
final quotationNotesProvider = StateProvider<String?>((ref) => null);
