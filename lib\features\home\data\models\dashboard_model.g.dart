// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DashboardModel _$DashboardModelFromJson(Map<String, dynamic> json) =>
    DashboardModel(
      startDate: json['startDate'] as String?,
      endDate: json['endDate'] as String?,
      isCustomDates: json['isCustomDates'] as bool?,
      todayClients: json['todayClients'] as String?,
      todayOrders: json['todayOrders'] as String?,
      todayQuotations: json['todayQuotations'] as String?,
      todayOperations: json['todayOperations'] as String?,
      todayProfits: json['todayProfits'] as String?,
      todayCommissions: json['todayCommissions'] as String?,
      monthClients: json['monthClients'] as String?,
      monthOrders: json['monthOrders'] as String?,
      monthQuotations: json['monthQuotations'] as String?,
      monthOperations: json['monthOperations'] as String?,
      monthOperationsCount: json['monthOperationsCount'] as String?,
      monthProfits: json['monthProfits'] as String?,
      monthCommissions: json['monthCommissions'] as String?,
      yearClients: json['yearClients'] as String?,
      yearOrders: json['yearOrders'] as String?,
      yearQuotations: json['yearQuotations'] as String?,
      yearProfits: json['yearProfits'] as String?,
      yearCommissions: json['yearCommissions'] as String?,
      yearSupport: json['yearSupport'] as String?,
      invoicePending: json['invoicePending'] as String?,
      invoicePendingAmount: json['invoicePendingAmount'] as String?,
      invoicePaid: json['invoicePaid'] as String?,
      invoicePaidAmount: json['invoicePaidAmount'] as String?,
      invoiceOverDue: json['invoiceOverDue'] as String?,
      invoiceOverDueAmount: json['invoiceOverDueAmount'] as String?,
      paymentWaitting: json['paymentWaitting'] as String?,
      paymentWaittingAmount: json['paymentWaittingAmount'] as String?,
      paymentPending: json['paymentPending'] as String?,
      paymentPendingAmount: json['paymentPendingAmount'] as String?,
      paymentApproved: json['paymentApproved'] as String?,
      ordersNew: json['ordersNew'] as String?,
      ordersWaitingForAprroval: json['ordersWaitingForAprroval'] as String?,
      ordersDeliverProcess: json['ordersDeliverProcess'] as String?,
      ordersDeliverProcessForCustomer:
          json['ordersDeliverProcessForCustomer'] as String?,
      ordersWaitingForPayment: json['ordersWaitingForPayment'] as String?,
      ordersCompleted: json['ordersCompleted'] as String?,
      ordersDeclient: json['ordersDeclient'] as String?,
      delayedOrders: json['delayedOrders'] as String?,
      quotationsNew: json['quotationsNew'] as String?,
      quotationsSubmitted: json['quotationsSubmitted'] as String?,
      quotationsAccepted: json['quotationsAccepted'] as String?,
      quotationsRejected: json['quotationsRejected'] as String?,
      quotationsExpired: json['quotationsExpired'] as String?,
      allCommissions: json['allCommissions'] as String?,
      waitingCommissions: json['waitingCommissions'] as String?,
      unPaidCommissions: json['unPaidCommissions'] as String?,
      paidCommissions: json['paidCommissions'] as String?,
      targetSuppliers: json['targetSuppliers'] as String?,
      targetClients: json['targetClients'] as String?,
      targetProfits: json['targetProfits'] as String?,
      targetOperations: json['targetOperations'] as String?,
      targetOperationsCount: json['targetOperationsCount'] as String?,
      monthSuppliers: json['monthSuppliers'] as String?,
      monthSuppliersPer: json['monthSuppliersPer'] as String?,
      monthProfitsPer: json['monthProfitsPer'] as String?,
      monthOperationsPer: json['monthOperationsPer'] as String?,
      monthOperationsCountPer: json['monthOperationsCountPer'] as String?,
      monthClientsPer: json['monthClientsPer'] as String?,
      ordersAmount: json['ordersAmount'] as String?,
      paymentsAmount: json['paymentsAmount'] as String?,
      owedAmount: json['owedAmount'] as String?,
      unUsedBalance: json['unUsedBalance'] as String?,
      creditBalance: json['creditBalance'] as String?,
      yearOperations: json['yearOperations'] as String?,
      paymentApprovedAmount: json['paymentApprovedAmount'] as String?,
    );

Map<String, dynamic> _$DashboardModelToJson(DashboardModel instance) =>
    <String, dynamic>{
      'owedAmount': instance.owedAmount,
      'unUsedBalance': instance.unUsedBalance,
      'creditBalance': instance.creditBalance,
      'yearOperations': instance.yearOperations,
      'paymentApprovedAmount': instance.paymentApprovedAmount,
      'startDate': instance.startDate,
      'endDate': instance.endDate,
      'isCustomDates': instance.isCustomDates,
      'todayClients': instance.todayClients,
      'todayOrders': instance.todayOrders,
      'todayQuotations': instance.todayQuotations,
      'todayOperations': instance.todayOperations,
      'todayProfits': instance.todayProfits,
      'todayCommissions': instance.todayCommissions,
      'monthClients': instance.monthClients,
      'monthOrders': instance.monthOrders,
      'monthQuotations': instance.monthQuotations,
      'monthOperations': instance.monthOperations,
      'monthOperationsCount': instance.monthOperationsCount,
      'monthProfits': instance.monthProfits,
      'monthCommissions': instance.monthCommissions,
      'yearClients': instance.yearClients,
      'yearOrders': instance.yearOrders,
      'yearQuotations': instance.yearQuotations,
      'yearProfits': instance.yearProfits,
      'yearCommissions': instance.yearCommissions,
      'yearSupport': instance.yearSupport,
      'invoicePending': instance.invoicePending,
      'invoicePendingAmount': instance.invoicePendingAmount,
      'invoicePaid': instance.invoicePaid,
      'invoicePaidAmount': instance.invoicePaidAmount,
      'invoiceOverDue': instance.invoiceOverDue,
      'invoiceOverDueAmount': instance.invoiceOverDueAmount,
      'paymentWaitting': instance.paymentWaitting,
      'paymentWaittingAmount': instance.paymentWaittingAmount,
      'paymentPending': instance.paymentPending,
      'paymentPendingAmount': instance.paymentPendingAmount,
      'paymentApproved': instance.paymentApproved,
      'ordersNew': instance.ordersNew,
      'ordersWaitingForAprroval': instance.ordersWaitingForAprroval,
      'ordersDeliverProcess': instance.ordersDeliverProcess,
      'ordersDeliverProcessForCustomer':
          instance.ordersDeliverProcessForCustomer,
      'ordersWaitingForPayment': instance.ordersWaitingForPayment,
      'ordersCompleted': instance.ordersCompleted,
      'ordersDeclient': instance.ordersDeclient,
      'delayedOrders': instance.delayedOrders,
      'quotationsNew': instance.quotationsNew,
      'quotationsSubmitted': instance.quotationsSubmitted,
      'quotationsAccepted': instance.quotationsAccepted,
      'quotationsRejected': instance.quotationsRejected,
      'quotationsExpired': instance.quotationsExpired,
      'allCommissions': instance.allCommissions,
      'waitingCommissions': instance.waitingCommissions,
      'unPaidCommissions': instance.unPaidCommissions,
      'paidCommissions': instance.paidCommissions,
      'targetSuppliers': instance.targetSuppliers,
      'targetClients': instance.targetClients,
      'targetProfits': instance.targetProfits,
      'targetOperations': instance.targetOperations,
      'targetOperationsCount': instance.targetOperationsCount,
      'monthSuppliers': instance.monthSuppliers,
      'monthSuppliersPer': instance.monthSuppliersPer,
      'monthProfitsPer': instance.monthProfitsPer,
      'monthOperationsPer': instance.monthOperationsPer,
      'monthOperationsCountPer': instance.monthOperationsCountPer,
      'monthClientsPer': instance.monthClientsPer,
      'ordersAmount': instance.ordersAmount,
      'paymentsAmount': instance.paymentsAmount,
    };
