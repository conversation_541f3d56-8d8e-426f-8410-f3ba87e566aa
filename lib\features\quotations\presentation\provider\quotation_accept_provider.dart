import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/domain/usecases/accept_quotation.dart';

final acceptQuotationProvider =
StateNotifierProvider.autoDispose<AcceptQuotationProvider, ViewState>(
        (ref) =>
        AcceptQuotationProvider(ref.watch(acceptQuotationUseCaseProvider)));

class AcceptQuotationProvider extends BaseProvider<ResultModel> {
  final AcceptQuotation _acceptQuotation;
  AcceptQuotationProvider(this._acceptQuotation);

  Future<void> acceptQuotation(QuotationsModel model) async {
    setLoadingState();
    final response = await _acceptQuotation.call(AcceptQuotationParams(model));
    response.fold((failure) {
      setErrorState(failure.message);
    }, (data) {
      setLoadedState(data);
    });
  }
}
