import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';

class SearchBarWidget extends StatelessWidget {
  final TextEditingController textController;
  final Function(String) onChange;
  final VoidCallback? onPressedSearch;
  final VoidCallback? onPressedClear;
  final bool showClearIcon;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  const SearchBarWidget({
    super.key,
    required this.textController,
    required this.onChange,
    this.onPressedSearch,
    this.onPressedClear,
    this.showClearIcon = false,
    this.keyboardType,
    this.inputFormatters,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          const EdgeInsets.symmetric(horizontal: AppDimensions.kSizeMedium),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              keyboardType: keyboardType,
              inputFormatters: inputFormatters,
              controller: textController,
              onChanged: onChange,
              decoration: InputDecoration(
                  fillColor: AppColors.cardColor,
                  hintText: '${'search_here'.tr()}...',
                  hintStyle: const TextStyle(color: AppColors.hintTextColor),
                  suffixIcon: showClearIcon
                      ? CupertinoButton(
                          onPressed: onPressedClear,
                          child: const Icon(
                            Icons.clear,
                            color: AppColors.hintTextColor,
                          ))
                      : null),
            ),
          ),
          if (onPressedSearch != null)
            CupertinoButton(
              onPressed: onPressedSearch,
              child: const Icon(
                Icons.search,
                color: AppColors.hintTextColor,
              ),
            )
        ],
      ),
    );
  }
}
