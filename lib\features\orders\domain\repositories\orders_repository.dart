import 'package:dartz/dartz.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/features/orders/data/models/orders_model.dart';
import 'package:mdd/features/orders/data/models/orders_params.dart';
import 'package:mdd/features/orders/domain/entities/orders_entity.dart';
import 'package:mdd/features/orders/domain/usecases/approve_order.dart';
import 'package:mdd/features/orders/domain/usecases/create_order.dart';
import 'package:mdd/features/orders/domain/usecases/order_details.dart';
import 'package:mdd/features/orders/domain/usecases/update_order.dart';

abstract class OrdersRepository {
  Future<Either<Failure, List<OrdersEntity>>> getOrders(OrdersParams params);
  Future<Either<Failure, OrdersModel>> getOrderById(OrderDetailsParams params);
  Future<Either<Failure, ResultModel>> updateOrder(UpdateOrderParams params);
  Future<Either<Failure, ResultModel>> createOrder(CreateOrderParams params);
  Future<Either<Failure, ResultModel>> approveOrder(ApproveOrderParams params);
}
