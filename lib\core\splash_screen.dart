import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/repositories/device_info_repository.dart';
import 'package:mdd/features/user/data/datasources/user_local_data_source.dart';
import 'package:mdd/features/user/domain/entities/user_entity.dart';
import 'package:mdd/features/user/domain/usecases/notification_token.dart';
import 'package:mdd/features/user/presentation/provider/add_notification_token_provider.dart';
import 'package:mdd/features/user/presentation/provider/user_provider.dart';
import 'package:mdd/features/user/presentation/widgets/login_background.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/services/firebase_cloud_messaging/firebase_cloud_messaging.dart';
import 'package:mdd/utils/constants/storage_keys_constants.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

import 'models/view_states.dart';

@RoutePage()
class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});
  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  @override
  void initState() {
    UiHelper.postBuildCallback((duration) {
      ref.read(deviceInfoRepoProvider).init();
      _onRefresh();
    });
    super.initState();
  }

  void _onRefresh() {
    ref.read(userLocalDataSourceImpl).getUserData().then((userData) {
      if (userData == null) {
        context.replaceRoute(const FirstOnBoardingRoute());
        return;
      }
      ref.read(userProvider.notifier).login(
            userName: userData[kUserName]!,
            password: userData[kPassword]!,
          );
    });
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<ViewState>(userProvider, (_, state) {
      if (state is LoadedViewState<UserEntity>) {
        if (state.data.userType == UserType.customer) {
          ref.read(addNotificationTokenProvider.notifier).addNotificationToken(
                params: NotificationTokenParams(
                  deviceID: ref.read(firebaseMessagingProvider).token ?? '',
                  userID: state.data.userID ?? '',
                  email: state.data.email ?? '',
                  userType: 'C',
                  deviceInfo: Platform.isIOS ? 'ios' : 'android',
                ),
              );
          context.replaceRoute(const BottomNavBarRoute());
        } else {
          ref.read(userProvider.notifier).logout();
          UiHelper.showNotification('incorrect_user_or_password'.tr());
        }
      } else if (state is ErrorViewState) {
        ref.read(userProvider.notifier).logout();
        context.replaceRoute(const UserLoginRoute());
      }
    });
    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          const BackgroundImage(path: 'assets/images/splash_background.png'),
          Padding(
            padding: const EdgeInsets.only(bottom: 180.0),
            child: Image.asset(
              'assets/images/mdd-logo-white.png',
              fit: BoxFit.contain,
              width: 270,
            ),
          ),
          Align(
            alignment: Alignment.bottomRight,
            child: Image.asset(
              'assets/images/splash_sharp.png',
              width: context.widthR(.9),
              opacity: const AlwaysStoppedAnimation(0.7),
            ),
          )
        ],
      ),
    );
  }
}
