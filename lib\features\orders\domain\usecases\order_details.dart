import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/orders/data/models/orders_model.dart';
import 'package:mdd/features/orders/data/repositories/orders_repository_impl.dart';
import 'package:mdd/features/orders/domain/repositories/orders_repository.dart';

final orderDetailsUseCaseProvider = Provider<OrderDetails>(
    (ref) => OrderDetails(ref.watch(orderRepositoryImpl)));

class OrderDetails implements UseCase<OrdersModel?, OrderDetailsParams> {
  final OrdersRepository ordersRepository;

  OrderDetails(this.ordersRepository);

  @override
  Future<Either<Failure, OrdersModel?>> call(OrderDetailsParams params) async {
    return await ordersRepository.getOrderById(params);
  }
}

class OrderDetailsParams {
  final String? id;
  final bool includeItems;
  final bool includeQuotations;
  final bool includeAttachments;
  final bool includeComments;
  final bool includeApprovals;
  OrderDetailsParams({
    required this.id,
    this.includeItems = true,
    this.includeQuotations = true,
    this.includeAttachments = true,
    this.includeComments = true,
    this.includeApprovals = true,
  });
}
