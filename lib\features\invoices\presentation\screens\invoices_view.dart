import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/description_widget/card_item_widget.dart';
import 'package:mdd/core/widgets/description_widget/description_card_item_widget.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/attachments/domain/entites/attachments_entity.dart';
import 'package:mdd/features/attachments/presentation/provider/attachments_provider.dart';
import 'package:mdd/features/invoices/domain/entities/invoices_entity.dart';
import 'package:mdd/features/invoices/presentation/provider/invoice_file_provider.dart';
import 'package:mdd/features/invoices/presentation/provider/invoices_provider.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/constants/constants.dart';
import 'package:mdd/utils/constants/keys.dart';
import 'package:mdd/utils/enum_extensions.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/files_handler.dart';
import 'package:mdd/utils/helper_functions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class InvoicesView extends ConsumerStatefulWidget {
  const InvoicesView({
    super.key,
  });

  @override
  ConsumerState createState() => _InvoicesViewState();
}

class _InvoicesViewState extends ConsumerState<InvoicesView> {
  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      _fetch();
    });
    super.initState();
  }

  void _fetch() {
    ref.read(invoicesProvider.notifier).fetchInvoices();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(invoiceFileProvider, (previous, state) async {
      if (state is LoadingViewState) {
        UiHelper.showLoadingDialog(context);
      }
      if (state is LoadedViewState<AttachmentsEntity>) {
        Navigator.pop(context);
        await FilesHandler(state.data).openFile();
        // await FileProcess(state.data).downloadFile();
      } else if (state is ErrorViewState) {
        await context.maybePop();
        UiHelper.showNotification(state.errorMessage);
      }
    });
    return Consumer(builder: (context, ref, child) {
      final state = ref.watch(invoicesProvider);

      if (state is LoadingViewState) {
        return const LoaderWidget();
      }
      if (state is ErrorViewState) {
        return Text(state.errorMessage);
      }
      if (state is LoadedViewState<List<InvoicesEntity>>) {
        final provider = ref.read(invoicesProvider.notifier);
        return ListView.builder(
            itemCount: state.data.length + 1,
            key: const PageStorageKey(AppKeys.invoicesListKey),
            itemBuilder: (BuildContext context, int index) {
              if (index == state.data.length) {
                if (provider.hasMoreData &&
                    state.data.length >= AppConstants.paginationLimit) {
                  provider.loadMore();
                  return const Padding(
                    padding: EdgeInsets.all(AppDimensions.kSizeMedium),
                    child: LoaderWidget(),
                  );
                } else {
                  return const SizedBox();
                }
              }
              return CardItemsWidget<int>(

                title: state.data[index].amount == null
                    ? ''
                    : state.data[index].amount!.toFormattedPrice(),
                subtitle: state.data[index].orderNo?.toString() ?? '',
                image: 'assets/images/due_amount_image.png',
                imageBackground: AppColors.blueCard,
                statusName: state.data[index].status?.translatedName ?? '',
                statusColor: state.data[index].status?.statusColor,
                menuItems: [
                  if (state.data[index].status != InvoicesStatusType.paid)
                    PopupMenuItem(
                      value: 1,
                      child: TextWidget('create_payment'.tr()),
                    ),
                  PopupMenuItem(
                    value: 2,
                    child: TextWidget('print'.tr()),
                  ),
                ],
                onSelected: (value) async {
                  if (value == 1) {
                    context
                        .pushRoute(CreatePaymentRoute(
                          orderId: state.data[index].orderID,
                          invoiceId: state.data[index].invoiceID,
                          amount: state.data[index].pendingAmount,
                        ))
                        .then((value) =>
                            ref.read(attachmentsProvider).clearAll());
                  } else if (value == 2) {
                    AttachmentsEntity attachmentsEntity =AttachmentsEntity(
                      url: state.data[index].invoicePath,
                      fileName: "Invoice-${state.data[index].invoiceID}.pdf",
                      orderAttachmentId:state.data[index].invoiceID,
                    ) ;
                    await FilesHandler(attachmentsEntity).openFile();}
                },
                descriptionWidget: [
                  if (state.data[index].employeeName != null)
                    DescriptionCardItemWidget(
                      title: 'supplyChainer'.tr(),
                      trailing: state.data[index].employeeName ?? '',
                    ),
                  DescriptionCardItemWidget(
                    title: 'creation_date'.tr(),
                    trailing: state.data[index].createOn != null
                        ? HelperFunctions.formatDate(
                            state.data[index].createOn!)
                        : '',
                  ),
                  DescriptionCardItemWidget(
                    title: 'duo_date'.tr(),
                    trailing: state.data[index].dueDate != null
                        ? HelperFunctions.formatDate(state.data[index].dueDate!)
                        : '',
                  ),
                  DescriptionCardItemWidget(
                    title: 'paid_amount'.tr(),
                    trailing: state.data[index].paidAmount == null
                        ? ''
                        : state.data[index].paidAmount!.toFormattedPrice(),
                  ),
                  DescriptionCardItemWidget(
                    title: 'unpaid_amount'.tr(),
                    trailing: state.data[index].pendingAmount == null
                        ? ''
                        : state.data[index].pendingAmount!.toFormattedPrice(),
                  ),
                ],
              );
            });
      }
      return const SizedBox();
    });
  }
}
