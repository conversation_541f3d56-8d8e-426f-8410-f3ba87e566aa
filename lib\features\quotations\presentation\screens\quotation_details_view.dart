import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/core/widgets/tap_menu_widget.dart';
import 'package:mdd/features/organization/presentation/provider/organization_details_provider.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/presentation/provider/quotation_details_provider.dart';
import 'package:mdd/features/quotations/presentation/provider/quotation_details_tap_provider.dart';
import 'package:mdd/features/quotations/presentation/screens/approvals_details_tap_view.dart';
import 'package:mdd/features/quotations/presentation/screens/quotation_details_tap_view.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class QuotationDetailsView extends ConsumerStatefulWidget {
  final String quotationId;
  const QuotationDetailsView({
    super.key,
    required this.quotationId,
  });

  @override
  ConsumerState createState() => _QuotationDetailsViewState();
}

class _QuotationDetailsViewState extends ConsumerState<QuotationDetailsView> {
  void _fetch() {
    ref
        .read(quotationDetailsProvider.notifier)
        .fetchQuotationDetails(widget.quotationId);
    ref.read(orgDetailsProvider.notifier).fetchOrgDetails();
  }

  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      _fetch();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      final state = ref.watch(quotationDetailsProvider);
      final selectedTapProvider =
          ref.watch(tapMenuSelectedQuotationDetails.state);

      if (state is LoadingViewState) {
        return const Center(child: LoaderWidget());
      }
      if (state is LoadedViewState<QuotationsModel>) {
        return Column(
          children: [
            SizedBox(
              height: context.heightR(.05),
              child: ListView(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.kSizeMedium,
                ),
                scrollDirection: Axis.horizontal,
                children: [
                  TapMenuWidget(
                    isActive: selectedTapProvider.state == 0,
                    tabTitle: 'details'.tr(),
                    onTap: () {
                      if (selectedTapProvider.state != 0) {
                        selectedTapProvider.state = 0;
                      }
                    },
                  ),
                  TapMenuWidget(
                    isActive: selectedTapProvider.state == 1,
                    tabTitle: 'approvals'.tr(),
                    onTap: () {
                      if (selectedTapProvider.state != 1) {
                        selectedTapProvider.state = 1;
                      }
                    },
                  ),
                ],
              ),
            ),
            if (selectedTapProvider.state == 0)
              QuotationDetailsTapView(quotation: state.data),
            if (selectedTapProvider.state == 1)
              ApprovalsDetailsTapView(approvals: state.data.approvals ?? [])
          ],
        );
      }
      return const SizedBox();
    });
  }
}
