import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/projects/data/models/projects_model.dart';
import 'package:mdd/features/projects/data/repositories/projects_repository_impl.dart';
import 'package:mdd/features/projects/domain/repositories/projects_repository.dart';

final projectsUseCaseProvider =
    Provider<Projects>((ref) => Projects(ref.watch(projectsRepositoryImpl)));

class Projects implements UseCase<List<ProjectsModel>, ProjectsParams> {
  final ProjectsRepository _projectsRepository;

  Projects(this._projectsRepository);

  @override
  Future<Either<Failure, List<ProjectsModel>>> call(
      ProjectsParams params) async {
    return await _projectsRepository.getProjects(params);
  }
}

class ProjectsParams {
  final int? page;
  final String? organizationId;
  final String? find;

  const ProjectsParams({
    required this.organizationId,
    required this.page,
    this.find,
  });
}
