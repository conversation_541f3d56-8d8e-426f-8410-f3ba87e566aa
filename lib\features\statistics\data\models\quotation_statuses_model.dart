import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/statistics/domain/entity/quotation_statuses_entity.dart';
import 'package:mdd/utils/enums.dart';

part 'quotation_statuses_model.g.dart';

@JsonSerializable()
class QuotationStatusesModel extends QuotationStatusesEntity {
  QuotationStatusesModel({
    required super.status,
    required super.statusName,
    required super.quotationsCount,
    required super.quotationsAmount,
  });

  factory QuotationStatusesModel.fromJson(Map<String, dynamic> json) =>
      _$QuotationStatusesModelFromJson(json);

  Map<String, dynamic> toJson() => _$QuotationStatusesModelToJson(this);
}
