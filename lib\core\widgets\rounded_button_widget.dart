import 'package:flutter/material.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';

class RoundedButtonWidget extends StatelessWidget {
  final String title;
  final VoidCallback? onPressed;
  final Color backgroundColor;
  final Color disabledColor;
  final bool showBorder;
  const RoundedButtonWidget({
    super.key,
    required this.title,
    required this.onPressed,
    this.backgroundColor = AppColors.purple,
    this.disabledColor = AppColors.disabledTitleColor1,
    this.showBorder = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          border: Border.all(
            color: showBorder
                ? backgroundColor.withOpacity(0.2)
                : Colors.transparent,
          ),
          borderRadius: const BorderRadius.all(
              Radius.circular(AppDimensions.kSize3XLarge))),
      padding:
          const EdgeInsets.symmetric(horizontal: AppDimensions.kSizeXSmall2),
      child: ButtonWidget(
        onPressed: onPressed,
        title: title,
        backgroundColor: backgroundColor,
        disabledBackground: AppColors.disabledTitleColor1,
        shapeRadius: AppDimensions.kSize3XLarge,
        horizontalPadding: AppDimensions.kSizeXXLarge,
        disabledColor: disabledColor,
      ),
    );
  }
}
