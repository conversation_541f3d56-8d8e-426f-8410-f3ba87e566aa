import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/description_widget/description_card_item_widget.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/core/widgets/title_item_widget.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/presentation/provider/quotation_details_provider.dart';
import 'package:mdd/features/quotations/presentation/widgets/product_item_widget.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/extensions.dart';

@RoutePage()
class QuotationAcceptanceScreen extends ConsumerStatefulWidget {
  const QuotationAcceptanceScreen({super.key});

  @override
  ConsumerState createState() => _QuotationAcceptanceScreenState();
}

class _QuotationAcceptanceScreenState
    extends ConsumerState<QuotationAcceptanceScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MainAppBar(),
      body: Consumer(builder: (context, ref, child) {
        final state = ref.watch(quotationDetailsProvider);
        if (state is LoadingViewState) {
          return const Center(child: LoaderWidget());
        }
        if (state is LoadedViewState<QuotationsModel>) {
          final isAllAccepted =
              ref.watch(quotationDetailsProvider.notifier).isAllAccepted;
          return Column(
            children: [
              const SizedBox(
                height: AppDimensions.kSizeLarge,
              ),
              DetailsCustomBar(
                title: 'quotation'.tr(),
                activeIndex: 0,
              ),
              Expanded(
                child: Scrollbar(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: AppDimensions.kSizeMedium),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              TitleItemWidget(title: 'products'.tr()),
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: AppDimensions.kSizeMedium,
                                ),
                                child: ButtonWidget(
                                  onPressed: isAllAccepted
                                      ? null
                                      : () {
                                          ref
                                              .read(quotationDetailsProvider
                                                  .notifier)
                                              .acceptAllProducts();
                                        },
                                  title: 'accept_all'.tr(),
                                ),
                              ),
                            ],
                          ),
                        ),
                        ListView.builder(
                          itemCount: state.data.items?.length,
                          shrinkWrap: true,
                          primary: false,
                          padding: const EdgeInsets.only(
                            bottom: AppDimensions.kSizeMedium,
                          ),
                          itemBuilder: (context, index) {
                            final product = state.data.items?[index];
                            return Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppDimensions.kSizeMedium,
                              ),
                              child: ProductItemWidget(
                                unitName: product?.unitName ?? '',
                                productName: product?.productName ?? '',
                                price: product?.price == null
                                    ? ''
                                    : product!.price!
                                        .toString()
                                        .groupingSeparator(),
                                qty: product?.qty.toString() ?? '',
                                isAccepted: product?.isAccepted ?? false,
                                onChange: (val) {
                                  if (state.data.mustAprroveAll != true) {
                                    ref
                                        .read(quotationDetailsProvider.notifier)
                                        .acceptProduct(
                                          productId: (product?.productID)!,
                                          unitId: (product?.unitID)!,
                                        );
                                  }
                                },
                              ),
                            );
                          },
                        ),
                        const Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: AppDimensions.kSizeMedium,
                          ),
                          child: Divider(
                            color: AppColors.listTileTrailingTextGrey,
                          ),
                        ),
                        DescriptionCardItemWidget(
                          title: 'total_products'.tr(),
                          trailing: state.data.price.toString(),
                        ),
                        DescriptionCardItemWidget(
                          title: 'service_fee'.tr(),
                          trailing: state.data.serviceFees == null
                              ? ''
                              : state.data.serviceFees
                                  .toString()
                                  .groupingSeparator(),
                        ),
                        if (state.data.deliveryFees != 0.0)
                          DescriptionCardItemWidget(
                            title: 'delivery_fees'.tr(),
                            trailing: state.data.deliveryFees == null
                                ? ''
                                : state.data.deliveryFees
                                    .toString()
                                    .groupingSeparator(),
                          ),
                        if (state.data.additionalFees != 0.0)
                          DescriptionCardItemWidget(
                            title: 'additional_fees'.tr(),
                            trailing: state.data.additionalFees == null
                                ? ''
                                : state.data.additionalFees
                                    .toString()
                                    .groupingSeparator(),
                          ),
                        DescriptionCardItemWidget(
                          title: '${'vat'.tr()} ${state.data.vatPer}%',
                          trailing: state.data.vat == null
                              ? ''
                              : state.data.vat.toString().groupingSeparator(),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Card(
                  color: AppColors.white,
                  elevation: 10,
                  margin: EdgeInsets.zero,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: AppDimensions.kSizeXXLarge,
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              const SizedBox(
                                width: AppDimensions.kSizeMedium,
                              ),
                              TextWidget(
                                '${'total_amount'.tr()} :',
                                color: AppColors.listTileTitleTextGrey,
                              ),
                              TextWidget(state.data.total.toString()),
                            ],
                          ),
                        ),
                        ButtonWidget(
                            onPressed: ref
                                    .watch(quotationDetailsProvider.notifier)
                                    .isAtLeastOneAccepted
                                ? () {
                                    context.pushRoute(
                                      const QuotationAdditionalInfoRoute(),
                                    );
                                  }
                                : null,
                            title: 'next'.tr()),
                        const SizedBox(width: AppDimensions.kSizeMedium),
                      ],
                    ),
                  )),
            ],
          );
        }
        return const SizedBox();
      }),
    );
  }
}
