import 'package:json_annotation/json_annotation.dart';

enum Flavor { dev, prod, preProduction }

enum BottomNavBarDestination { home, orders, offers, invoices, statistics }

// orders status
enum OrderStatusType {
  @JsonValue(0)
  none,
  @JsonValue(1)
  created, // Draft, غير مرسل
  @JsonValue(2)
  submitted, // New, جديد
  @JsonValue(3)
  underProcess, // Quoting , جاري التسعير
  @JsonValue(4)
  quotationSubmitted, // َQuotation Submitted, تم إرسال عرض السعر
  @JsonValue(5)
  quotationApproved, // Quotation Approved, مقبول
  @JsonValue(6)
  quotationDeclient, // Quotation Declined, مرفوض عرض السعر
  @JsonValue(7)
  paymentWaiting, // Payment Waiting, انتظار الدفع
  @JsonValue(8)
  paymentDone,
  @JsonValue(9)
  paymentApproved,
  @JsonValue(10)
  payemntDeclinet,
  @JsonValue(11)
  deliverProcess, // Deliver in Process, جاري التوصيل
  @JsonValue(12)
  deliveryNoteSent,
  @JsonValue(13)
  completed, // Completed, مكتمل
  // not exits in email //TODO remove in future
  @JsonValue(14)
  closed,
  @JsonValue(15)
  canceled,
  // not exits in email //TODO remove in future
  @JsonValue(16)
  poWaitingForRequest,
  @JsonValue(17)
  ipoSubmitted,
  @JsonValue(18)
  ipoClosed,
  @JsonValue(19)
  quotationReturned,
  @JsonValue(20)
  quotationExpired,
  @JsonValue(21)
  quotationReNewed,
  @JsonValue(22)
  pricingFeeWaiting,
  @JsonValue(23)
  quotationReturnedAfterApproval,
  @JsonValue(24)
  ipoReturnd,
  @JsonValue(25)
  ipoRepoend,
  @JsonValue(26)
  waiting, // Waiting , إنتظار
  @JsonValue(27)
  rejected, // Rejected, مرفوض
  @JsonValue(28)
  preparingShipment,
  @JsonValue(29)
  ipoMissingDocs,
  @JsonValue(30)
  ipoInComplete,
  @JsonValue(31)
  invocing, // Invoicing, إصدار الفاتورة
}

// Quotations status
enum QuotationsStatusType {
  @JsonValue(0)
  created,
  @JsonValue(1)
  submitted,
  @JsonValue(2)
  accepted,
  @JsonValue(3)
  rejected,
  @JsonValue(4)
  expired,
  @JsonValue(5)
  canceled,
}

// Invoices status
enum InvoicesStatusType {
  @JsonValue(0)
  draft,
  @JsonValue(1)
  unpaid,
  @JsonValue(2)
  overDuo,
  @JsonValue(3)
  paid,
  @JsonValue(4)
  paidPartial,
  @JsonValue(5)
  canceled,
  @JsonValue(6)
  creditNote,
}

// payments status
enum PaymentsStatusType {
  @JsonValue(0)
  waiting,
  @JsonValue(1)
  pending,
  @JsonValue(2)
  approved,
  @JsonValue(3)
  partiallyApproved,
  @JsonValue(4)
  rejected,
}

// order types
enum OrderTypes {
  @JsonValue(1)
  directPO,
  @JsonValue(2)
  tender,
  @JsonValue(3)
  lvp,
  @JsonValue(4)
  payBill,
  @JsonValue(5)
  etimadTender,
}

// notification type
enum NotificationType { success, fail }

// comments user type
enum UserType {
  @JsonValue("C")
  @JsonValue("c")
  customer,
  @JsonValue("E")
  @JsonValue("e")
  employee
}

enum CustomerType {
  @JsonValue(0)
  non,
  @JsonValue(1)
  demander,
  @JsonValue(2)
  authorizer,
  @JsonValue(3)
  fullAuthority,
  @JsonValue(4)
  receiver,
}

enum ApprovalStatusType {
  @JsonValue(0)
  waiting,
  @JsonValue(1)
  approved,
  @JsonValue(2)
  rejected,
}

enum OrganizationStatus {
  @JsonValue(0)
  emailNotConfrimed,
  @JsonValue(1)
  aprroved,
  @JsonValue(2)
  waittingForApproval,
  @JsonValue(3)
  informationMissed,
  @JsonValue(4)
  blackListed,
}

enum OrganizationType {
  @JsonValue(0)
  unclassified,
  @JsonValue(1)
  corporate,
  @JsonValue(2)
  sme,
}
