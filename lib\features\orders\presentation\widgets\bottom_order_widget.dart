import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/card_view_widget.dart';
import 'package:mdd/features/orders/presentation/provider/add_order_provider.dart';
import 'package:mdd/features/orders/presentation/screens/create_order/order_cart_view.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';

class BottomOrderWidget extends StatelessWidget {
  final VoidCallback onNextPressed;
  const BottomOrderWidget({super.key, required this.onNextPressed});

  @override
  Widget build(BuildContext context) {
    return Card(
      color: AppColors.white,
      elevation: 10,
      margin: EdgeInsets.zero,
      child: Padding(
        padding:
            const EdgeInsets.symmetric(vertical: AppDimensions.kSizeXXLarge),
        child: Row(
          children: [
            const SizedBox(
              width: AppDimensions.kSizeMedium,
            ),
            Consumer(builder: (context, ref, child) {
              final totalProducts = ref.watch(addOrderProvider).totalProducts;
              return CardViewWidget(
                title: totalProducts.toString(),
                isDisabled: totalProducts <= 0,
              );
            }),
            const SizedBox(
              width: AppDimensions.kSizeMedium,
            ),
            Consumer(builder: (context, ref, child) {
              final bool isActive =
                  ref.watch(addOrderProvider).totalProducts > 0;
              return ButtonWidget(
                onPressed: isActive
                    ? () {
                        showModalBottomSheet(
                            context: context,
                            backgroundColor: AppColors.white,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(
                                    AppDimensions.kMediumRadius)),
                            builder: (_) => const OrderCartsView());
                      }
                    : null,
                title: 'products_list'.tr(),
                borderColor: AppColors.purple,
                backgroundColor: AppColors.white,
                titleColor: AppColors.purple,
              );
            }),
            const Spacer(),
            Consumer(builder: (context, ref, child) {
              final bool isActive =
                  ref.watch(addOrderProvider).totalProducts > 0;
              return ButtonWidget(
                onPressed: isActive ? onNextPressed : null,
                title: 'next'.tr(),
              );
            }),
            const SizedBox(
              width: AppDimensions.kSizeMedium,
            ),
          ],
        ),
      ),
    );
  }
}
