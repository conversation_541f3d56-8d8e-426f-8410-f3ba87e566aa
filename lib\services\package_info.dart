import 'package:package_info_plus/package_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'package_info.g.dart';

@Riverpod(keepAlive: true)
PackageInfo packageInfo(PackageInfoRef ref) {
  return ref.watch(packageInfoFutureProvider).requireValue;
}

@Riverpod(keepAlive: true)
Future<PackageInfo> packageInfoFuture(PackageInfoFutureRef ref) async {
  return await PackageInfo.fromPlatform();
}
