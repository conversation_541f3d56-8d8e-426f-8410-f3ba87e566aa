import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/extensions.dart';

class FormFieldWidget extends StatelessWidget {
  final TextEditingController controller;
  final String? Function(String?)? validator;
  final VoidCallback? onTap;
  final bool editingForm;
  final bool readOnly;
  final String? hintText;
  final String? icon;
  final Widget? suffixWidget;
  final Color fillColor;
  final Color disabledColor;
  final Color borderColor;
  final TextInputType? textInputType;
  final List<TextInputFormatter>? inputFormatters;
  final int? maxLines;
  final int? maxLength;
  final AutovalidateMode? autoValidateMode;
  final bool obscureText;
  final void Function(String)? onChanged;

  const FormFieldWidget({
    super.key,
    required this.controller,
    this.validator,
    this.editingForm = true,
    this.hintText,
    this.readOnly = false,
    this.icon,
    this.suffixWidget,
    this.fillColor = AppColors.white,
    this.borderColor = AppColors.textFiledBorderColor,
    this.disabledColor = AppColors.cardDetailsBackground,
    this.onTap,
    this.textInputType,
    this.inputFormatters,
    this.maxLines,
    this.maxLength,
    this.autoValidateMode,
    this.onChanged,
    this.obscureText = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
          vertical: AppDimensions.kSizeSmall,
          horizontal: AppDimensions.kSizeMedium),
      child: TextFormField(
        autovalidateMode:
            autoValidateMode ?? AutovalidateMode.onUserInteraction,
        style: context.textTheme.titleMedium?.copyWith(
          color: AppColors.disabledColor1,
        ),
        onChanged: onChanged,
        maxLines: maxLines ?? 1,
        maxLength: maxLength,
        obscureText: obscureText,
        onTap: onTap,
        keyboardType: textInputType,
        inputFormatters: inputFormatters,
        controller: controller,
        validator: validator,
        readOnly: readOnly,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: const TextStyle(color: AppColors.tabTextColor),
          fillColor: editingForm ? AppColors.white : AppColors.cardDetailsBackground,
          enabled: editingForm,
          enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.kMediumRadius),
              borderSide: BorderSide(color: borderColor)),
          focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.kMediumRadius),
              borderSide: BorderSide(color: borderColor)),
          disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.kMediumRadius),
              borderSide: BorderSide(color: disabledColor)),
          prefixIcon: icon != null
              ? Padding(
                  padding: const EdgeInsets.all(AppDimensions.kSizeMedium),
                  child: SvgPicture.asset(
                    icon!,
                    colorFilter: const ColorFilter.mode(
                        AppColors.disabledColor1, BlendMode.srcIn),
                  ),
                )
              : null,
          suffixIcon: suffixWidget,
        ),
      ),
    );
  }
}
