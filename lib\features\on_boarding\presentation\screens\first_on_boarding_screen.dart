import 'dart:math' as math;

import 'package:animate_do/animate_do.dart';
import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/ui/card_clipper.dart';

@RoutePage()
class FirstOnBoardingScreen extends StatelessWidget {
  const FirstOnBoardingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SlideInUp(
                child: Image.asset(
                  'assets/images/on_boarding_image1.png',
                  height: context.widthR(0.7),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.kSize6XLarge,
                ),
                child: RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    text: "${'on_boarding_title1_1'.tr()} ",
                    style: context.textTheme.headlineMedium?.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                    children: [
                      TextSpan(
                        text: "${'on_boarding_title1_2'.tr()} ",
                        style: context.textTheme.headlineMedium?.copyWith(
                          color: AppColors.darkGrey,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      TextSpan(
                        text: 'on_boarding_title1_3'.tr(),
                        style: context.textTheme.headlineMedium?.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Padding(
              //   padding: const EdgeInsets.symmetric(
              //       horizontal: AppDimensions.kSize4XLarge),
              //   child: TextWidget(
              //     'on_boarding_body1'.tr(),
              //     textAlign: TextAlign.center,
              //   ),
              // ),
              const SizedBox(height: AppDimensions.kSizeXLarge),
              const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircleAvatar(
                    radius: 5,
                    backgroundColor: AppColors.activeIndicatorColor,
                  ),
                  SizedBox(width: AppDimensions.kSizeSmall),
                  CircleAvatar(
                    radius: 5,
                    backgroundColor: AppColors.unActiveIndicatorColor,
                  ),
                  SizedBox(width: AppDimensions.kSizeSmall),
                  CircleAvatar(
                    radius: 5,
                    backgroundColor: AppColors.unActiveIndicatorColor,
                  ),
                  SizedBox(width: AppDimensions.kSizeSmall),
                  CircleAvatar(
                    radius: 5,
                    backgroundColor: AppColors.unActiveIndicatorColor,
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.kSize7XLarge),
            ],
          ),
          Positioned(
            left: 0,
            right: 0,
            bottom: 35,
            child: Transform.rotate(
              angle: context.locale.languageCode == 'en' ? math.pi : 0,
              child: InkWell(
                onTap: () {
                  context.replaceRoute(const SecondOnBoardingRoute());
                },
                child: Hero(
                  tag: 'on_boarding_button',
                  child: Image.asset(
                    'assets/images/rounded_button_ar.png',
                    height: 130,
                  ),
                ),
              ),
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: ClipPath(
              clipper: CardClipper(40, top: true),
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(40),
                  topRight: Radius.circular(40),
                ),
                child: Container(
                  height: 100,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xFF2CB191),
                        Color(0xFF23A88A),
                        Color(0xFF209A85),
                        Color(0xFF209A85),
                        Color(0xFF209A85),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
