import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/exceptions/exceptions.dart';
import 'package:mdd/features/statistics/data/datasources/statistics_remote_data_source.dart';
import 'package:mdd/features/statistics/domain/entity/statisctics_entity.dart';
import 'package:mdd/features/statistics/domain/repositories/statistics_repository.dart';
import 'package:mdd/features/statistics/domain/usecases/statistics.dart';

final statisticsRepositoryImpl =
    Provider<StatisticsRepositoryImpl>((ref) => StatisticsRepositoryImpl(
          ref.watch(statisticsRemoteDataSourceImpl),
        ));

class StatisticsRepositoryImpl implements StatisticsRepository {
  final StatisticsRemoteDataSource _rejectReasonsRemoteDataSource;
  StatisticsRepositoryImpl(this._rejectReasonsRemoteDataSource);
  @override
  Future<Either<Failure, StatisticsEntity?>> getStatistics(
      StatisticsParams params) async {
    try {
      final response =
          await _rejectReasonsRemoteDataSource.getStatistics(params);
      return Right(response);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }
}
