// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notifications_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NotificationsModel _$NotificationsModelFromJson(Map<String, dynamic> json) =>
    NotificationsModel(
      notificationID: json['notificationID'] as String,
      objectID: json['objectID'] as String?,
      notificationTemplateID: json['notificationTemplateID'] as int,
      title: json['title'] as String?,
      content: json['content'] as String?,
      type: json['type'] as String?,
      createdOn: json['createdOn'] as String?,
      userID: json['userID'] as String?,
      userName: json['userName'] as String?,
      userType: $enumDecodeNullable(_$UserTypeEnumMap, json['userType']),
      isEmailSent: json['isEmailSent'] as bool,
      isPushSent: json['isPushSent'] as bool,
      isSMSSent: json['isSMSSent'] as bool,
      isEmail: json['isEmail'] as bool,
      isSMS: json['isSMS'] as bool,
      isPush: json['isPush'] as bool,
      isViewed: json['isViewed'] as bool,
      isSent: json['isSent'] as bool,
      mobileNo: json['mobileNo'] as String?,
      language: json['language'] as String?,
    );

Map<String, dynamic> _$NotificationsModelToJson(NotificationsModel instance) =>
    <String, dynamic>{
      'notificationID': instance.notificationID,
      'objectID': instance.objectID,
      'notificationTemplateID': instance.notificationTemplateID,
      'title': instance.title,
      'content': instance.content,
      'type': instance.type,
      'createdOn': instance.createdOn,
      'userID': instance.userID,
      'userName': instance.userName,
      'userType': _$UserTypeEnumMap[instance.userType],
      'isEmailSent': instance.isEmailSent,
      'isPushSent': instance.isPushSent,
      'isSMSSent': instance.isSMSSent,
      'isEmail': instance.isEmail,
      'isSMS': instance.isSMS,
      'isPush': instance.isPush,
      'isViewed': instance.isViewed,
      'isSent': instance.isSent,
      'mobileNo': instance.mobileNo,
      'language': instance.language,
    };

const _$UserTypeEnumMap = {
  UserType.customer: 'C',
  UserType.employee: 'E',
};
