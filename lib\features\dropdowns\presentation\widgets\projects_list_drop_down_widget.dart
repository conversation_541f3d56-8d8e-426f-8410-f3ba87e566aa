import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/drop_down_widget.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/presentation/providers/projects_list_provider.dart';
import 'package:mdd/features/user/domain/entities/user_entity.dart';
import 'package:mdd/features/user/presentation/provider/user_provider.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class ProjectsListDropDownWidget extends ConsumerStatefulWidget {
  const ProjectsListDropDownWidget({
    this.value,
    Key? key,
  }) : super(key: key);

  final String? value;

  @override
  ConsumerState createState() => _ProjectsDropDownWidgetState();
}

class _ProjectsDropDownWidgetState
    extends ConsumerState<ProjectsListDropDownWidget> {
  @override
  void initState() {
    final organizationID =
        (ref.read(userProvider) as LoadedViewState<UserEntity>)
            .data
            .customer
            ?.organizationID;

    UiHelper.postBuildCallback((p0) {
      ref.read(projectsListProvider.notifier).fetchProjectsList(organizationID);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(projectsListProvider);

    if (state is LoadingViewState) {
      return const LoaderWidget();
    }
    if (state is LoadedViewState<List<DropDownEntity>>) {
      return Padding(
        padding: const EdgeInsets.symmetric(
          vertical: AppDimensions.kSizeSmall,
          horizontal: AppDimensions.kSizeMedium,
        ),
        child: DropDownWidget<DropDownEntity>(
          title: 'project_name'.tr(),
          validatorMessage: 'project_name_validate'.tr(),
          onChanged: (project) {
            ref.watch(selectedProjectProvider.notifier).state = project;
          },
          showSelectedItems: true,
          compareFn: (item, sItem) => item.id == sItem.id,
          itemAsString: (project) => project.textEn ?? project.textAr ?? '',
          items: state.data,
          value: widget.value != null ? getSelectedProject(state.data) : null,
        ),
      );
    }
    return const SizedBox();
  }

  DropDownEntity? getSelectedProject(List<DropDownEntity> projects) {
    for (final project in projects) {
      if (project.id == widget.value) return project;
    }
    return null;
  }
}
