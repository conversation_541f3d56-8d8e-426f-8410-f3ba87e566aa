import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/user/data/repositories/user_repository_impl.dart';
import 'package:mdd/features/user/domain/repositories/user_repository.dart';

final notificationTokenUseCaseProvider = Provider<NotificationToken>(
    (ref) => NotificationToken(ref.watch(userRepositoryImpl)));

class NotificationToken
    implements UseCase<ResultModel, NotificationTokenParams> {
  final UserRepository userRepository;

  NotificationToken(this.userRepository);

  @override
  Future<Either<Failure, ResultModel>> call(
      NotificationTokenParams params) async {
    return await userRepository.addNotificationToken(params);
  }
}

class NotificationTokenParams {
  final String deviceID;
  final String userID;
  final String email;
  final String userType;
  final String deviceInfo;

  const NotificationTokenParams({
    required this.deviceID,
    required this.userID,
    required this.email,
    required this.userType,
    required this.deviceInfo,
  });
}
