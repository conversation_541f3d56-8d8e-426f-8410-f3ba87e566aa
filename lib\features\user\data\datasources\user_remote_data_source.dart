import 'dart:math';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/endPoints/end_points.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/customer/data/models/customers_model.dart';
import 'package:mdd/features/user/data/models/user_model.dart';
import 'package:mdd/features/user/domain/usecases/notification_token.dart';
import 'package:mdd/features/user/domain/usecases/update_password.dart';
import 'package:mdd/features/user/domain/usecases/update_profile.dart';
import 'package:mdd/services/dio_client.dart';

abstract class UserRemoteDataSource {
  Future<UserModel?> login(String userName, String password);
  Future<CustomersModel?> getProfile(NoParams params);
  Future<ResultModel> update(UpdateProfileParams params);
  Future<ResultModel> updatePassword(UpdatePasswordParams params);
  Future<ResultModel> addNotificationToken(NotificationTokenParams params);
  Future<ResultModel> deleteNotificationToken(String notificationToken);
}

final userRemoteDataSourceImpl = Provider<UserRemoteDataSourceImpl>(
    (ref) => UserRemoteDataSourceImpl(ref.watch(dioClientProvider)));

class UserRemoteDataSourceImpl implements UserRemoteDataSource {
  final DioClient _dioClient;

  UserRemoteDataSourceImpl(this._dioClient);

  @override
  Future<UserModel?> login(String userName, String password) async {
    final response = await _dioClient.dio.post(
      EndPoints.login,
      data: {
        'userName': userName,
        'password': password,
        'userType': 'client',
      },
    );

    return UserModel.fromJson(response.data['data']);
  }

  @override
  Future<CustomersModel?> getProfile(NoParams params) async {
    final response = await _dioClient.dio.get(EndPoints.profile);
    return CustomersModel.fromJson(response.data['data']);
  }

  @override
  Future<ResultModel> update(UpdateProfileParams params) async {
    final response = await _dioClient.dio.put(
      EndPoints.profile,
      data: params.customerModel.toJson(),
    );

    return ResultModel.fromJson(response.data);
  }

  @override
  Future<ResultModel> updatePassword(UpdatePasswordParams params) async {
    final response = await _dioClient.dio.put(
      EndPoints.password,
      data: {
        "oldPassword": params.oldPassword,
        "newPassword": params.newPassword,
        "confirmPassword": params.confirmPassword,
      },
    );
    return ResultModel.fromJson(response.data);
  }

  @override
  Future<ResultModel> addNotificationToken(
      NotificationTokenParams params) async {
    final response = await _dioClient.dio.post(
      EndPoints.devices,
      data: {
        "deviceID": params.deviceID,
        "userID": params.userID,
        "email": params.email,
        "userType": params.userType,
        "deviceInfo": params.deviceInfo,
      },
    );

    return ResultModel.fromJson(response.data);
  }

  @override
  Future<ResultModel> deleteNotificationToken(String notificationToken) async {
    final response = await _dioClient.dio.delete(
      '${EndPoints.devices}/$notificationToken',
    );

    return ResultModel.fromJson(response.data);
  }
}
