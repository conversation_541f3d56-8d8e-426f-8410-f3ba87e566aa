import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/order_details_item_widget.dart';
import 'package:mdd/features/orders/presentation/provider/add_order_provider.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class OrderCartsView extends StatelessWidget {
  final bool primary;
  const OrderCartsView({super.key, this.primary = true});

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      final orderDetails = ref.watch(addOrderProvider);
      return ListView.builder(
        itemCount: orderDetails.items.length,
        shrinkWrap: true,
        primary: primary,
        itemBuilder: (context, index) {
          final itemVal = orderDetails.items.values.toList()[index];
          final itemKey = orderDetails.items.keys.toList()[index];
          return OrderDetailsItemWidget(
            productName: itemVal.productName ?? '',
            categoryName: itemVal.categoryName ?? '',
            unitName: itemVal.unitName ?? '',
            qty: itemVal.qty.toString(),
            onPressed: () => UiHelper.showSimpleAlertDialog(
              context: context,
              action: () => ref.read(addOrderProvider.notifier).removeItem(
                    key: itemKey,
                    context: context,
                  ),
              message: 'delete_order_detail_message'.tr(),
              okButtonText: 'confirm'.tr(),
              cancelButtonText: 'cancel'.tr(),
            ),
          );
        },
      );
    });
  }
}
