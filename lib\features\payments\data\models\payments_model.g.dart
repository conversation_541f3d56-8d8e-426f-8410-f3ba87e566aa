// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payments_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaymentsModel _$PaymentsModelFromJson(Map<String, dynamic> json) =>
    PaymentsModel(
      paymentID: json['paymentID'] as String,
      invoiceID: json['invoiceID'] as String?,
      orderID: json['orderID'] as String?,
      orderNo: json['orderNo'] as int?,
      prNumber: json['prNumber'] as String?,
      quotationID: json['quotationID'] as String?,
      quotationNo: json['quotationNo'] as String?,
      paidAmount: (json['paidAmount'] as num).toDouble(),
      approvedAmount: (json['approvedAmount'] as num).toDouble(),
      paymentDate: json['paymentDate'] as String?,
      transactionNo: json['transactionNo'] as String?,
      bankID: json['bankID'] as String?,
      bankName: json['bankName'] as String?,
      paymentRecipt: json['paymentRecipt'] as String?,
      status: $enumDecode(_$PaymentsStatusTypeEnumMap, json['status']),
      statusName: json['statusName'] as String?,
      customerID: json['customerID'] as String?,
      customerName: json['customerName'] as String?,
      employeeID: json['employeeID'] as String?,
      employeeName: json['employeeName'] as String?,
      organizationID: json['organizationID'] as String?,
      organizationName: json['organizationName'] as String?,
      approvedBy: json['approvedBy'] as String?,
      approvedName: json['approvedName'] as String?,
      approvedOn: json['approvedOn'] as String?,
      rejectedOn: json['rejectedOn'] as String?,
      fileName: json['fileName'] as String?,
      fileContent: json['fileContent'] as String?,
      fileContentType: json['fileContentType'] as String?,
      isDebit: json['isDebit'] as bool,
      isFromDebitPayment: json['isFromDebitPayment'] as bool,
      paymentDebitID: json['paymentDebitID'] as String?,
    );

Map<String, dynamic> _$PaymentsModelToJson(PaymentsModel instance) =>
    <String, dynamic>{
      'paymentID': instance.paymentID,
      'paidAmount': instance.paidAmount,
      'approvedAmount': instance.approvedAmount,
      'transactionNo': instance.transactionNo,
      'bankName': instance.bankName,
      'paymentDate': instance.paymentDate,
      'orderNo': instance.orderNo,
      'status': _$PaymentsStatusTypeEnumMap[instance.status]!,
      'orderID': instance.orderID,
      'prNumber': instance.prNumber,
      'quotationID': instance.quotationID,
      'quotationNo': instance.quotationNo,
      'invoiceID': instance.invoiceID,
      'employeeID': instance.employeeID,
      'employeeName': instance.employeeName,
      'bankID': instance.bankID,
      'paymentRecipt': instance.paymentRecipt,
      'statusName': instance.statusName,
      'customerID': instance.customerID,
      'customerName': instance.customerName,
      'organizationID': instance.organizationID,
      'organizationName': instance.organizationName,
      'approvedBy': instance.approvedBy,
      'approvedName': instance.approvedName,
      'approvedOn': instance.approvedOn,
      'rejectedOn': instance.rejectedOn,
      'fileName': instance.fileName,
      'fileContent': instance.fileContent,
      'fileContentType': instance.fileContentType,
      'isDebit': instance.isDebit,
      'isFromDebitPayment': instance.isFromDebitPayment,
      'paymentDebitID': instance.paymentDebitID,
    };

const _$PaymentsStatusTypeEnumMap = {
  PaymentsStatusType.waiting: 0,
  PaymentsStatusType.pending: 1,
  PaymentsStatusType.approved: 2,
  PaymentsStatusType.partiallyApproved: 3,
  PaymentsStatusType.rejected: 4,
};
