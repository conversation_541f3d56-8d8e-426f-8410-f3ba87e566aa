import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/statistics/domain/entity/statisctics_entity.dart';
import 'package:mdd/features/statistics/domain/usecases/statistics.dart';
import 'package:mdd/utils/constants/constants.dart';

final statisticsProvider =
    StateNotifierProvider.autoDispose<StatisticsProvider, ViewState>(
        (ref) => StatisticsProvider(
              ref.watch(statisticsUseCaseProvider),
            ));

class StatisticsProvider extends BaseProvider<StatisticsEntity> {
  final Statistics _statistics;

  StatisticsProvider(
    this._statistics,
  );
  Future<void> getStatistics() async {
    setLoadingState();
    final response = await _statistics.call(
      StatisticsParams(
        startDate: AppConstants.kStartDate,
        endDate: AppConstants.kEndDate,
      ),
    );
    response.fold((failure) {
      setErrorState(failure.message);
    }, (statistics) {
      if (statistics != null) {
        setLoadedState(statistics);
      }
    });
  }

  int totalOrders() {
    final orderStatuses =
        (state as LoadedViewState<StatisticsEntity>).data.orderStatuses;
    if (orderStatuses != null && orderStatuses.isNotEmpty) {
      return orderStatuses
          .map((e) => e.ordersCount)
          .reduce((prev, next) => prev + next);
    }
    return 0;
  }

  int totalQuotations() {
    final quotationStatuses =
        (state as LoadedViewState<StatisticsEntity>).data.quotationStatuses;
    if (quotationStatuses != null && quotationStatuses.isNotEmpty) {
      return quotationStatuses
          .map((e) => e.quotationsCount)
          .reduce((prev, next) => prev + next);
    }
    return 0;
  }

  double totalAmountOfOrders() {
    final monthlySummary =
        (state as LoadedViewState<StatisticsEntity>).data.monthlySummary;
    if (monthlySummary != null && monthlySummary.isNotEmpty) {
      return monthlySummary
          .map((e) => e.ordersAmount)
          .reduce((prev, next) => prev + next);
    }
    return 0;
  }
}
