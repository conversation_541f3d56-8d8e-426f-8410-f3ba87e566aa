import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/projects/data/models/projects_model.dart';
import 'package:mdd/features/projects/domain/usecases/create_new_project.dart';

final createNewProjectProvider =
    StateNotifierProvider.autoDispose<CreateNewProjectProvider, ViewState>(
        (ref) => CreateNewProjectProvider(
              ref.watch(createNewProjectUseCaseProvider),
            ));

class CreateNewProjectProvider extends BaseProvider<ResultModel> {
  final CreateNewProject _createNewProject;

  CreateNewProjectProvider(this._createNewProject);

  Future<void> fetchCreateNewProject(
    ProjectsModel createNewProjectModel,
  ) async {
    setLoadingState();
    final response = await _createNewProject
        .call(CreateNewProjectParams(createNewProjectModel));
    response.fold((failure) {
      setErrorState(failure.message);
    }, (data) {
      setLoadedState(data);
    });
  }
}
