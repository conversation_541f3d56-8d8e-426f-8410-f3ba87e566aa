import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/notifications/data/repositories/notifications_repository_impl.dart';
import 'package:mdd/features/notifications/domain/entities/notifications_entity.dart';
import 'package:mdd/features/notifications/domain/repositories/notifications_repository.dart';

final notificationsUseCaseProvider = Provider<Notifications>(
    (ref) => Notifications(ref.watch(notificationsRepositoryImpl)));

class Notifications implements UseCase<List<NotificationsEntity>, NoParams> {
  final NotificationsRepository _notificationsRepository;

  Notifications(this._notificationsRepository);

  @override
  Future<Either<Failure, List<NotificationsEntity>>> call(
      NoParams params) async {
    return await _notificationsRepository.getNotifications(params);
  }
}
