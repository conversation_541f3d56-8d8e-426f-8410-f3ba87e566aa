import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/projects/data/repositories/projects_repository_impl.dart';
import 'package:mdd/features/projects/domain/entities/projects_entity.dart';
import 'package:mdd/features/projects/domain/repositories/projects_repository.dart';

final updateProjectUseCaseProvider = Provider<UpdateProject>((ref) {
  return UpdateProject(
    ref.watch(projectsRepositoryImpl),
  );
});

class UpdateProject implements UseCase<ResultModel, UpdateProjectParams> {
  final ProjectsRepository updateProjectRepository;

  UpdateProject(this.updateProjectRepository);

  @override
  Future<Either<Failure, ResultModel>> call(UpdateProjectParams params) async {
    return await updateProjectRepository.updateProject(params);
  }
}

class UpdateProjectParams extends Equatable {
  final ProjectsEntity project;

  const UpdateProjectParams(this.project);

  @override
  List<Object?> get props => [project];
}
