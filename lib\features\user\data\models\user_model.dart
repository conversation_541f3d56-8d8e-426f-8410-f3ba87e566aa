import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/customer/data/models/customers_model.dart';
import 'package:mdd/features/user/domain/entities/user_entity.dart';
import 'package:mdd/utils/enums.dart';

import 'role_model.dart';

part 'user_model.g.dart';

@JsonSerializable(explicitToJson: true)
class UserModel extends UserEntity {
  UserModel({
    super.userID,
    super.fullName,
    super.email,
    super.userName,
    super.password,
    super.lastPasswordChage,
    super.isMfaEnabled,
    super.mfaCode,
    super.isActive,
    super.userType,
    super.token,
    super.tokenExpireDate,
    super.roleType,
    super.customer,
    super.roles,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);
}
