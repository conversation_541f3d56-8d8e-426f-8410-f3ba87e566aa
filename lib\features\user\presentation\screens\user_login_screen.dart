import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_flavor/flutter_flavor.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/core/widgets/title_widget.dart';
import 'package:mdd/features/user/domain/entities/user_entity.dart';
import 'package:mdd/features/user/domain/usecases/notification_token.dart';
import 'package:mdd/features/user/presentation/provider/add_notification_token_provider.dart';
import 'package:mdd/features/user/presentation/provider/login_form_provider.dart';
import 'package:mdd/features/user/presentation/provider/user_provider.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/services/firebase_cloud_messaging/firebase_cloud_messaging.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/config_reader.dart';
import 'package:mdd/utils/constants/environment.dart';
import 'package:mdd/utils/constants/regex_constants.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/ui/card_clipper.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

@RoutePage()
class UserLoginScreen extends ConsumerStatefulWidget {
  const UserLoginScreen({
    super.key,
  });

  @override
  ConsumerState createState() => _UserLoginScreenState();
}

class _UserLoginScreenState extends ConsumerState<UserLoginScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late final TextEditingController _emailController;
  late final TextEditingController _passController;

  @override
  initState() {
    super.initState();
    _emailController = TextEditingController(
      text: FlavorConfig.instance.variables['flavor'] == Environment.dev
          ? ConfigReader.getUsername()
          : "",
    );
    _passController = TextEditingController(
      text: FlavorConfig.instance.variables['flavor'] == Environment.dev
          ? ConfigReader.getPassword()
          : "",
    );
  }

  Future<void> _login() async {
    if (_formKey.currentState!.validate()) {
      await ref.read(userProvider.notifier).login(
            userName: _emailController.text,
            password: _passController.text,
          );
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<ViewState>(userProvider, (_, state) {
      if (state is LoadingViewState) {
        UiHelper.showLoadingDialog(context);
      } else if (state is LoadedViewState<UserEntity>) {
        if (state.data.userType == UserType.customer) {
          ref.read(addNotificationTokenProvider.notifier).addNotificationToken(
                params: NotificationTokenParams(
                  deviceID: ref.read(firebaseMessagingProvider).token ?? '',
                  userID: state.data.userID ?? '',
                  email: state.data.email ?? '',
                  userType: 'C',
                  deviceInfo: Platform.isIOS ? 'ios' : 'android',
                ),
              );
          context
            ..maybePop()
            ..replaceRoute(const BottomNavBarRoute());
        } else {
          context.maybePop();
          ref.read(userProvider.notifier).logout();
          UiHelper.showNotification('incorrect_user_or_password'.tr());
        }
      } else if (state is ErrorViewState) {
        context.maybePop();
        return UiHelper.showNotification(state.errorMessage);
      }
    });
    return Scaffold(
      body: Stack(
        children: [
          Image.asset(
            'assets/images/login_background.png',
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
          ),
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: AppDimensions.kSize5XLarge),
                const SizedBox(height: AppDimensions.kSize5XLarge),
                Image.asset('assets/images/mdd-logo-white.png'),
                const SizedBox(height: AppDimensions.kSize5XLarge),
                TextWidget(
                  'welcome_again'.tr(),
                  color: AppColors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppDimensions.kSizeMedium),
                TextWidget(
                  'please_login'.tr(),
                  color: AppColors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.w300,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppDimensions.kSize4XLarge),
                Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppDimensions.kSize3XLarge,
                        ),
                        child: TextFormField(
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          style: context.textTheme.titleMedium?.copyWith(
                            color: AppColors.white,
                          ),
                          keyboardType: TextInputType.emailAddress,
                          controller: _emailController,
                          validator: (value) {
                            if (value != null &&
                                !RegexConstants.kEmailRegex.hasMatch(value)) {
                              return 'enter_valid_email'.tr();
                            }
                            return null;
                          },
                          decoration: InputDecoration(
                            hintText: 'email'.tr(),
                            fillColor: AppColors.white.withOpacity(0.3),
                            prefixIcon: Padding(
                              padding: const EdgeInsets.all(
                                AppDimensions.kSizeMedium,
                              ),
                              child: SvgPicture.asset(
                                'assets/icons/email_icon.svg',
                                colorFilter: const ColorFilter.mode(
                                    AppColors.white, BlendMode.srcIn),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: AppDimensions.kSize4XLarge,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppDimensions.kSize3XLarge,
                        ),
                        child: TextFormField(
                          obscureText: ref.watch(passwordVisibilityProvider),
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          controller: _passController,
                          style: context.textTheme.titleMedium?.copyWith(
                            color: AppColors.white,
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'enter_password'.tr();
                            }
                            return null;
                          },
                          decoration: InputDecoration(
                            hintText: 'password'.tr(),
                            fillColor: AppColors.white.withOpacity(0.3),
                            prefixIcon: Padding(
                              padding: const EdgeInsets.all(
                                AppDimensions.kSizeMedium,
                              ),
                              child: SvgPicture.asset(
                                  'assets/icons/password_icon.svg'),
                            ),
                            suffixIcon: GestureDetector(
                              onTap: () {
                                final visibility =
                                    ref.read(passwordVisibilityProvider.state);
                                if (visibility.state) {
                                  visibility.state = false;
                                  return;
                                }
                                visibility.state = true;
                              },
                              child: Padding(
                                padding: const EdgeInsets.all(
                                  AppDimensions.kSizeMedium,
                                ),
                                child: SvgPicture.asset(
                                  'assets/icons/eye_icon.svg',
                                  colorFilter: ColorFilter.mode(
                                    ref.watch(passwordVisibilityProvider)
                                        ? AppColors.white.withOpacity(0.6)
                                        : AppColors.white,
                                    BlendMode.srcIn,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: AppDimensions.kSizeXXLarge),
                    ],
                  ),
                ),
                // CupertinoButton(
                //     onPressed: () {},
                //     child: TextWidget(
                //       'forget_password'.tr(),
                //       color: AppColors.white,
                //     )),
                // const SizedBox(
                //   height: AppDimensions.kSizeXLarge,
                // ),
                const SizedBox(height: AppDimensions.kSizeMedium2),
                InkWell(
                  onTap: _login,
                  child: Stack(
                    clipBehavior: Clip.none,
                    children: [
                      ClipPath(
                        clipper: CardClipper(28),
                        child: Card(
                          color: AppColors.white.withOpacity(0.2),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              AppDimensions.kDefaultRadius,
                            ),
                          ),
                          elevation: 0,
                          child: Padding(
                            padding: const EdgeInsets.only(
                              left: AppDimensions.kSize4XLarge,
                              right: AppDimensions.kSize4XLarge,
                              top: AppDimensions.kSizeSmall,
                              bottom: AppDimensions.kSizeLarge,
                            ),
                            child: TitleWidget(
                              'login'.tr(),
                              color: AppColors.primary,
                              fontSize: 24,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        left: -AppDimensions.kSizeXXLarge,
                        top: 0,
                        bottom: 0,
                        child: Container(
                          width: 50,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: const LinearGradient(
                              begin: Alignment.topRight,
                              end: Alignment.bottomLeft,
                              colors: [
                                AppColors.blueChillColor,
                                AppColors.blueChillColor,
                                AppColors.poloBlueColor,
                              ],
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.astralColor.withOpacity(0.4),
                                spreadRadius: 7,
                                blurRadius: 10,
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.arrow_forward,
                            color: AppColors.white,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
