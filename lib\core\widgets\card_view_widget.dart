import 'package:flutter/material.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';

class CardViewWidget extends StatelessWidget {
  final String title;
  final Color backgroundColor;
  final Color titleColor;
  final bool isDisabled;
  final double shapeRadius;

  const CardViewWidget({
    super.key,
    required this.title,
    this.backgroundColor = AppColors.primary,
    this.titleColor = AppColors.primary,
    this.isDisabled = false,
    this.shapeRadius = 10,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: isDisabled
          ? AppColors.cardDetailsBackground
          : backgroundColor.withOpacity(0.2),
      elevation: 0,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(shapeRadius)),
      child: Padding(
        padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.kSizeMedium,
            vertical: AppDimensions.kSizeXSmall2),
        child: TextWidget(
          title,
          color: isDisabled ? AppColors.disabledTitleColor1 : AppColors.primary,
        ),
      ),
    );
  }
}
