import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/data/repositories/quotations_repository_impl.dart';
import 'package:mdd/features/quotations/domain/repositories/quotations_repository.dart';

final quotationsUseCaseProvider = Provider<Quotations>(
    (ref) => Quotations(ref.watch(quotationsRepositoryImpl)));

class Quotations implements UseCase<List<QuotationsModel>, QuotationsParams> {
  final QuotationsRepository _quotationsRepository;

  Quotations(this._quotationsRepository);

  @override
  Future<Either<Failure, List<QuotationsModel>>> call(
      QuotationsParams params) async {
    return await _quotationsRepository.getQuotations(params);
  }
}

class QuotationsParams {
  final int page;
  final int? quotationStatus;
  final String? quotationNo;
  const QuotationsParams(
      {required this.page, this.quotationStatus, this.quotationNo});
}
