import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/projects/data/models/projects_model.dart';
import 'package:mdd/features/projects/data/repositories/projects_repository_impl.dart';
import 'package:mdd/features/projects/domain/repositories/projects_repository.dart';

final createNewProjectUseCaseProvider = Provider<CreateNewProject>((ref) {
  return CreateNewProject(
    ref.watch(projectsRepositoryImpl),
  );
});

class CreateNewProject implements UseCase<ResultModel, CreateNewProjectParams> {
  final ProjectsRepository createNewProjectRepository;

  CreateNewProject(this.createNewProjectRepository);

  @override
  Future<Either<Failure, ResultModel>> call(
      CreateNewProjectParams params) async {
    return await createNewProjectRepository.createNewProject(params);
  }
}

class CreateNewProjectParams {
  final ProjectsModel createNewProject;

  CreateNewProjectParams(this.createNewProject);
}
