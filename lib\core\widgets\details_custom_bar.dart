import 'dart:math' as math;

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/core/widgets/time_line_widget.dart';
import 'package:mdd/theme/dimensions.dart';

class DetailsCustomBar extends StatelessWidget {
  final String? title;
  final int? activeIndex;
  const DetailsCustomBar({super.key, required this.title, this.activeIndex});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: AppDimensions.kSizeSmall),
                  Transform.rotate(
                    angle: context.locale.languageCode == 'en' ? math.pi : 0,
                    child: CupertinoButton(
                      onPressed: () => context.popRoute(),
                      padding: EdgeInsets.zero,
                      child: SvgPicture.asset('assets/icons/arrow_back.svg'),
                    ),
                  ),
                ],
              ),
              Flexible(child: TextWidget(title ?? '')),
            ],
          ),
        ),
        if (activeIndex != null) ...[
          TimeLineWidget(activeIndex: activeIndex!),
          const SizedBox(width: AppDimensions.kSizeMedium),
        ]
      ],
    );
  }
}
