import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/comments/domain/usecases/add_comments.dart';

final addCommentsProvider =
    StateNotifierProvider.autoDispose<AddCommentsProvider, ViewState>(
  (ref) => AddCommentsProvider(
    ref.watch(addCommentsUseCaseProvider),
  ),
);

class AddCommentsProvider extends BaseProvider<ResultModel> {
  final AddComments _addComments;

  AddCommentsProvider(this._addComments);

  Future<void> addComment(String orderId, String content) async {
    setLoadingState();
    final res = await _addComments.call(
      AddCommentsParams(
        orderId: orderId,
        content: content,
      ),
    );
    res.fold(
      (failure) => setErrorState(failure.message),
      setLoadedState,
    );
  }
}
