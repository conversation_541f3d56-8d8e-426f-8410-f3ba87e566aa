import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/comments/data/models/comments_model.dart';
import 'package:mdd/features/comments/data/repositories/comments_repository_impl.dart';
import 'package:mdd/features/comments/domain/repositories/comments_repository.dart';

final orderCommentsUseCaseProvider = Provider<OrderComments>(
  (ref) => OrderComments(ref.watch(commentsRepositoryImpl)),
);

class OrderComments
    implements UseCase<List<CommentsModel>, OrderCommentsParams> {
  final CommentsRepository _commentsRepository;

  OrderComments(this._commentsRepository);

  @override
  Future<Either<Failure, List<CommentsModel>>> call(
      OrderCommentsParams params) async {
    return await _commentsRepository.orderComments(params);
  }
}

class OrderCommentsParams {
  final String orderId;

  OrderCommentsParams(this.orderId);
}
