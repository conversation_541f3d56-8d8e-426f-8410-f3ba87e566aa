import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/user/data/repositories/user_repository_impl.dart';
import 'package:mdd/features/user/domain/repositories/user_repository.dart';

final updatePasswordUseCaseProvider = Provider<UpdatePassword>(
    (ref) => UpdatePassword(ref.watch(userRepositoryImpl)));

class UpdatePassword implements UseCase<ResultModel, UpdatePasswordParams> {
  final UserRepository userRepository;

  UpdatePassword(this.userRepository);

  @override
  Future<Either<Failure, ResultModel>> call(UpdatePasswordParams params) async {
    return await userRepository.updatePassword(params);
  }
}

class UpdatePasswordParams {
  final String oldPassword;
  final String newPassword;
  final String confirmPassword;

  const UpdatePasswordParams({
    required this.oldPassword,
    required this.newPassword,
    required this.confirmPassword,
  });
}
