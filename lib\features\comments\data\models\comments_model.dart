import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/comments/domain/entities/comments_entity.dart';
import 'package:mdd/utils/enums.dart';

part 'comments_model.g.dart';

@JsonSerializable()
class CommentsModel extends CommentsEntity {
  CommentsModel({
    required super.orderCommentID,
    required super.orderID,
    required super.orderNo,
    required String super.conetent,
    required super.createdOn,
    required super.fullName,
    required super.createdBy,
    required super.userType,
    required super.isInternal,
  });

  factory CommentsModel.fromJson(Map<String, dynamic> json) =>
      _$CommentsModelFromJson(json);

  Map<String, dynamic> toJson() => _$CommentsModelToJson(this);
}
