import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/endPoints/end_points.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/features/payments/data/models/payments_model.dart';
import 'package:mdd/features/payments/domain/usecases/create_new_payment.dart';
import 'package:mdd/features/payments/domain/usecases/payments.dart';
import 'package:mdd/services/dio_client.dart';
import 'package:mdd/utils/constants/constants.dart';

abstract class PaymentsRemoteDataSource {
  Future<List<PaymentsModel>> getPayments(PaymentsParams params);
  Future<ResultModel> createNewPayment(CreateNewPaymentParams params);
}

final paymentsRemoteDataSourceImpl = Provider<PaymentsRemoteDataSourceImpl>(
    (ref) => PaymentsRemoteDataSourceImpl(ref.watch(dioClientProvider)));

class PaymentsRemoteDataSourceImpl implements PaymentsRemoteDataSource {
  final DioClient _dioClient;

  PaymentsRemoteDataSourceImpl(this._dioClient);
  @override
  Future<List<PaymentsModel>> getPayments(PaymentsParams params) async {
    final response = await _dioClient.dio.get(
      EndPoints.payments,
      queryParameters: {
        'limit': AppConstants.paginationLimit,
        'page': params.page,
        if (params.paymentStatus != null) 'status': params.paymentStatus,
        if (params.paymentNo?.isNotEmpty ?? false) 'find': params.paymentNo,
      },
    );
    return (response.data['data'] as List)
        .map((payment) => PaymentsModel.fromJson(payment))
        .toList();
  }

  @override
  Future<ResultModel> createNewPayment(CreateNewPaymentParams params) async {
    final response = await _dioClient.dio.post(
      EndPoints.payments,
      data: params.createNewPayment.toJson()
        ..removeWhere((key, value) => value == null),
    );
    return ResultModel.fromJson(response.data);
  }
}
