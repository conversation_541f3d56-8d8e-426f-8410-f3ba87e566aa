import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/drop_down_widget.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/presentation/providers/cities_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class CitiesListDropDownWidget extends ConsumerStatefulWidget {
  const CitiesListDropDownWidget({
    Key? key,
    this.value,
    this.enabled = true,
  }) : super(key: key);

  final String? value;
  final bool enabled;
  @override
  ConsumerState<CitiesListDropDownWidget> createState() =>
      _CitiesListDropDownWidgetState();
}

class _CitiesListDropDownWidgetState
    extends ConsumerState<CitiesListDropDownWidget> {
  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      final state = ref.watch(citiesListProvider);
      if (state is! LoadedViewState<List<DropDownEntity>>) {
        ref.read(citiesListProvider.notifier).fetchCitiesList();
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(citiesListProvider);
    if (state is LoadingViewState) {
      return const LoaderWidget();
    }
    if (state is LoadedViewState<List<DropDownEntity>>) {
      return Padding(
        padding: const EdgeInsets.symmetric(
          vertical: AppDimensions.kSizeSmall,
          horizontal: AppDimensions.kSizeMedium,
        ),
        child: DropDownWidget<DropDownEntity>(
          title: 'city'.tr(),
          validatorMessage: 'city_validate'.tr(),
          onChanged: (city) {
            ref.watch(selectedCityProvider.notifier).state = city;
          },
          icon: 'assets/icons/location_icon.svg',
          borderColor: AppColors.cardDetailsBackground,
          showSelectedItems: true,
          compareFn: (item, sItem) => item.id == sItem.id,
          value: widget.value != null ? getSelectedCity(state.data) : null,
          itemAsString: (cities) =>
              (context.locale.languageCode == 'en'
                  ? cities.textEn ?? cities.textAr
                  : cities.textAr ?? cities.textEn) ??
              '',
          items: state.data,
          enabled: widget.enabled,
        ),
      );
    }
    return const SizedBox();
  }

  DropDownEntity? getSelectedCity(List<DropDownEntity> cities) {
    for (final city in cities) {
      if (city.id == widget.value) return city;
    }
    return null;
  }
}
