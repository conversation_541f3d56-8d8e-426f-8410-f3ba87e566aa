// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_order_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateOrderModel _$CreateOrderModelFromJson(Map<String, dynamic> json) =>
    CreateOrderModel(
      projectID: json['projectID'] as String,
      customerID: json['customerID'] as String,
      orderID: json['orderID'] as String?,
      organizationID: json['organizationID'] as String,
      orderType: json['orderType'] as int,
      description: json['description'] as String?,
      attachments: (json['attachments'] as List<dynamic>?)
          ?.map((e) => AttachmentsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      orderDetails: (json['orderDetails'] as List<dynamic>?)
          ?.map((e) => OrderDetailsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$CreateOrderModelToJson(CreateOrderModel instance) =>
    <String, dynamic>{
      'projectID': instance.projectID,
      'customerID': instance.customerID,
      'orderID': instance.orderID,
      'organizationID': instance.organizationID,
      'orderType': instance.orderType,
      'description': instance.description,
      'attachments': instance.attachments,
      'orderDetails': instance.orderDetails,
    };
