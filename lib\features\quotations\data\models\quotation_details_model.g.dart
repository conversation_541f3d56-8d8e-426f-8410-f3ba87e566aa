// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quotation_details_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuotationDetailsModel _$QuotationDetailsModelFromJson(
        Map<String, dynamic> json) =>
    QuotationDetailsModel(
      quotationDetailID: json['quotationDetailID'] as String?,
      sNo: json['sNo'] as int?,
      quotationID: json['quotationID'] as String?,
      quotationNo: json['quotationNo'] as String?,
      orderID: json['orderID'] as String?,
      orderNo: json['orderNo'] as int?,
      productID: json['productID'] as String?,
      productName: json['productName'] as String?,
      supplierID: json['supplierID'] as String?,
      supplierName: json['supplierName'] as String?,
      unitID: json['unitID'] as String?,
      unitName: json['unitName'] as String?,
      isAccepted: json['isAccepted'] as bool,
      isVATExcluded: json['isVATExcluded'] as bool,
      item: json['item'] as String?,
      price: (json['price'] as num?)?.toDouble(),
      purchasePrice: (json['purchasePrice'] as num?)?.toDouble(),
      qty: json['qty'] as int?,
      total: (json['total'] as num?)?.toDouble(),
      isReturned: json['isReturned'] as bool?,
      returnRequestBy: json['returnRequestBy'] as String?,
      returnRequestDate: json['returnRequestDate'] as String?,
      returnBy: json['returnBy'] as String?,
      returnDate: json['returnDate'] as String?,
    );

Map<String, dynamic> _$QuotationDetailsModelToJson(
        QuotationDetailsModel instance) =>
    <String, dynamic>{
      'quotationDetailID': instance.quotationDetailID,
      'orderNo': instance.orderNo,
      'productID': instance.productID,
      'productName': instance.productName,
      'supplierID': instance.supplierID,
      'supplierName': instance.supplierName,
      'unitID': instance.unitID,
      'unitName': instance.unitName,
      'isAccepted': instance.isAccepted,
      'isVATExcluded': instance.isVATExcluded,
      'price': instance.price,
      'purchasePrice': instance.purchasePrice,
      'qty': instance.qty,
      'total': instance.total,
      'sNo': instance.sNo,
      'quotationID': instance.quotationID,
      'quotationNo': instance.quotationNo,
      'orderID': instance.orderID,
      'item': instance.item,
      'isReturned': instance.isReturned,
      'returnRequestBy': instance.returnRequestBy,
      'returnRequestDate': instance.returnRequestDate,
      'returnBy': instance.returnBy,
      'returnDate': instance.returnDate,
    };
