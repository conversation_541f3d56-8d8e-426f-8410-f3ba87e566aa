import 'package:dartz/dartz.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/features/attachments/domain/entites/attachments_entity.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/domain/usecases/accept_approval_quotation.dart';
import 'package:mdd/features/quotations/domain/usecases/accept_quotation.dart';
import 'package:mdd/features/quotations/domain/usecases/pdf_quotation.dart';
import 'package:mdd/features/quotations/domain/usecases/quotation_details.dart';
import 'package:mdd/features/quotations/domain/usecases/quotations.dart';
import 'package:mdd/features/quotations/domain/usecases/reject_quotation.dart';

abstract class QuotationsRepository {
  Future<Either<Failure, List<QuotationsModel>>> getQuotations(
      QuotationsParams params);
  Future<Either<Failure, QuotationsModel>> getQuotationById(
      QuotationDetailsParams params);
  Future<Either<Failure, ResultModel>> rejectQuotation(
      RejectQuotationParams params);
  Future<Either<Failure, ResultModel>> acceptQuotation(
      AcceptQuotationParams params);
  Future<Either<Failure, ResultModel>> acceptApprovalQuotation(
      AcceptApprovalQuotationParams params);
  Future<Either<Failure, AttachmentsEntity>> getPdfQuotation(
      PdfQuotationParams params);
}
