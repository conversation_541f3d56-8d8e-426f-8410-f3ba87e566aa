// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'projects_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProjectsModel _$ProjectsModelFromJson(Map<String, dynamic> json) =>
    ProjectsModel(
      projectID: json['projectID'] as String?,
      organizationID: json['organizationID'] as String?,
      organizationName: json['organizationName'] as String?,
      name: json['name'] as String?,
      location: json['location'] as String?,
      description: json['description'] as String?,
      createdOn: json['createdOn'] as String?,
      customerID: json['customerID'] as String?,
      customerName: json['customerName'] as String?,
      customerEmail: json['customerEmail'] as String?,
      customerMobile: json['customerMobile'] as String?,
      technicalApproverID: json['technicalApproverID'] as String?,
      technicalApproverName: json['technicalApproverName'] as String?,
      technicalApproverEmail: json['technicalApproverEmail'] as String?,
      finalApproverID: json['finalApproverID'] as String?,
      finalApproverName: json['finalApproverName'] as String?,
      finalApproverEmail: json['finalApproverEmail'] as String?,
      contactPerson: json['contactPerson'] == null
          ? null
          : CustomersModel.fromJson(
              json['contactPerson'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ProjectsModelToJson(ProjectsModel instance) =>
    <String, dynamic>{
      'projectID': instance.projectID,
      'organizationID': instance.organizationID,
      'name': instance.name,
      'location': instance.location,
      'description': instance.description,
      'customerID': instance.customerID,
      'contactPerson': instance.contactPerson?.toJson(),
      'organizationName': instance.organizationName,
      'createdOn': instance.createdOn,
      'customerName': instance.customerName,
      'customerEmail': instance.customerEmail,
      'customerMobile': instance.customerMobile,
      'technicalApproverID': instance.technicalApproverID,
      'technicalApproverName': instance.technicalApproverName,
      'technicalApproverEmail': instance.technicalApproverEmail,
      'finalApproverID': instance.finalApproverID,
      'finalApproverName': instance.finalApproverName,
      'finalApproverEmail': instance.finalApproverEmail,
    };
