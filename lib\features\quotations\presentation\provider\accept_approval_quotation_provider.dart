import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/domain/usecases/accept_approval_quotation.dart';

final acceptApprovalQuotationProvider = StateNotifierProvider.autoDispose<
    AcceptApprovalQuotationProvider, ViewState>(
      (ref) => AcceptApprovalQuotationProvider(
    ref.watch(acceptApprovalQuotationUseCaseProvider),
  ),
);

class AcceptApprovalQuotationProvider extends BaseProvider<ResultModel> {
  final AcceptApprovalQuotation _acceptApprovalQuotation;
  AcceptApprovalQuotationProvider(this._acceptApprovalQuotation);

  Future<void> acceptApprovalQuotation(QuotationsModel quotation) async {
    setLoadingState();
    final response = await _acceptApprovalQuotation.call(
      AcceptApprovalQuotationParams(quotation),
    );
    response.fold((failure) {
      setErrorState(failure.message);
    }, (data) {
      setLoadedState(data);
    });
  }
}
