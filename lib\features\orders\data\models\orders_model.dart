import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/attachments/data/models/attachments_model.dart';
import 'package:mdd/features/comments/data/models/comments_model.dart';
import 'package:mdd/features/orders/data/models/order_details_model.dart';
import 'package:mdd/features/orders/domain/entities/orders_entity.dart';
import 'package:mdd/features/quotations/data/models/approval_model.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/utils/enums.dart';

part 'orders_model.g.dart';

@JsonSerializable()
class OrdersModel extends OrdersEntity {
  OrdersModel({
    super.orderID,
    super.projectID,
    super.projectName,
    super.orderNo,
    this.prNumber,
    this.description,
    super.createdOn,
    super.orderStatus,
    this.orderStatusName,
    super.orderType,
    super.orderTypeName,
    this.submitedOn,
    super.organizationID,
    super.organizationName,
    super.employeeID,
    super.employeeName,
    this.isDelayed,
    super.customerID,
    super.customerName,
    this.dnSentOn,
    this.isDeleted,
    this.cancelBy,
    this.cancelByName,
    this.canceledOn,
    this.orderCancelReasonID,
    this.orderCancelReasonName,
    super.cancelNote,
    this.isCreatedByEmployee,
    this.createdBy,
    this.createdByName,
    this.isPricingFeeRequired,
    this.pricingFee,
    this.pricingFeeDate,
    this.pricingFeeBy,
    this.pricingFeeNotes,
    this.isPricingFeePaid,
    this.hasDeliveryNote,
    this.hasReview,
    this.isFromEmail,
    this.emailTitle,
    this.quotationTime,
    this.deliveryTime,
    this.quotationActualTime,
    this.deliveryActualTime,
    this.evaluationPer,
    this.sosActive,
    super.isSAPAccepted,
    super.sapFileContent,
    super.sapFileContentType,
    super.sapFileName,
    super.sapAcceptedOn,
    this.expectedBudget,
    this.termsAndConditions,
    this.recommendedSuppliers,
    super.currentApprover,
    super.currentApproverName,
    super.details,
    super.quotations,
    this.accepttedQuotation,
    this.leftLeftProcessTime,
    super.comments,
    super.attachments,
    this.employee,
    this.customer,
    super.approvals,
  });

  final String? description;
  final String? prNumber;
  final String? orderStatusName;
  final String? submitedOn;
  final bool? isDelayed;
  final String? dnSentOn;
  final bool? isDeleted;
  final String? cancelBy;
  final String? cancelByName;
  final String? canceledOn;
  final String? orderCancelReasonID;
  final String? orderCancelReasonName;
  final bool? isCreatedByEmployee;
  final String? createdBy;
  final String? createdByName;
  final bool? isPricingFeeRequired;
  final double? pricingFee;
  final String? pricingFeeDate;
  final String? pricingFeeBy;
  final String? pricingFeeNotes;
  final bool? isPricingFeePaid;
  final bool? hasDeliveryNote;
  final bool? hasReview;
  final bool? isFromEmail;
  final String? emailTitle;
  final int? quotationTime;
  final int? deliveryTime;
  final double? quotationActualTime;
  final double? deliveryActualTime;
  final double? evaluationPer;
  final bool? sosActive;
  final double? expectedBudget;
  final String? termsAndConditions;
  final String? recommendedSuppliers;
  final QuotationsModel? accepttedQuotation;
  final double? leftLeftProcessTime;
  final String? employee;
  final String? customer;

  factory OrdersModel.fromJson(Map<String, dynamic> json) =>
      _$OrdersModelFromJson(json);

  Map<String, dynamic> toJson() => _$OrdersModelToJson(this);

  OrdersModel copyWith({
    String? orderID,
    String? projectID,
    String? projectName,
    int? orderNo,
    String? description,
    String? prNumber,
    String? createdOn,
    OrderStatusType? orderStatus,
    String? orderStatusName,
    OrderTypes? orderType,
    String? orderTypeName,
    String? submitedOn,
    String? organizationID,
    String? organizationName,
    String? employeeID,
    String? employeeName,
    bool? isDelayed,
    String? customerID,
    String? customerName,
    String? dnSentOn,
    bool? isDeleted,
    String? cancelBy,
    String? cancelByName,
    String? canceledOn,
    String? orderCancelReasonID,
    String? orderCancelReasonName,
    String? cancelNote,
    bool? isCreatedByEmployee,
    String? createdBy,
    String? createdByName,
    bool? isPricingFeeRequired,
    double? pricingFee,
    String? pricingFeeDate,
    String? pricingFeeBy,
    String? pricingFeeNotes,
    bool? isPricingFeePaid,
    bool? hasDeliveryNote,
    bool? hasReview,
    bool? isFromEmail,
    String? emailTitle,
    bool? isSAPAccepted,
    String? sapFileContent,
    String? sapFileContentType,
    String? sapFileName,
    String? sapAcceptedOn,
    List<OrderDetailsModel>? details,
    List<QuotationsModel>? quotations,
    QuotationsModel? accepttedQuotation,
    double? leftLeftProcessTime,
    List<CommentsModel>? comments,
    List<AttachmentsModel>? attachments,
    String? employee,
    String? customer,
  }) =>
      OrdersModel(
        orderID: orderID ?? this.orderID,
        projectID: projectID ?? this.projectID,
        projectName: projectName ?? this.projectName,
        orderNo: orderNo ?? this.orderNo,
        prNumber: prNumber ?? this.prNumber,
        description: description ?? this.description,
        createdOn: createdOn ?? this.createdOn,
        orderStatus: orderStatus ?? this.orderStatus,
        orderStatusName: orderStatusName ?? this.orderStatusName,
        orderType: orderType ?? this.orderType,
        orderTypeName: orderTypeName ?? this.orderTypeName,
        submitedOn: submitedOn ?? this.submitedOn,
        organizationID: organizationID ?? this.organizationID,
        organizationName: organizationName ?? this.organizationName,
        employeeID: employeeID ?? this.employeeID,
        employeeName: employeeName ?? this.employeeName,
        isDelayed: isDelayed ?? this.isDelayed,
        customerID: customerID ?? this.customerID,
        customerName: customerName ?? this.customerName,
        dnSentOn: dnSentOn ?? this.dnSentOn,
        isDeleted: isDeleted ?? this.isDeleted,
        cancelBy: cancelBy ?? this.cancelBy,
        cancelByName: cancelByName ?? this.cancelByName,
        canceledOn: canceledOn ?? this.canceledOn,
        orderCancelReasonID: orderCancelReasonID ?? this.orderCancelReasonID,
        orderCancelReasonName:
            orderCancelReasonName ?? this.orderCancelReasonName,
        cancelNote: cancelNote ?? this.cancelNote,
        isCreatedByEmployee: isCreatedByEmployee ?? this.isCreatedByEmployee,
        createdBy: createdBy ?? this.createdBy,
        createdByName: createdByName ?? this.createdByName,
        isPricingFeeRequired: isPricingFeeRequired ?? this.isPricingFeeRequired,
        pricingFee: pricingFee ?? this.pricingFee,
        pricingFeeDate: pricingFeeDate ?? this.pricingFeeDate,
        pricingFeeBy: pricingFeeBy ?? this.pricingFeeBy,
        pricingFeeNotes: pricingFeeNotes ?? this.pricingFeeNotes,
        isPricingFeePaid: isPricingFeePaid ?? this.isPricingFeePaid,
        hasDeliveryNote: hasDeliveryNote ?? this.hasDeliveryNote,
        hasReview: hasReview ?? this.hasReview,
        isFromEmail: isFromEmail ?? this.isFromEmail,
        emailTitle: emailTitle ?? this.emailTitle,
        isSAPAccepted: isSAPAccepted ?? this.isSAPAccepted,
        sapFileContent: sapFileContent ?? this.sapFileContent,
        sapFileContentType: sapFileContentType ?? this.sapFileContentType,
        sapFileName: sapFileName ?? this.sapFileName,
        sapAcceptedOn: sapAcceptedOn ?? this.sapAcceptedOn,
        details: details ?? this.details,
        quotations: quotations ?? this.quotations,
        accepttedQuotation: accepttedQuotation ?? this.accepttedQuotation,
        leftLeftProcessTime: leftLeftProcessTime ?? this.leftLeftProcessTime,
        comments: comments ?? this.comments,
        attachments: attachments ?? this.attachments,
        employee: employee ?? this.employee,
        customer: customer ?? this.customer,
      );
}
