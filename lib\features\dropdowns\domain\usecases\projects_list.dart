import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/dropdowns/data/repositories/drop_downs_repository_impl.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/domain/repositories/drop_downs_repository.dart';

final projectsListUseCaseProvider = Provider<ProjectsList>(
  (ref) => ProjectsList(ref.watch(dropDownsRepositoryImpl)),
);

class ProjectsList
    implements UseCase<List<DropDownEntity>, ProjectsListParams> {
  final DropDownsRepository _repository;

  ProjectsList(this._repository);

  @override
  Future<Either<Failure, List<DropDownEntity>>> call(
      ProjectsListParams params) async {
    return _repository.projectsList(params);
  }
}

class ProjectsListParams {
  final String? orgID;

  const ProjectsListParams({required this.orgID});
}
