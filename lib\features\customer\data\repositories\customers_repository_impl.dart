import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/exceptions/exceptions.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/features/customer/data/datasources/customers_remote_data_source.dart';
import 'package:mdd/features/customer/data/models/customers_model.dart';
import 'package:mdd/features/customer/domain/repositories/customers_repository.dart';
import 'package:mdd/features/customer/domain/usecases/customers.dart';
import 'package:mdd/features/customer/domain/usecases/update_customer.dart';

final customersRepositoryImpl =
    Provider<CustomersRepositoryImpl>((ref) => CustomersRepositoryImpl(
          ref.watch(customersRemoteDataSourceImpl),
        ));

class CustomersRepositoryImpl implements CustomersRepository {
  final CustomersRemoteDataSource _customersRemoteDataSource;

  CustomersRepositoryImpl(this._customersRemoteDataSource);

  @override
  Future<Either<Failure, List<CustomersModel>>> getCustomers(
      CustomersParams params) async {
    try {
      final res = await _customersRemoteDataSource.getCustomers(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }

  @override
  Future<Either<Failure, ResultModel>> createNewCustomer(params) async {
    try {
      final res = await _customersRemoteDataSource.createNewCustomer(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }

  @override
  Future<Either<Failure, ResultModel>> updateCustomer(
      UpdateCustomerParams params) async {
    try {
      final res = await _customersRemoteDataSource.updateCustomer(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }
}
