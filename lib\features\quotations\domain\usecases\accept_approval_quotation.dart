import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/data/repositories/quotations_repository_impl.dart';
import 'package:mdd/features/quotations/domain/repositories/quotations_repository.dart';

final acceptApprovalQuotationUseCaseProvider =
    Provider<AcceptApprovalQuotation>(
        (ref) => AcceptApprovalQuotation(ref.watch(quotationsRepositoryImpl)));

class AcceptApprovalQuotation
    implements UseCase<ResultModel, AcceptApprovalQuotationParams> {
  final QuotationsRepository _quotationsRepository;

  AcceptApprovalQuotation(this._quotationsRepository);

  @override
  Future<Either<Failure, ResultModel>> call(
      AcceptApprovalQuotationParams params) async {
    return await _quotationsRepository.acceptApprovalQuotation(params);
  }
}

class AcceptApprovalQuotationParams {
  final QuotationsModel quotation;
  const AcceptApprovalQuotationParams(this.quotation);
}
