import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/utils/enums.dart';

part 'approval_model.g.dart';

@JsonSerializable()
class ApprovalModel {
  ApprovalModel({
    required this.orderApprovalID,
    required this.quotationApprovalID,
    required this.orderID,
    required this.quotationID,
    required this.priority,
    required this.approver,
    required this.approverName,
    required this.status,
    this.approvedOn,
    this.notes,
    required this.approvalLimit,
  });

  final String? orderApprovalID;
  final String? quotationApprovalID;
  final String orderID;
  final String? quotationID;
  final int priority;
  final String approver;
  final String approverName;
  final ApprovalStatusType status;
  final String? approvedOn;
  final String? notes;
  final double approvalLimit;

  factory ApprovalModel.fromJson(Map<String, dynamic> json) =>
      _$ApprovalModelFromJson(json);

  Map<String, dynamic> toJson() => _$ApprovalModelToJson(this);
}
