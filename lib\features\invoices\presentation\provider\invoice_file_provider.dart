import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/attachments/domain/entites/attachments_entity.dart';
import 'package:mdd/features/invoices/domain/usecases/invoice_file.dart';

final invoiceFileProvider =
    StateNotifierProvider<InvoiceFileProvider, ViewState>(
  (ref) => InvoiceFileProvider(ref.watch(invoiceFileUseCaseProvider)),
);

class InvoiceFileProvider extends BaseProvider<AttachmentsEntity> {
  InvoiceFileProvider(this._invoiceFile);
  final InvoiceFile _invoiceFile;

  Future<void> fetchGetInvoiceFileById(String? invoiceId) async {
    setLoadingState();

    final response = await _invoiceFile.call(InvoiceFileParams(id: invoiceId));
    await response.fold((failure) {
      setErrorState(failure.message);
    }, (invoice) async {
      if (invoice != null) {
        setLoadedState(invoice);
      }
    });
  }
}

typedef MyCallback = void Function(AttachmentsEntity? invoice);
