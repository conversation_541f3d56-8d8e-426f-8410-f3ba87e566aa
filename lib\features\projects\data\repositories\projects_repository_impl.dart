import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/exceptions/exceptions.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/features/projects/data/datasources/projects_remote_data_source.dart';
import 'package:mdd/features/projects/data/models/projects_model.dart';
import 'package:mdd/features/projects/domain/repositories/projects_repository.dart';
import 'package:mdd/features/projects/domain/usecases/create_new_project.dart';
import 'package:mdd/features/projects/domain/usecases/projects.dart';
import 'package:mdd/features/projects/domain/usecases/update_project.dart';

final projectsRepositoryImpl =
    Provider<ProjectsRepositoryImpl>((ref) => ProjectsRepositoryImpl(
          ref.watch(projectsRemoteDataSourceImpl),
        ));

class ProjectsRepositoryImpl implements ProjectsRepository {
  final ProjectsRemoteDataSource _projectsRemoteDataSource;
  ProjectsRepositoryImpl(this._projectsRemoteDataSource);
  @override
  Future<Either<Failure, List<ProjectsModel>>> getProjects(
      ProjectsParams params) async {
    try {
      final res = await _projectsRemoteDataSource.getProjects(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }

  @override
  Future<Either<Failure, ResultModel>> createNewProject(
      CreateNewProjectParams params) async {
    try {
      final res = await _projectsRemoteDataSource.createNewProject(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }

  @override
  Future<Either<Failure, ResultModel>> updateProject(
      UpdateProjectParams params) async {
    try {
      final res = await _projectsRemoteDataSource.updateProject(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }
}
