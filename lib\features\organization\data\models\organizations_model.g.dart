// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'organizations_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrgsModel _$OrgsModelFromJson(Map<String, dynamic> json) => OrgsModel(
      organizationID: json['organizationID'] as String?,
      organizationNo: json['organizationNo'] as int?,
      name: json['name'] as String?,
      cr: json['cr'] as String?,
      fileName: json['fileName'] as String?,
      fileContent: json['fileContent'] as String?,
      fileContentType: json['fileContentType'] as String?,
      landLine: json['landLine'] as String?,
      street: json['street'] as String?,
      zipCode: json['zipCode'] as String?,
      cityID: json['cityID'] as String?,
      cityName: json['cityName'] as String?,
      email: json['email'] as String?,
      website: json['website'] as String?,
      registerDate: json['registerDate'] as String?,
      creditBalance: (json['creditBalance'] as num?)?.toDouble(),
      creditDays: json['creditDays'] as int?,
      status: $enumDecodeNullable(_$OrganizationStatusEnumMap, json['status']),
      statusName: json['statusName'] as String?,
      type: $enumDecodeNullable(_$OrganizationTypeEnumMap, json['type']),
      typeName: json['typeName'] as String?,
      isPORequiredOnApproval: json['isPORequiredOnApproval'] as bool?,
      isPONumberAutoGenerated: json['isPONumberAutoGenerated'] as bool?,
      isAllowedApproveQuotation: json['isAllowedApproveQuotation'] as bool?,
      isCredit: json['isCredit'] as bool?,
      logo: json['logo'] as String?,
      vatNumber: json['vatNumber'] as String?,
    );

Map<String, dynamic> _$OrgsModelToJson(OrgsModel instance) => <String, dynamic>{
      'organizationID': instance.organizationID,
      'name': instance.name,
      'cr': instance.cr,
      'landLine': instance.landLine,
      'street': instance.street,
      'zipCode': instance.zipCode,
      'cityID': instance.cityID,
      'cityName': instance.cityName,
      'email': instance.email,
      'website': instance.website,
      'creditBalance': instance.creditBalance,
      'creditDays': instance.creditDays,
      'status': _$OrganizationStatusEnumMap[instance.status],
      'type': _$OrganizationTypeEnumMap[instance.type],
      'vatNumber': instance.vatNumber,
      'isPORequiredOnApproval': instance.isPORequiredOnApproval,
      'isPONumberAutoGenerated': instance.isPONumberAutoGenerated,
      'isAllowedApproveQuotation': instance.isAllowedApproveQuotation,
      'organizationNo': instance.organizationNo,
      'fileName': instance.fileName,
      'fileContent': instance.fileContent,
      'fileContentType': instance.fileContentType,
      'registerDate': instance.registerDate,
      'statusName': instance.statusName,
      'typeName': instance.typeName,
      'isCredit': instance.isCredit,
      'logo': instance.logo,
    };

const _$OrganizationStatusEnumMap = {
  OrganizationStatus.emailNotConfrimed: 0,
  OrganizationStatus.aprroved: 1,
  OrganizationStatus.waittingForApproval: 2,
  OrganizationStatus.informationMissed: 3,
  OrganizationStatus.blackListed: 4,
};

const _$OrganizationTypeEnumMap = {
  OrganizationType.unclassified: 0,
  OrganizationType.corporate: 1,
  OrganizationType.sme: 2,
};
