import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';


import '../../theme/colors.dart';
import '../../theme/dimensions.dart';

class DropDownWidget<T> extends StatelessWidget {
  final String? validatorMessage;
  final String title;
  final List<T> items;
  final Color? fillColor;
  final Color? hintColor;
  final Color? borderColor;
  final String? icon;
  final T? value;
  final String Function(T) itemAsString;
  final void Function(T?) onChanged;
  final bool Function(T, T)? compareFn;
  final Future<List<T>> Function(String)? asyncItems;
  final bool showSelectedItems;
  final bool enabled;

  const DropDownWidget({
    super.key,
    this.validatorMessage,
    required this.title,
    this.items = const [],
    required this.onChanged,
    required this.itemAsString,
    this.compareFn,
    this.asyncItems,
    this.showSelectedItems = false,
    this.enabled = true,
    this.fillColor,
    this.hintColor,
    this.borderColor,
    this.icon,
    this.value,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownSearch<T>(

      asyncItems: asyncItems,
      validator: (value) {
        if (value == null && validatorMessage != null) {
          return validatorMessage;
        }
        return null;
      },

      autoValidateMode: AutovalidateMode.onUserInteraction,
      dropdownDecoratorProps: DropDownDecoratorProps(
        dropdownSearchDecoration: InputDecoration(

          hintText: title,
          hintStyle: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: hintColor ?? AppColors.tabTextColor,
            fontSize: AppDimensions.kSizeLarge,
          ),
          prefixIcon: icon != null
              ? Padding(
            padding: const EdgeInsets.all(AppDimensions.kSizeMedium),
            child: SvgPicture.asset(
              icon!,
              colorFilter: const ColorFilter.mode(
                  AppColors.disabledColor1, BlendMode.srcIn),
            ),
          )
              : null,
          fillColor: fillColor ?? AppColors.cardColor,
          focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: borderColor ?? AppColors.borderColor1.withOpacity(0.71),
              ),
              borderRadius: BorderRadius.circular(10)),
          enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: borderColor ?? AppColors.borderColor1.withOpacity(0.71),
              ),
              borderRadius: BorderRadius.circular(10)),
          disabledBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: borderColor ?? AppColors.borderColor1.withOpacity(0.71),
              ),
              borderRadius: BorderRadius.circular(10)),
          border: OutlineInputBorder(
            borderSide: BorderSide(
              color: AppColors.borderColor1.withOpacity(0.71),
            ),
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ),
      items: items,

      itemAsString: itemAsString,
      onChanged: onChanged,
      selectedItem: value,
      compareFn: compareFn,
      enabled: enabled,
      dropdownButtonProps: const DropdownButtonProps(color: AppColors.primary),
      clearButtonProps: const ClearButtonProps(isVisible: true),
      popupProps: PopupProps.menu(
        menuProps: const MenuProps(
            backgroundColor: AppColors.cardColor
          // Change the popup background color here
        ),

        showSearchBox: true,
        searchFieldProps: TextFieldProps(
            decoration: InputDecoration(
                enabledBorder: OutlineInputBorder(
                    borderRadius:  BorderRadius.circular(10),

                    borderSide: const BorderSide(

                      color: AppColors.borderColor1,
                    )
                ),
                focusedBorder: OutlineInputBorder(
                    borderRadius:  BorderRadius.circular(10),

                    borderSide: const BorderSide(

                      color: AppColors.borderColor1,
                    )
                )


            )),
        showSelectedItems: showSelectedItems,
        isFilterOnline: true,


        // menuProps: const MenuProps(
        //   backgroundColor: AppColors.cardDetailsBackground,
        // ),
      ),
    );
  }
}

class DropDownMultiSelectionWidget<T> extends StatelessWidget {
  final String? validatorMessage;
  final String title;
  final List<T> items;
  final Color? fillColor;
  final Color? hintColor;
  final Color? borderColor;
  final String? icon;
  final List<T> value;
  final String Function(T) itemAsString;
  final void Function(List<T>) onChanged;
  final bool Function(T, T)? compareFn;
  final Future<List<T>> Function(String)? asyncItems;
  final bool showSelectedItems;
  final bool enabled;

  const DropDownMultiSelectionWidget({
    super.key,
    this.validatorMessage,
    required this.title,
    this.items = const [],
    required this.onChanged,
    required this.itemAsString,
    this.compareFn,
    this.asyncItems,
    this.showSelectedItems = false,
    this.enabled = true,
    this.fillColor,
    this.hintColor,
    this.borderColor,
    this.icon,
    this.value = const [],
  });

  @override
  Widget build(BuildContext context) {
    return DropdownSearch<T>.multiSelection(
      asyncItems: asyncItems,
      validator: (value) {
        if (value == null && validatorMessage != null) {
          return validatorMessage;
        }
        return null;
      },
      autoValidateMode: AutovalidateMode.onUserInteraction,
      dropdownDecoratorProps: DropDownDecoratorProps(
        dropdownSearchDecoration: InputDecoration(
          hintText: title,
          hintStyle: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: hintColor ?? AppColors.tabTextColor,
            fontSize: AppDimensions.kSizeLarge,
          ),
          prefixIcon: icon != null
              ? Padding(
            padding: const EdgeInsets.all(AppDimensions.kSizeMedium),
            child: SvgPicture.asset(
              icon!,
              colorFilter: const ColorFilter.mode(
                  AppColors.disabledColor1, BlendMode.srcIn),
            ),
          )
              : null,
          fillColor: fillColor ?? AppColors.cardDetailsBackground,
          focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: borderColor ?? AppColors.borderColor1.withOpacity(0.71),
              ),
              borderRadius: BorderRadius.circular(10)),
          enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: borderColor ?? AppColors.borderColor1.withOpacity(0.71),
              ),
              borderRadius: BorderRadius.circular(10)),
          disabledBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: borderColor ?? AppColors.borderColor1.withOpacity(0.71),
              ),
              borderRadius: BorderRadius.circular(10)),
          border: OutlineInputBorder(
            borderSide: BorderSide(
              color: AppColors.borderColor1.withOpacity(0.71),
            ),
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ),
      items: items,
      itemAsString: itemAsString,
      onChanged: onChanged,
      selectedItems: value,
      compareFn: compareFn,
      enabled: enabled,
      dropdownButtonProps: const DropdownButtonProps(color: AppColors.primary),
      clearButtonProps: const ClearButtonProps(isVisible: true),
      popupProps: PopupPropsMultiSelection.menu(
        showSearchBox: true,
        showSelectedItems: showSelectedItems,
        isFilterOnline: true,

        // menuProps: const MenuProps(
        //   backgroundColor: AppColors.cardDetailsBackground,
        // ),
      ),
    );
  }
}
