import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/invoices/data/repositories/invoices_repository_impl.dart';
import 'package:mdd/features/invoices/domain/entities/invoices_entity.dart';
import 'package:mdd/features/invoices/domain/repositories/invoices_repository.dart';

final invoicesUseCaseProvider =
    Provider<Invoices>((ref) => Invoices(ref.watch(invoicesRepositoryImpl)));

class Invoices implements UseCase<List<InvoicesEntity>, InvoicesParams> {
  final InvoicesRepository _invoicesRepository;

  Invoices(this._invoicesRepository);

  @override
  Future<Either<Failure, List<InvoicesEntity>>> call(
      InvoicesParams params) async {
    return await _invoicesRepository.getInvoices(params);
  }
}

class InvoicesParams {
  final int page;
  final int? invoiceStatus;
  final String? invoiceNo;
  const InvoicesParams(
      {required this.page, this.invoiceStatus, this.invoiceNo});
}
