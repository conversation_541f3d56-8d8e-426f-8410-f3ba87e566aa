import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/data/repositories/quotations_repository_impl.dart';
import 'package:mdd/features/quotations/domain/repositories/quotations_repository.dart';

final acceptQuotationUseCaseProvider = Provider<AcceptQuotation>(
    (ref) => AcceptQuotation(ref.watch(quotationsRepositoryImpl)));

class AcceptQuotation implements UseCase<ResultModel, AcceptQuotationParams> {
  final QuotationsRepository _quotationsRepository;

  AcceptQuotation(this._quotationsRepository);

  @override
  Future<Either<Failure, ResultModel>> call(
      AcceptQuotationParams params) async {
    return await _quotationsRepository.acceptQuotation(params);
  }
}

class AcceptQuotationParams {
  final QuotationsModel model;

  const AcceptQuotationParams(this.model);
}
