import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/domain/usecases/categories.dart';

final selectedCategoryProvider = StateProvider<DropDownEntity?>((ref) => null);

final categoriesProvider =
    StateNotifierProvider.autoDispose<CategoriesProvider, ViewState>(
  (ref) => CategoriesProvider(
    ref.watch(categoriesUseCaseProvider),
  ),
);

class CategoriesProvider extends BaseProvider<List<DropDownEntity>> {
  final Categories _categories;

  CategoriesProvider(this._categories);
  Future<void> fetchCategories() async {
    setLoadingState();
    final response = await _categories.call(NoParams());
    response.fold(
      (failure) => setErrorState(failure.message),
      setLoadedState,
    );
  }
}
