import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/quotations/data/models/approval_model.dart';
import 'package:mdd/features/quotations/data/models/quotation_details_model.dart';
import 'package:mdd/features/quotations/domain/entities/quotations_entity.dart';
import 'package:mdd/utils/enums.dart';

part 'quotations_model.g.dart';

@JsonSerializable(explicitToJson: true)
class QuotationsModel extends QuotationsEntity {
  QuotationsModel({
    required super.quotationID,
    required super.quotationNo,
    required super.orderID,
    super.orderNo,
    this.prNumber,
    this.customerPurchaseOrderNo,
    this.fileName,
    this.fileContent,
    this.fileContentType,
    this.isAcceptedByEmployee,
    this.acceptedByEmployeeID,
    this.acceptedByEmployeeName,
    this.createdBy,
    this.createdByName,
    this.createdOn,
    super.expiredDate,
    super.submitDate,
    this.submittedBy,
    this.mustAprroveAll,
    this.acceptedBy,
    this.acceptedByName,
    this.acceptDate,
    this.rejectDate,
    this.rejectNotes,
    required super.price,
    required this.priceWithNoVAT,
    super.vat,
    super.vatPer,
    super.serviceFees,
    this.serviceFeesPer,
    super.serviceFeesCalculated,
    super.serviceFeesDiscount,
    this.requestChangeFees,
    this.initialApproval,
    this.initialApprovalBy,
    this.initialApprovalByName,
    this.initialApprovalOn,
    this.finalApproval,
    this.finalApprovalBy,
    this.finalApprovalByName,
    this.finalApprovalOn,
    super.additionalFees,
    super.deliveryFees,
    this.deliverTo,
    this.location,
    this.googleMapLocation,
    this.mobileNo,
    required super.total,
    this.isSOSActive,
    this.sosFees,
    this.sosCommission,
    super.conditions,
    this.isReNew,
    this.renewalDays,
    this.rejectReasonID,
    this.rejectReasonName,
    this.includeServiceFeesToItems,
    super.quotationStatus,
    this.statusName,
    this.puchaseOrderTotal,
    this.employeeCommission,
    this.orderStatus,
    this.orderStatusName,
    this.employeeID,
    this.employeeName,
    this.employeeMobile,
    this.organizationName,
    super.organizationID,
    super.orderType,
    this.orderBy,
    this.orderByName,
    this.acceptanceCode,
    this.currentApprover,
    this.currentApproverName,
    this.profits,
    super.items,
    super.deliveryInDays,
    this.approvals,
  });

  final String? prNumber;
  final String? statusName;
  final String? fileName;
  final String? fileContent;
  final String? fileContentType;
  final bool? mustAprroveAll;
  final bool? includeServiceFeesToItems;
  final String? customerPurchaseOrderNo;
  final bool? isAcceptedByEmployee;
  final String? acceptedByEmployeeID;
  final String? acceptedByEmployeeName;
  final String? createdBy;
  final String? createdByName;
  final String? createdOn;
  final String? submittedBy;
  final String? acceptedBy;
  final String? acceptedByName;
  final String? acceptDate;
  final String? rejectDate;
  final String? rejectNotes;
  final double priceWithNoVAT;
  final double? serviceFeesPer;
  final bool? requestChangeFees;
  final bool? initialApproval;
  final String? initialApprovalBy;
  final String? initialApprovalByName;
  final String? initialApprovalOn;
  final bool? finalApproval;
  final String? finalApprovalBy;
  final String? finalApprovalByName;
  final String? finalApprovalOn;
  final String? deliverTo;
  final String? location;
  final String? googleMapLocation;
  final String? mobileNo;
  final bool? isSOSActive;
  final double? sosFees;
  final double? sosCommission;
  final bool? isReNew;
  final int? renewalDays;
  final String? rejectReasonID;
  final String? rejectReasonName;
  final double? puchaseOrderTotal;
  final double? employeeCommission;
  final int? orderStatus;
  final String? orderStatusName;
  final String? employeeID;
  final String? employeeName;
  final String? employeeMobile;
  final String? organizationName;
  final String? orderBy;
  final String? orderByName;
  final String? acceptanceCode;
  final String? currentApprover;
  final String? currentApproverName;
  final double? profits;
  final List<ApprovalModel>? approvals;

  factory QuotationsModel.fromJson(Map<String, dynamic> json) =>
      _$QuotationsModelFromJson(json);

  Map<String, dynamic> toJson() => _$QuotationsModelToJson(this);
  QuotationsModel copyWith({
    String? quotationID,
    String? quotationNo,
    String? orderID,
    int? orderNo,
    String? prNumber,
    String? customerPurchaseOrderNo,
    String? fileName,
    String? fileContent,
    String? fileContentType,
    bool? isAcceptedByEmployee,
    String? acceptedByEmployeeID,
    String? acceptedByEmployeeName,
    String? createdBy,
    String? createdByName,
    String? createdOn,
    String? expiredDate,
    String? submitDate,
    String? submittedBy,
    bool? mustAprroveAll,
    String? acceptedBy,
    String? acceptedByName,
    String? acceptDate,
    String? rejectDate,
    String? rejectNotes,
    double? price,
    double? priceWithNoVAT,
    double? vat,
    double? vatPer,
    double? serviceFeesPer,
    double? serviceFees,
    double? serviceFeesCalculated,
    double? serviceFeesDiscount,
    bool? requestChangeFees,
    bool? initialApproval,
    String? initialApprovalBy,
    String? initialApprovalByName,
    String? initialApprovalOn,
    bool? finalApproval,
    String? finalApprovalBy,
    String? finalApprovalByName,
    String? finalApprovalOn,
    double? additionalFees,
    double? deliveryFees,
    String? deliverTo,
    String? location,
    String? googleMapLocation,
    String? mobileNo,
    double? total,
    bool? isSOSActive,
    double? sosFees,
    double? sosCommission,
    String? conditions,
    bool? isReNew,
    int? renewalDays,
    String? rejectReasonID,
    String? rejectReasonName,
    bool? includeServiceFeesToItems,
    QuotationsStatusType? quotationStatus,
    String? statusName,
    double? puchaseOrderTotal,
    double? employeeCommission,
    int? orderStatus,
    String? orderStatusName,
    String? employeeID,
    String? employeeName,
    String? employeeMobile,
    String? organizationName,
    String? organizationID,
    int? orderType,
    String? orderBy,
    String? orderByName,
    String? acceptanceCode,
    String? currentApprover,
    String? currentApproverName,
    int? deliveryInDays,
    double? profits,
    List<QuotationDetailsModel>? items,
    List<ApprovalModel>? approvals,
  }) =>
      QuotationsModel(
        quotationID: quotationID ?? this.quotationID,
        quotationNo: quotationNo ?? this.quotationNo,
        orderID: orderID ?? this.orderID,
        orderNo: orderNo ?? this.orderNo,
        prNumber: prNumber ?? this.prNumber,
        customerPurchaseOrderNo:
        customerPurchaseOrderNo ?? this.customerPurchaseOrderNo,
        fileName: fileName ?? this.fileName,
        fileContent: fileContent ?? this.fileContent,
        fileContentType: fileContentType ?? this.fileContentType,
        isAcceptedByEmployee: isAcceptedByEmployee ?? this.isAcceptedByEmployee,
        acceptedByEmployeeID: acceptedByEmployeeID ?? this.acceptedByEmployeeID,
        acceptedByEmployeeName:
        acceptedByEmployeeName ?? this.acceptedByEmployeeName,
        createdBy: createdBy ?? this.createdBy,
        createdByName: createdByName ?? this.createdByName,
        createdOn: createdOn ?? this.createdOn,
        expiredDate: expiredDate ?? this.expiredDate,
        submitDate: submitDate ?? this.submitDate,
        submittedBy: submittedBy ?? this.submittedBy,
        mustAprroveAll: mustAprroveAll ?? this.mustAprroveAll,
        acceptedBy: acceptedBy ?? this.acceptedBy,
        acceptedByName: acceptedByName ?? this.acceptedByName,
        acceptDate: acceptDate ?? this.acceptDate,
        rejectDate: rejectDate ?? this.rejectDate,
        rejectNotes: rejectNotes ?? this.rejectNotes,
        price: price ?? this.price,
        priceWithNoVAT: priceWithNoVAT ?? this.priceWithNoVAT,
        vat: vat ?? this.vat,
        vatPer: vatPer ?? this.vatPer,
        serviceFeesPer: serviceFeesPer ?? this.serviceFeesPer,
        serviceFees: serviceFees ?? this.serviceFees,
        serviceFeesCalculated:
        serviceFeesCalculated ?? this.serviceFeesCalculated,
        serviceFeesDiscount: serviceFeesDiscount ?? this.serviceFeesDiscount,
        requestChangeFees: requestChangeFees ?? this.requestChangeFees,
        initialApproval: initialApproval ?? this.initialApproval,
        initialApprovalBy: initialApprovalBy ?? this.initialApprovalBy,
        initialApprovalByName:
        initialApprovalByName ?? this.initialApprovalByName,
        initialApprovalOn: initialApprovalOn ?? this.initialApprovalOn,
        finalApproval: finalApproval ?? this.finalApproval,
        finalApprovalBy: finalApprovalBy ?? this.finalApprovalBy,
        finalApprovalByName: finalApprovalByName ?? this.finalApprovalByName,
        finalApprovalOn: finalApprovalOn ?? this.finalApprovalOn,
        additionalFees: additionalFees ?? this.additionalFees,
        deliveryFees: deliveryFees ?? this.deliveryFees,
        deliverTo: deliverTo ?? this.deliverTo,
        location: location ?? this.location,
        googleMapLocation: googleMapLocation ?? this.googleMapLocation,
        mobileNo: mobileNo ?? this.mobileNo,
        total: total ?? this.total,
        isSOSActive: isSOSActive ?? this.isSOSActive,
        sosFees: sosFees ?? this.sosFees,
        sosCommission: sosCommission ?? this.sosCommission,
        conditions: conditions ?? this.conditions,
        isReNew: isReNew ?? this.isReNew,
        renewalDays: renewalDays ?? this.renewalDays,
        rejectReasonID: rejectReasonID ?? this.rejectReasonID,
        rejectReasonName: rejectReasonName ?? this.rejectReasonName,
        includeServiceFeesToItems:
        includeServiceFeesToItems ?? this.includeServiceFeesToItems,
        quotationStatus: quotationStatus ?? this.quotationStatus,
        statusName: statusName ?? this.statusName,
        puchaseOrderTotal: puchaseOrderTotal ?? this.puchaseOrderTotal,
        employeeCommission: employeeCommission ?? this.employeeCommission,
        orderStatus: orderStatus ?? this.orderStatus,
        orderStatusName: orderStatusName ?? this.orderStatusName,
        employeeID: employeeID ?? this.employeeID,
        employeeName: employeeName ?? this.employeeName,
        employeeMobile: employeeMobile ?? this.employeeMobile,
        organizationName: organizationName ?? this.organizationName,
        organizationID: organizationID ?? this.organizationID,
        orderType: orderType ?? this.orderType,
        orderBy: orderBy ?? this.orderBy,
        orderByName: orderByName ?? this.orderByName,
        acceptanceCode: acceptanceCode ?? this.acceptanceCode,
        currentApprover: currentApprover ?? this.currentApprover,
        currentApproverName: currentApproverName ?? this.currentApproverName,
        deliveryInDays: deliveryInDays ?? this.deliveryInDays,
        profits: profits ?? this.profits,
        items: items ?? this.items,
        approvals: approvals ?? this.approvals,
      );
}
