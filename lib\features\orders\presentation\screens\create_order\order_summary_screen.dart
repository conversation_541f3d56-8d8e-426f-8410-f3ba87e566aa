import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/description_widget/card_item_widget.dart';
import 'package:mdd/core/widgets/description_widget/description_card_item_widget.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/core/widgets/title_item_widget.dart';
import 'package:mdd/features/attachments/presentation/widgets/attachments_list_view.dart';
import 'package:mdd/features/dropdowns/presentation/providers/projects_list_provider.dart';
import 'package:mdd/features/orders/presentation/provider/add_order_provider.dart';
import 'package:mdd/features/orders/presentation/provider/create_order_provider.dart';
import 'package:mdd/features/orders/presentation/provider/update_order_provider.dart';
import 'package:mdd/features/orders/presentation/screens/create_order/order_cart_view.dart';
import 'package:mdd/features/user/domain/entities/user_entity.dart';
import 'package:mdd/features/user/presentation/provider/user_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enum_extensions.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

@RoutePage()
class OrderSummaryScreen extends ConsumerStatefulWidget {
  const OrderSummaryScreen({
    super.key,
  });

  @override
  ConsumerState createState() => _OrderSummaryScreenState();
}

class _OrderSummaryScreenState extends ConsumerState<OrderSummaryScreen> {
  @override
  Widget build(BuildContext context) {
    ref
      ..listen(createOrderProvider, (_, state) {
        if (state is LoadingViewState) {
          UiHelper.showLoadingDialog(context);
        } else if (state is LoadedViewState<ResultModel>) {
          context.popRoute();
          UiHelper.showNotification(
            'send_order_success'.tr(),
            notificationType: NotificationType.success,
          );
          ref.read(addOrderProvider).clearAll();
          context.router.popUntilRoot();
        } else if (state is ErrorViewState) {
          context.maybePop();
          UiHelper.showNotification(
            'try_again'.tr(),
          );
        }
      })
      ..listen(updateOrderProvider, (_, state) {
        if (state is LoadingViewState) {
          UiHelper.showLoadingDialog(context);
        } else if (state is LoadedViewState<ResultModel>) {
          context.popRoute();
          UiHelper.showNotification(
            'send_order_success'.tr(),
            notificationType: NotificationType.success,
          );
          ref.read(addOrderProvider).clearAll();
          context.router.popUntilRoot();
        } else if (state is ErrorViewState) {
          Navigator.of(context).pop();
          UiHelper.showNotification(state.errorMessage);
        }
      });
    return Scaffold(
      appBar: const MainAppBar(),
      body: Column(
        children: [
          DetailsCustomBar(
            title: 'add_order'.tr(),
            activeIndex: 2,
          ),
          TitleItemWidget(title: 'order_summary'.tr()),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  CardItemsWidget(
                    image: "assets/images/orders_image.png",
                    imageBackground: AppColors.brownCard,
                    title: ref.watch(selectedProjectProvider)?.textEn ??
                        ref.watch(selectedProjectProvider)?.textAr ??
                        '',
                    subtitle: "",
                    statusName: OrderStatusType.created.translatedName,
                    descriptionWidget: [
                      DescriptionCardItemWidget(
                        title: 'customer_name'.tr(),
                        trailing: (ref.read(userProvider)
                                    as LoadedViewState<UserEntity>)
                                .data
                                .customer
                                ?.fullName ??
                            '',
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: AppDimensions.kSizeMedium,
                  ),
                  const Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: AppDimensions.kSizeMedium),
                    child: OrderCartsView(
                      primary: false,
                    ),
                  ),
                  const Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.kSizeMedium,
                    ),
                    child: AttachmentsListView(primary: false),
                  ),
                ],
              ),
            ),
          ),
          Card(
            color: AppColors.white,
            elevation: 10,
            margin: EdgeInsets.zero,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (ref.watch(addOrderProvider).getOrder() != null)
                  ButtonWidget(
                    backgroundColor: AppColors.purple,
                    onPressed: () =>
                        ref.read(updateOrderProvider.notifier).updateOrder(),
                    title: "update".tr(),
                  ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: AppDimensions.kSizeXLarge,
                    horizontal: AppDimensions.kSizeMedium,
                  ),
                  child: ButtonWidget(
                    onPressed: () => ref
                        .read(createOrderProvider.notifier)
                        .fetchCreateOrder(),
                    title: "send_order".tr(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
