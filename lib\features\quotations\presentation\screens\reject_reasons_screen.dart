import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/form_field_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/core/widgets/title_widget.dart';
import 'package:mdd/features/dropdowns/presentation/providers/reject_reasons_provider.dart';
import 'package:mdd/features/dropdowns/presentation/widgets/reject_reasons_drop_down_widget.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/presentation/provider/quotation_details_provider.dart';
import 'package:mdd/features/quotations/presentation/provider/reject_quotation_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class RejectReasonsScreen extends ConsumerStatefulWidget {
  final QuotationsModel model;

  const RejectReasonsScreen(this.model, {super.key});

  @override
  ConsumerState createState() => _RejectReasonsScreenState();
}

class _RejectReasonsScreenState extends ConsumerState<RejectReasonsScreen> {
  final _notesController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    ref.listen(rejectQuotationProvider, (previous, state) {
      if (state is LoadingViewState) {
        UiHelper.showLoadingDialog(context);
      }

      if (state is LoadedViewState<ResultModel>) {
        Navigator.pop(context);
        Navigator.pop(context);
        UiHelper.showNotification('reject_quotation_success'.tr(),
            notificationType: NotificationType.success);
        ref
            .read(quotationDetailsProvider.notifier)
            .fetchQuotationDetails(widget.model.quotationID);
      }
      if (state is ErrorViewState) {
        context.popRoute();
        UiHelper.showNotification(
          'try_again'.tr(),
        );
      }
    });
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Card(
            elevation: 0,
            color: AppColors.cardDetailsBackground,
            margin: const EdgeInsets.all(AppDimensions.kSizeLarge),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  AppDimensions.kDefaultRadius,
                )),
            child: Padding(
              padding: const EdgeInsets.symmetric(
                vertical: AppDimensions.kSizeXXLarge,
                horizontal: AppDimensions.kSizeXXLarge,
              ),
              child: Image.asset(
                'assets/images/reject_image.png',
                height: context.widthR(0.3),
              ),
            ),
          ),
          const TitleWidget(
            'رفض العرض',
            color: AppColors.disabledColor1,
            fontSize: AppDimensions.kSizeLarge2,
            fontWeight: FontWeight.bold,
          ),
          const SizedBox(
            height: AppDimensions.kSizeLarge,
          ),
          const RejectReasonsDropDown(),
          FormFieldWidget(
            controller: _notesController,
            fillColor: AppColors.white,
            borderColor: AppColors.cardDetailsBackground,
            hintText: 'الملاحظات',
            textInputType: TextInputType.text,
            maxLines: 5,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.kSizeLarge,
              vertical: AppDimensions.kSizeLarge,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => context.popRoute(),
                  child: const TextWidget('إلغاء'),
                ),
                ButtonWidget(
                  onPressed: ref.watch(selectedRejectReasonProvider) != null
                      ? _callRejectQuotation
                      : null,
                  title: 'رفض',
                  backgroundColor: AppColors.red,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _callRejectQuotation() {
    ref.read(rejectQuotationProvider.notifier).reject(
      widget.model.copyWith(
        rejectReasonID: ref.read(selectedRejectReasonProvider)!.id,
        rejectNotes: _notesController.text,
        rejectDate: DateTime.now().toIso8601String(),
        items: [],
      ),
    );
  }
}
