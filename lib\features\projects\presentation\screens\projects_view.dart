import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/description_widget/card_item_widget.dart';
import 'package:mdd/core/widgets/description_widget/description_card_item_widget.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/projects/data/models/projects_model.dart';
import 'package:mdd/features/projects/presentation/provider/projects_provider.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/utils/constants/constants.dart';
import 'package:mdd/utils/constants/keys.dart';
import 'package:mdd/utils/extensions.dart';

class ProjectsView extends ConsumerWidget {
  const ProjectsView({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Consumer(builder: (context, ref, child) {
      final state = ref.watch(projectsProvider);

      if (state is LoadingViewState) {
        return const LoaderWidget();
      }
      if (state is ErrorViewState) {
        return Text(state.errorMessage);
      }
      if (state is LoadedViewState<List<ProjectsModel>>) {
        final provider = ref.read(projectsProvider.notifier);
        return ListView.builder(
          itemCount: state.data.length + 1,
          key: const PageStorageKey(AppKeys.projectListKey),
          padding: EdgeInsets.only(
            bottom: context.mediaQuery.padding.bottom + 70,
          ),
          itemBuilder: (BuildContext context, int index) {
            if (index == state.data.length) {
              if (provider.hasMoreData &&
                  state.data.length >= AppConstants.paginationLimit) {
                provider.loadMore();
                return const Padding(
                  padding: EdgeInsets.all(10),
                  child: SizedBox(
                    child: LoaderWidget(),
                  ),
                );
              } else {
                return const SizedBox();
              }
            }
            return CardItemsWidget(
              title: state.data[index].name ?? '',
              subtitle: state.data[index].location ?? '',
              image: 'assets/images/projects_image.png',
              imageBackground: AppColors.blueYonderCard,
              menuItems: [
                PopupMenuItem(
                  value: 1,
                  child: TextWidget('edit'.tr()),
                ),
              ],
              onSelected: (value) {
                if (value == 1) {
                  context.pushRoute(
                    EditProjectRoute(project: state.data[index]),
                  );
                }
              },
              descriptionWidget: [
                DescriptionCardItemWidget(
                  title: 'contact'.tr(),
                  trailing: state.data[index].customerName ?? '',
                ),
                DescriptionCardItemWidget(
                  title: 'phone_number'.tr(),
                  trailing: state.data[index].customerMobile ?? '',
                ),
                DescriptionCardItemWidget(
                  title: 'email'.tr(),
                  trailing: state.data[index].customerEmail ?? '',
                ),
              ],
            );
          },
        );
      }
      return const SizedBox();
    });
  }
}
