import 'package:auto_route/auto_route.dart';

import 'app_router.gr.dart';

@AutoRouterConfig(replaceInRouteName: 'Screen,Route')
class AppRouter extends $AppRouter {
  @override
  RouteType get defaultRouteType => const RouteType.material();

  @override
  final List<AutoRoute> routes = [
    AutoRoute(page: SplashRoute.page, path: '/'),
    CustomRoute(
      page: FirstOnBoardingRoute.page,
      transitionsBuilder: TransitionsBuilders.fadeIn,
      durationInMilliseconds: 400,
    ),
    CustomRoute(
      page: SecondOnBoardingRoute.page,
      transitionsBuilder: TransitionsBuilders.slideBottom,
      durationInMilliseconds: 400,
    ),
    CustomRoute(
      page: ThirdOnBoardingRoute.page,
      transitionsBuilder: TransitionsBuilders.fadeIn,
      durationInMilliseconds: 800,
    ),
    AutoRoute(
      page: FourthOnBoardingRoute.page,
    ),
    AutoRoute(
      page: UserLoginRoute.page,
    ),
    AutoRoute(
      page: BottomNavBarRoute.page,
      children: [
        AutoRoute(page: HomeRoute.page),
        AutoRoute(page: OrdersRoute.page),
        AutoRoute(page: QuotationsRoute.page),
        AutoRoute(page: InvoicesRoute.page),
        AutoRoute(page: StatisticsRoute.page),
      ],
    ),
    AutoRoute(
      page: AddOrderAttachmentsRoute.page,
    ),
    AutoRoute(page: OrderDetailsRoute.page),
    AutoRoute(page: CreateOrderRoute.page),
    AutoRoute(page: OrderSummaryRoute.page),
    AutoRoute(page: QuotationDetailsRoute.page),
    AutoRoute(page: QuotationAcceptanceRoute.page),
    AutoRoute(page: SettingsRoute.page),
    AutoRoute(page: PrivacyPolicyRoute.page),
    AutoRoute(page: CustomerSupportRoute.page),
    AutoRoute(page: UserProfileRoute.page),
    AutoRoute(page: NotificationsRoute.page),
    AutoRoute(page: CreatePaymentRoute.page),
    AutoRoute(page: QuotationAdditionalInfoRoute.page),
    AutoRoute(page: QuotationAddressDetailsRoute.page),
    AutoRoute(page: LocationPickerRoute.page, fullscreenDialog: true),
    AutoRoute(page: PaymentsRoute.page),
    AutoRoute(page: ProjectsRoute.page),
    AutoRoute(page: CreateProjectRoute.page),
    AutoRoute(page: EditProjectRoute.page),
    AutoRoute(page: AddCustomerRoute.page),
    AutoRoute(page: EditCustomerRoute.page),
    AutoRoute(page: CustomersRoute.page),
    AutoRoute(page: UpdateOrgRoute.page),
  ];
}
