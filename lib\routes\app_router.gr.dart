// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i36;
import 'package:flutter/cupertino.dart' as _i37;
import 'package:flutter/material.dart' as _i39;
import 'package:mdd/core/splash_screen.dart' as _i30;
import 'package:mdd/features/customer/data/models/customers_model.dart' as _i40;
import 'package:mdd/features/customer/presentation/screens/add_customer_screen.dart'
    as _i1;
import 'package:mdd/features/customer/presentation/screens/customers_screen.dart'
    as _i8;
import 'package:mdd/features/customer/presentation/screens/edit_customer_screen.dart'
    as _i9;
import 'package:mdd/features/home/<USER>/screens/home_screen.dart'
    as _i13;
import 'package:mdd/features/invoices/presentation/screens/invoices_screen.dart'
    as _i14;
import 'package:mdd/features/location_picker/presentation/screens/location_picker_screen.dart'
    as _i15;
import 'package:mdd/features/nav_bar/presentation/screens/bottom_nav_bar_screen.dart'
    as _i3;
import 'package:mdd/features/nav_bar/presentation/screens/drawer_views/customer_support_screen.dart'
    as _i7;
import 'package:mdd/features/nav_bar/presentation/screens/drawer_views/privacy_policy_screen.dart'
    as _i21;
import 'package:mdd/features/nav_bar/presentation/screens/drawer_views/settings_screen.dart'
    as _i29;
import 'package:mdd/features/notifications/presentation/screens/notifications_screen.dart'
    as _i16;
import 'package:mdd/features/on_boarding/presentation/screens/first_on_boarding_screen.dart'
    as _i11;
import 'package:mdd/features/on_boarding/presentation/screens/fourth_on_boarding_screen.dart'
    as _i12;
import 'package:mdd/features/on_boarding/presentation/screens/second_on_boarding_screen.dart'
    as _i28;
import 'package:mdd/features/on_boarding/presentation/screens/third_on_boarding_screen.dart'
    as _i32;
import 'package:mdd/features/orders/data/models/orders_model.dart' as _i38;
import 'package:mdd/features/orders/domain/entities/orders_entity.dart' as _i42;
import 'package:mdd/features/orders/presentation/screens/create_order/add_order_attachments_screen.dart'
    as _i2;
import 'package:mdd/features/orders/presentation/screens/create_order/create_order_screen.dart'
    as _i4;
import 'package:mdd/features/orders/presentation/screens/create_order/order_summary_screen.dart'
    as _i18;
import 'package:mdd/features/orders/presentation/screens/order_details_screen.dart'
    as _i17;
import 'package:mdd/features/orders/presentation/screens/orders_screen.dart'
    as _i19;
import 'package:mdd/features/organization/data/models/organizations_model.dart'
    as _i43;
import 'package:mdd/features/organization/presentation/screen/update_organization_screen.dart'
    as _i33;
import 'package:mdd/features/payments/presentation/screens/create_payment_screen.dart'
    as _i5;
import 'package:mdd/features/payments/presentation/screens/payments_screen.dart'
    as _i20;
import 'package:mdd/features/projects/data/models/projects_model.dart' as _i41;
import 'package:mdd/features/projects/presentation/screens/create_project_screen.dart'
    as _i6;
import 'package:mdd/features/projects/presentation/screens/edit_project_screen.dart'
    as _i10;
import 'package:mdd/features/projects/presentation/screens/projects_screen.dart'
    as _i22;
import 'package:mdd/features/quotations/presentation/screens/quotation_acceptance/quotation_acceptance_screen.dart'
    as _i23;
import 'package:mdd/features/quotations/presentation/screens/quotation_acceptance/quotation_additional_info_screen.dart'
    as _i24;
import 'package:mdd/features/quotations/presentation/screens/quotation_acceptance/quotation_address_details_screen.dart'
    as _i25;
import 'package:mdd/features/quotations/presentation/screens/quotation_details_screen.dart'
    as _i26;
import 'package:mdd/features/quotations/presentation/screens/quotations_screen.dart'
    as _i27;
import 'package:mdd/features/statistics/presentation/screens/statistics_screen.dart'
    as _i31;
import 'package:mdd/features/user/presentation/screens/user_login_screen.dart'
    as _i34;
import 'package:mdd/features/user/presentation/screens/user_profile_screen.dart'
    as _i35;

abstract class $AppRouter extends _i36.RootStackRouter {
  $AppRouter({super.navigatorKey});

  @override
  final Map<String, _i36.PageFactory> pagesMap = {
    AddCustomerRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i1.AddCustomerScreen(),
      );
    },
    AddOrderAttachmentsRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i2.AddOrderAttachmentsScreen(),
      );
    },
    BottomNavBarRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i3.BottomNavBarScreen(),
      );
    },
    CreateOrderRoute.name: (routeData) {
      final args = routeData.argsAs<CreateOrderRouteArgs>(
          orElse: () => const CreateOrderRouteArgs());
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i4.CreateOrderScreen(
          key: args.key,
          order: args.order,
        ),
      );
    },
    CreatePaymentRoute.name: (routeData) {
      final args = routeData.argsAs<CreatePaymentRouteArgs>(
          orElse: () => const CreatePaymentRouteArgs());
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i5.CreatePaymentScreen(
          key: args.key,
          orderId: args.orderId,
          invoiceId: args.invoiceId,
          amount: args.amount,
        ),
      );
    },
    CreateProjectRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i6.CreateProjectScreen(),
      );
    },
    CustomerSupportRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i7.CustomerSupportScreen(),
      );
    },
    CustomersRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i8.CustomersScreen(),
      );
    },
    EditCustomerRoute.name: (routeData) {
      final args = routeData.argsAs<EditCustomerRouteArgs>();
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i9.EditCustomerScreen(
          args.customer,
          key: args.key,
        ),
      );
    },
    EditProjectRoute.name: (routeData) {
      final args = routeData.argsAs<EditProjectRouteArgs>();
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i10.EditProjectScreen(
          args.project,
          key: args.key,
        ),
      );
    },
    FirstOnBoardingRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i11.FirstOnBoardingScreen(),
      );
    },
    FourthOnBoardingRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i12.FourthOnBoardingScreen(),
      );
    },
    HomeRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i13.HomeScreen(),
      );
    },
    InvoicesRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i14.InvoicesScreen(),
      );
    },
    LocationPickerRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i15.LocationPickerScreen(),
      );
    },
    NotificationsRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i16.NotificationsScreen(),
      );
    },
    OrderDetailsRoute.name: (routeData) {
      final args = routeData.argsAs<OrderDetailsRouteArgs>();
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i17.OrderDetailsScreen(
          key: args.key,
          order: args.order,
        ),
      );
    },
    OrderSummaryRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i18.OrderSummaryScreen(),
      );
    },
    OrdersRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i19.OrdersScreen(),
      );
    },
    PaymentsRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i20.PaymentsScreen(),
      );
    },
    PrivacyPolicyRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i21.PrivacyPolicyScreen(),
      );
    },
    ProjectsRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i22.ProjectsScreen(),
      );
    },
    QuotationAcceptanceRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i23.QuotationAcceptanceScreen(),
      );
    },
    QuotationAdditionalInfoRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i24.QuotationAdditionalInfoScreen(),
      );
    },
    QuotationAddressDetailsRoute.name: (routeData) {
      final args = routeData.argsAs<QuotationAddressDetailsRouteArgs>();
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i25.QuotationAddressDetailsScreen(
          key: args.key,
          purchaseNumber: args.purchaseNumber,
          notes: args.notes,
        ),
      );
    },
    QuotationDetailsRoute.name: (routeData) {
      final args = routeData.argsAs<QuotationDetailsRouteArgs>();
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i26.QuotationDetailsScreen(
          key: args.key,
          quotationId: args.quotationId,
        ),
      );
    },
    QuotationsRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i27.QuotationsScreen(),
      );
    },
    SecondOnBoardingRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i28.SecondOnBoardingScreen(),
      );
    },
    SettingsRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i29.SettingsScreen(),
      );
    },
    SplashRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i30.SplashScreen(),
      );
    },
    StatisticsRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i31.StatisticsScreen(),
      );
    },
    ThirdOnBoardingRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i32.ThirdOnBoardingScreen(),
      );
    },
    UpdateOrgRoute.name: (routeData) {
      final args = routeData.argsAs<UpdateOrgRouteArgs>();
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i33.UpdateOrgScreen(
          key: args.key,
          org: args.org,
        ),
      );
    },
    UserLoginRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i34.UserLoginScreen(),
      );
    },
    UserProfileRoute.name: (routeData) {
      return _i36.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i35.UserProfileScreen(),
      );
    },
  };
}

/// generated route for
/// [_i1.AddCustomerScreen]
class AddCustomerRoute extends _i36.PageRouteInfo<void> {
  const AddCustomerRoute({List<_i36.PageRouteInfo>? children})
      : super(
          AddCustomerRoute.name,
          initialChildren: children,
        );

  static const String name = 'AddCustomerRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i2.AddOrderAttachmentsScreen]
class AddOrderAttachmentsRoute extends _i36.PageRouteInfo<void> {
  const AddOrderAttachmentsRoute({List<_i36.PageRouteInfo>? children})
      : super(
          AddOrderAttachmentsRoute.name,
          initialChildren: children,
        );

  static const String name = 'AddOrderAttachmentsRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i3.BottomNavBarScreen]
class BottomNavBarRoute extends _i36.PageRouteInfo<void> {
  const BottomNavBarRoute({List<_i36.PageRouteInfo>? children})
      : super(
          BottomNavBarRoute.name,
          initialChildren: children,
        );

  static const String name = 'BottomNavBarRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i4.CreateOrderScreen]
class CreateOrderRoute extends _i36.PageRouteInfo<CreateOrderRouteArgs> {
  CreateOrderRoute({
    _i37.Key? key,
    _i38.OrdersModel? order,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          CreateOrderRoute.name,
          args: CreateOrderRouteArgs(
            key: key,
            order: order,
          ),
          initialChildren: children,
        );

  static const String name = 'CreateOrderRoute';

  static const _i36.PageInfo<CreateOrderRouteArgs> page =
      _i36.PageInfo<CreateOrderRouteArgs>(name);
}

class CreateOrderRouteArgs {
  const CreateOrderRouteArgs({
    this.key,
    this.order,
  });

  final _i37.Key? key;

  final _i38.OrdersModel? order;

  @override
  String toString() {
    return 'CreateOrderRouteArgs{key: $key, order: $order}';
  }
}

/// generated route for
/// [_i5.CreatePaymentScreen]
class CreatePaymentRoute extends _i36.PageRouteInfo<CreatePaymentRouteArgs> {
  CreatePaymentRoute({
    _i39.Key? key,
    String? orderId,
    String? invoiceId,
    double? amount,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          CreatePaymentRoute.name,
          args: CreatePaymentRouteArgs(
            key: key,
            orderId: orderId,
            invoiceId: invoiceId,
            amount: amount,
          ),
          initialChildren: children,
        );

  static const String name = 'CreatePaymentRoute';

  static const _i36.PageInfo<CreatePaymentRouteArgs> page =
      _i36.PageInfo<CreatePaymentRouteArgs>(name);
}

class CreatePaymentRouteArgs {
  const CreatePaymentRouteArgs({
    this.key,
    this.orderId,
    this.invoiceId,
    this.amount,
  });

  final _i39.Key? key;

  final String? orderId;

  final String? invoiceId;

  final double? amount;

  @override
  String toString() {
    return 'CreatePaymentRouteArgs{key: $key, orderId: $orderId, invoiceId: $invoiceId, amount: $amount}';
  }
}

/// generated route for
/// [_i6.CreateProjectScreen]
class CreateProjectRoute extends _i36.PageRouteInfo<void> {
  const CreateProjectRoute({List<_i36.PageRouteInfo>? children})
      : super(
          CreateProjectRoute.name,
          initialChildren: children,
        );

  static const String name = 'CreateProjectRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i7.CustomerSupportScreen]
class CustomerSupportRoute extends _i36.PageRouteInfo<void> {
  const CustomerSupportRoute({List<_i36.PageRouteInfo>? children})
      : super(
          CustomerSupportRoute.name,
          initialChildren: children,
        );

  static const String name = 'CustomerSupportRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i8.CustomersScreen]
class CustomersRoute extends _i36.PageRouteInfo<void> {
  const CustomersRoute({List<_i36.PageRouteInfo>? children})
      : super(
          CustomersRoute.name,
          initialChildren: children,
        );

  static const String name = 'CustomersRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i9.EditCustomerScreen]
class EditCustomerRoute extends _i36.PageRouteInfo<EditCustomerRouteArgs> {
  EditCustomerRoute({
    required _i40.CustomersModel customer,
    _i39.Key? key,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          EditCustomerRoute.name,
          args: EditCustomerRouteArgs(
            customer: customer,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'EditCustomerRoute';

  static const _i36.PageInfo<EditCustomerRouteArgs> page =
      _i36.PageInfo<EditCustomerRouteArgs>(name);
}

class EditCustomerRouteArgs {
  const EditCustomerRouteArgs({
    required this.customer,
    this.key,
  });

  final _i40.CustomersModel customer;

  final _i39.Key? key;

  @override
  String toString() {
    return 'EditCustomerRouteArgs{customer: $customer, key: $key}';
  }
}

/// generated route for
/// [_i10.EditProjectScreen]
class EditProjectRoute extends _i36.PageRouteInfo<EditProjectRouteArgs> {
  EditProjectRoute({
    required _i41.ProjectsModel project,
    _i37.Key? key,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          EditProjectRoute.name,
          args: EditProjectRouteArgs(
            project: project,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'EditProjectRoute';

  static const _i36.PageInfo<EditProjectRouteArgs> page =
      _i36.PageInfo<EditProjectRouteArgs>(name);
}

class EditProjectRouteArgs {
  const EditProjectRouteArgs({
    required this.project,
    this.key,
  });

  final _i41.ProjectsModel project;

  final _i37.Key? key;

  @override
  String toString() {
    return 'EditProjectRouteArgs{project: $project, key: $key}';
  }
}

/// generated route for
/// [_i11.FirstOnBoardingScreen]
class FirstOnBoardingRoute extends _i36.PageRouteInfo<void> {
  const FirstOnBoardingRoute({List<_i36.PageRouteInfo>? children})
      : super(
          FirstOnBoardingRoute.name,
          initialChildren: children,
        );

  static const String name = 'FirstOnBoardingRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i12.FourthOnBoardingScreen]
class FourthOnBoardingRoute extends _i36.PageRouteInfo<void> {
  const FourthOnBoardingRoute({List<_i36.PageRouteInfo>? children})
      : super(
          FourthOnBoardingRoute.name,
          initialChildren: children,
        );

  static const String name = 'FourthOnBoardingRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i13.HomeScreen]
class HomeRoute extends _i36.PageRouteInfo<void> {
  const HomeRoute({List<_i36.PageRouteInfo>? children})
      : super(
          HomeRoute.name,
          initialChildren: children,
        );

  static const String name = 'HomeRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i14.InvoicesScreen]
class InvoicesRoute extends _i36.PageRouteInfo<void> {
  const InvoicesRoute({List<_i36.PageRouteInfo>? children})
      : super(
          InvoicesRoute.name,
          initialChildren: children,
        );

  static const String name = 'InvoicesRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i15.LocationPickerScreen]
class LocationPickerRoute extends _i36.PageRouteInfo<void> {
  const LocationPickerRoute({List<_i36.PageRouteInfo>? children})
      : super(
          LocationPickerRoute.name,
          initialChildren: children,
        );

  static const String name = 'LocationPickerRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i16.NotificationsScreen]
class NotificationsRoute extends _i36.PageRouteInfo<void> {
  const NotificationsRoute({List<_i36.PageRouteInfo>? children})
      : super(
          NotificationsRoute.name,
          initialChildren: children,
        );

  static const String name = 'NotificationsRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i17.OrderDetailsScreen]
class OrderDetailsRoute extends _i36.PageRouteInfo<OrderDetailsRouteArgs> {
  OrderDetailsRoute({
    _i39.Key? key,
    required _i42.OrdersEntity order,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          OrderDetailsRoute.name,
          args: OrderDetailsRouteArgs(
            key: key,
            order: order,
          ),
          initialChildren: children,
        );

  static const String name = 'OrderDetailsRoute';

  static const _i36.PageInfo<OrderDetailsRouteArgs> page =
      _i36.PageInfo<OrderDetailsRouteArgs>(name);
}

class OrderDetailsRouteArgs {
  const OrderDetailsRouteArgs({
    this.key,
    required this.order,
  });

  final _i39.Key? key;

  final _i42.OrdersEntity order;

  @override
  String toString() {
    return 'OrderDetailsRouteArgs{key: $key, order: $order}';
  }
}

/// generated route for
/// [_i18.OrderSummaryScreen]
class OrderSummaryRoute extends _i36.PageRouteInfo<void> {
  const OrderSummaryRoute({List<_i36.PageRouteInfo>? children})
      : super(
          OrderSummaryRoute.name,
          initialChildren: children,
        );

  static const String name = 'OrderSummaryRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i19.OrdersScreen]
class OrdersRoute extends _i36.PageRouteInfo<void> {
  const OrdersRoute({List<_i36.PageRouteInfo>? children})
      : super(
          OrdersRoute.name,
          initialChildren: children,
        );

  static const String name = 'OrdersRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i20.PaymentsScreen]
class PaymentsRoute extends _i36.PageRouteInfo<void> {
  const PaymentsRoute({List<_i36.PageRouteInfo>? children})
      : super(
          PaymentsRoute.name,
          initialChildren: children,
        );

  static const String name = 'PaymentsRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i21.PrivacyPolicyScreen]
class PrivacyPolicyRoute extends _i36.PageRouteInfo<void> {
  const PrivacyPolicyRoute({List<_i36.PageRouteInfo>? children})
      : super(
          PrivacyPolicyRoute.name,
          initialChildren: children,
        );

  static const String name = 'PrivacyPolicyRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i22.ProjectsScreen]
class ProjectsRoute extends _i36.PageRouteInfo<void> {
  const ProjectsRoute({List<_i36.PageRouteInfo>? children})
      : super(
          ProjectsRoute.name,
          initialChildren: children,
        );

  static const String name = 'ProjectsRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i23.QuotationAcceptanceScreen]
class QuotationAcceptanceRoute extends _i36.PageRouteInfo<void> {
  const QuotationAcceptanceRoute({List<_i36.PageRouteInfo>? children})
      : super(
          QuotationAcceptanceRoute.name,
          initialChildren: children,
        );

  static const String name = 'QuotationAcceptanceRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i24.QuotationAdditionalInfoScreen]
class QuotationAdditionalInfoRoute extends _i36.PageRouteInfo<void> {
  const QuotationAdditionalInfoRoute({List<_i36.PageRouteInfo>? children})
      : super(
          QuotationAdditionalInfoRoute.name,
          initialChildren: children,
        );

  static const String name = 'QuotationAdditionalInfoRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i25.QuotationAddressDetailsScreen]
class QuotationAddressDetailsRoute
    extends _i36.PageRouteInfo<QuotationAddressDetailsRouteArgs> {
  QuotationAddressDetailsRoute({
    _i39.Key? key,
    required String purchaseNumber,
    String? notes,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          QuotationAddressDetailsRoute.name,
          args: QuotationAddressDetailsRouteArgs(
            key: key,
            purchaseNumber: purchaseNumber,
            notes: notes,
          ),
          initialChildren: children,
        );

  static const String name = 'QuotationAddressDetailsRoute';

  static const _i36.PageInfo<QuotationAddressDetailsRouteArgs> page =
      _i36.PageInfo<QuotationAddressDetailsRouteArgs>(name);
}

class QuotationAddressDetailsRouteArgs {
  const QuotationAddressDetailsRouteArgs({
    this.key,
    required this.purchaseNumber,
    this.notes,
  });

  final _i39.Key? key;

  final String purchaseNumber;

  final String? notes;

  @override
  String toString() {
    return 'QuotationAddressDetailsRouteArgs{key: $key, purchaseNumber: $purchaseNumber, notes: $notes}';
  }
}

/// generated route for
/// [_i26.QuotationDetailsScreen]
class QuotationDetailsRoute
    extends _i36.PageRouteInfo<QuotationDetailsRouteArgs> {
  QuotationDetailsRoute({
    _i39.Key? key,
    required String quotationId,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          QuotationDetailsRoute.name,
          args: QuotationDetailsRouteArgs(
            key: key,
            quotationId: quotationId,
          ),
          initialChildren: children,
        );

  static const String name = 'QuotationDetailsRoute';

  static const _i36.PageInfo<QuotationDetailsRouteArgs> page =
      _i36.PageInfo<QuotationDetailsRouteArgs>(name);
}

class QuotationDetailsRouteArgs {
  const QuotationDetailsRouteArgs({
    this.key,
    required this.quotationId,
  });

  final _i39.Key? key;

  final String quotationId;

  @override
  String toString() {
    return 'QuotationDetailsRouteArgs{key: $key, quotationId: $quotationId}';
  }
}

/// generated route for
/// [_i27.QuotationsScreen]
class QuotationsRoute extends _i36.PageRouteInfo<void> {
  const QuotationsRoute({List<_i36.PageRouteInfo>? children})
      : super(
          QuotationsRoute.name,
          initialChildren: children,
        );

  static const String name = 'QuotationsRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i28.SecondOnBoardingScreen]
class SecondOnBoardingRoute extends _i36.PageRouteInfo<void> {
  const SecondOnBoardingRoute({List<_i36.PageRouteInfo>? children})
      : super(
          SecondOnBoardingRoute.name,
          initialChildren: children,
        );

  static const String name = 'SecondOnBoardingRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i29.SettingsScreen]
class SettingsRoute extends _i36.PageRouteInfo<void> {
  const SettingsRoute({List<_i36.PageRouteInfo>? children})
      : super(
          SettingsRoute.name,
          initialChildren: children,
        );

  static const String name = 'SettingsRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i30.SplashScreen]
class SplashRoute extends _i36.PageRouteInfo<void> {
  const SplashRoute({List<_i36.PageRouteInfo>? children})
      : super(
          SplashRoute.name,
          initialChildren: children,
        );

  static const String name = 'SplashRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i31.StatisticsScreen]
class StatisticsRoute extends _i36.PageRouteInfo<void> {
  const StatisticsRoute({List<_i36.PageRouteInfo>? children})
      : super(
          StatisticsRoute.name,
          initialChildren: children,
        );

  static const String name = 'StatisticsRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i32.ThirdOnBoardingScreen]
class ThirdOnBoardingRoute extends _i36.PageRouteInfo<void> {
  const ThirdOnBoardingRoute({List<_i36.PageRouteInfo>? children})
      : super(
          ThirdOnBoardingRoute.name,
          initialChildren: children,
        );

  static const String name = 'ThirdOnBoardingRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i33.UpdateOrgScreen]
class UpdateOrgRoute extends _i36.PageRouteInfo<UpdateOrgRouteArgs> {
  UpdateOrgRoute({
    _i39.Key? key,
    required _i43.OrgsModel org,
    List<_i36.PageRouteInfo>? children,
  }) : super(
          UpdateOrgRoute.name,
          args: UpdateOrgRouteArgs(
            key: key,
            org: org,
          ),
          initialChildren: children,
        );

  static const String name = 'UpdateOrgRoute';

  static const _i36.PageInfo<UpdateOrgRouteArgs> page =
      _i36.PageInfo<UpdateOrgRouteArgs>(name);
}

class UpdateOrgRouteArgs {
  const UpdateOrgRouteArgs({
    this.key,
    required this.org,
  });

  final _i39.Key? key;

  final _i43.OrgsModel org;

  @override
  String toString() {
    return 'UpdateOrgRouteArgs{key: $key, org: $org}';
  }
}

/// generated route for
/// [_i34.UserLoginScreen]
class UserLoginRoute extends _i36.PageRouteInfo<void> {
  const UserLoginRoute({List<_i36.PageRouteInfo>? children})
      : super(
          UserLoginRoute.name,
          initialChildren: children,
        );

  static const String name = 'UserLoginRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}

/// generated route for
/// [_i35.UserProfileScreen]
class UserProfileRoute extends _i36.PageRouteInfo<void> {
  const UserProfileRoute({List<_i36.PageRouteInfo>? children})
      : super(
          UserProfileRoute.name,
          initialChildren: children,
        );

  static const String name = 'UserProfileRoute';

  static const _i36.PageInfo<void> page = _i36.PageInfo<void>(name);
}
