import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/location_picker/presentation/screens/location_picker_view.dart';
import 'package:mdd/theme/colors.dart';

@RoutePage()
class LocationPickerScreen extends StatelessWidget {
  const LocationPickerScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TextWidget('location'.tr()),
        leading: InkWell(
          onTap: () => context.maybePop(),
          child: Center(
            child: CircleAvatar(
                radius: 20,
                backgroundColor: AppColors.cardColor,
                child: SvgPicture.asset('assets/icons/close_icon.svg')),
          ),
        ),
      ),
      body: const LocationPickerView(),
    );
  }
}
