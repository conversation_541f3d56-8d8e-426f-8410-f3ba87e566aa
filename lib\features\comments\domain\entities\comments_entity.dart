import 'package:mdd/utils/enums.dart';

class CommentsEntity {
  final String orderCommentID;
  final String orderID;
  final int? orderNo;
  final String? conetent;
  final String createdOn;
  final String? fullName;
  final String? createdBy;
  final UserType userType;
  final bool isInternal;

  CommentsEntity(
      {required this.orderCommentID,
      required this.orderID,
      required this.orderNo,
      required this.conetent,
      required this.createdOn,
      required this.fullName,
      required this.createdBy,
      required this.userType,
      required this.isInternal});
}
