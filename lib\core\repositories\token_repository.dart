import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:mdd/features/user/data/models/user_model.dart';
import 'package:mdd/services/secure_storage.dart';
import 'package:mdd/utils/constants/storage_keys_constants.dart';
import 'package:mdd/utils/sentry_reporter.dart';

final tokenRepositoryProvider = Provider(
  (ref) => TokenRepository(
    ref.watch(secureStorageProvider),
  ),
);

class TokenRepository {
  //* Dependency
  final FlutterSecureStorage secureStorage;

  //* State
  String? _authToken;
  UserModel? _userModel;

  //* Getters
  String? get authToken => _authToken;
  UserModel? get userModel => _userModel;

  //* Constructor
  TokenRepository(this.secureStorage);

  //* Token Methods

  /// Deletes token from local storage.
  Future<void> deleteUserAccess() async {
    await secureStorage.delete(key: kAuthToken);
    await secureStorage.delete(key: kUserName);
    await secureStorage.delete(key: kPassword);
    _authToken = null;
  }

  // Saves or Updates token in local storage with the given [newToken].
  Future<void> saveUserAccess(UserModel user, String password) async {
    _authToken = user.token;
    _userModel = user;
    await secureStorage.write(key: kAuthToken, value: user.token);
    await secureStorage.write(key: kUserName, value: user.email);
    await secureStorage.write(key: kPassword, value: password);
  }

  // Saves or Updates token in local storage with the given [newToken].
  Future<void> updatePassword(String password) async {
    await secureStorage.write(key: kPassword, value: password);
  }

  /// Read token from local storage.
  Future<Map<String, String>?> readUserAccess() async {
    try {
      final token = await secureStorage.read(key: kAuthToken);
      final userName = await secureStorage.read(key: kUserName);
      final password = await secureStorage.read(key: kPassword);
      if (token == null || userName == null || password == null) {
        return null;
      }
      return {
        kAuthToken: token,
        kUserName: userName,
        kPassword: password,
      };
    } on PlatformException {
      await secureStorage.delete(key: kAuthToken);
      await secureStorage.delete(key: kUserName);
      await secureStorage.delete(key: kPassword);
      return null;
    } catch (e, stackTrace) {
      await SentryReporter.genericThrow(e, stackTrace: stackTrace);
      debugPrint(e.toString());
      return null;
    }
  }

//check if token expired datetime is less than current datetime
  bool get isTokenExpired {
    final tokenExpireDate = userModel?.tokenExpireDate;
    if (tokenExpireDate == null) return true;
    final expireDate = DateTime.parse(tokenExpireDate);
    final currentDate = DateTime.now().toUtc();
    return currentDate.isAfter(expireDate);
  }
}
