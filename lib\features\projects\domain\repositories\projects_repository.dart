import 'package:dartz/dartz.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/features/projects/data/models/projects_model.dart';
import 'package:mdd/features/projects/domain/usecases/create_new_project.dart';
import 'package:mdd/features/projects/domain/usecases/projects.dart';
import 'package:mdd/features/projects/domain/usecases/update_project.dart';

abstract class ProjectsRepository {
  Future<Either<Failure, List<ProjectsModel>>> getProjects(
      ProjectsParams params);

  Future<Either<Failure, ResultModel>> createNewProject(
      CreateNewProjectParams params);
  Future<Either<Failure, ResultModel>> updateProject(
      UpdateProjectParams params);
}
