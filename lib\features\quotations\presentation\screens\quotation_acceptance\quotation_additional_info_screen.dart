import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/form_field_widget.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/core/widgets/title_item_widget.dart';
import 'package:mdd/features/attachments/presentation/screens/attachments_view.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/constants/regex_constants.dart';

@RoutePage()
class QuotationAdditionalInfoScreen extends StatefulWidget {
  const QuotationAdditionalInfoScreen({super.key});

  @override
  State<QuotationAdditionalInfoScreen> createState() =>
      _QuotationAdditionalInfoScreenState();
}

class _QuotationAdditionalInfoScreenState
    extends State<QuotationAdditionalInfoScreen> {
  final _paymentNumberController = TextEditingController();
  final _notesController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _paymentNumberController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MainAppBar(),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            const SizedBox(
              height: AppDimensions.kSizeLarge,
            ),
            DetailsCustomBar(
              title: 'quotation'.tr(),
              activeIndex: 1,
            ),
            Expanded(
                child: SingleChildScrollView(
              child: Column(
                children: [
                  TitleItemWidget(title: 'purchase_request'.tr()),
                  FormFieldWidget(
                    controller: _paymentNumberController,
                    fillColor: AppColors.cardDetailsBackground,
                    borderColor: AppColors.cardDetailsBackground,
                    hintText: 'purchase_number'.tr(),
                    inputFormatters: [
                      LengthLimitingTextInputFormatter(15),
                      FilteringTextInputFormatter.allow(
                          RegexConstants.kNumberRegex)
                    ],
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        return null;
                      }
                      return 'purchase_number_validate'.tr();
                    },
                    textInputType: TextInputType.number,
                  ),
                  const SizedBox(
                    height: AppDimensions.kSizeSmall,
                  ),
                  TitleItemWidget(title: 'notes'.tr()),
                  FormFieldWidget(
                    controller: _notesController,
                    fillColor: AppColors.cardDetailsBackground,
                    borderColor: AppColors.cardDetailsBackground,
                    hintText: 'add_note'.tr(),
                    textInputType: TextInputType.text,
                    maxLines: 5,
                  ),
                  const SizedBox(height: AppDimensions.kSizeSmall),
                  const AttachmentsView(primary: false),
                ],
              ),
            )),
            Card(
              color: AppColors.white,
              elevation: 10,
              margin: EdgeInsets.zero,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.kSizeMedium,
                ),
                margin: const EdgeInsets.symmetric(
                  vertical: AppDimensions.kSizeXXLarge,
                ),
                alignment: AlignmentDirectional.bottomEnd,
                child: ButtonWidget(
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      context.pushRoute(
                        QuotationAddressDetailsRoute(
                          purchaseNumber: _paymentNumberController.text,
                          notes: _notesController.text,
                        ),
                      );
                    }
                  },
                  title: 'next'.tr(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
