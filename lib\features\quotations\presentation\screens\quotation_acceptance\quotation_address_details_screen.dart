import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/form_field_widget.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/features/attachments/presentation/provider/attachments_provider.dart';
import 'package:mdd/features/location_picker/presentation/location_widget.dart';
import 'package:mdd/features/location_picker/presentation/provider/selected_location_provider.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/presentation/provider/quotation_accept_provider.dart';
import 'package:mdd/features/quotations/presentation/provider/quotation_details_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/constants/regex_constants.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

@RoutePage()
class QuotationAddressDetailsScreen extends ConsumerStatefulWidget {
  final String purchaseNumber;
  final String? notes;
  const QuotationAddressDetailsScreen({
    super.key,
    required this.purchaseNumber,
    this.notes,
  });

  @override
  ConsumerState createState() => _QuotationAddressDetailsScreenState();
}

class _QuotationAddressDetailsScreenState
    extends ConsumerState<QuotationAddressDetailsScreen> {
  final _receiverController = TextEditingController();
  final _phoneNumberController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _receiverController.dispose();
    _phoneNumberController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(acceptQuotationProvider, (previous, state) {
      if (state is LoadingViewState) {
        UiHelper.showLoadingDialog(context);
      }
      if (state is LoadedViewState<ResultModel>) {
        Navigator.pop(context);
        context.router.popUntilRoot();

        UiHelper.showNotification('accept_quotation_success'.tr(),
            notificationType: NotificationType.success);
      }
      if (state is ErrorViewState) {
        Navigator.pop(context);
        UiHelper.showNotification(
          state.errorMessage,
        );
      }
    });
    return Scaffold(
      appBar: const MainAppBar(),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            const SizedBox(
              height: AppDimensions.kSizeLarge,
            ),
            DetailsCustomBar(
              title: 'quotation'.tr(),
              activeIndex: 2,
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    FormFieldWidget(
                      controller: _receiverController,
                      fillColor: AppColors.cardDetailsBackground,
                      borderColor: AppColors.cardDetailsBackground,
                      hintText: 'receiver'.tr(),
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          return null;
                        }
                        return 'receiver_validate'.tr();
                      },
                    ),
                    FormFieldWidget(
                      controller: _phoneNumberController,
                      fillColor: AppColors.cardDetailsBackground,
                      borderColor: AppColors.cardDetailsBackground,
                      hintText: 'phone_number'.tr(),
                      textInputType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.length == 10) {
                          return null;
                        }
                        return 'enter_valid_phone'.tr();
                      },
                      inputFormatters: [
                        LengthLimitingTextInputFormatter(10),
                        FilteringTextInputFormatter.allow(
                            RegexConstants.kNumberRegex)
                      ],
                    ),
                    const LocationWidget(),
                  ],
                ),
              ),
            ),
            Card(
              color: AppColors.white,
              elevation: 10,
              margin: EdgeInsets.zero,
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: AppDimensions.kSizeXXLarge,
                ),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.kSizeMedium,
                  ),
                  alignment: Alignment.center,
                  child: ButtonWidget(
                    onPressed: _callAcceptQuotation,
                    title: 'approve'.tr(),
                    horizontalPadding: AppDimensions.kSizeXXLarge,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _callAcceptQuotation() {
    if (_formKey.currentState!.validate()) {
      final data = (ref.read(quotationDetailsProvider)
              as LoadedViewState<QuotationsModel>)
          .data;
      final attachments = ref.read(attachmentsProvider).attachments;
      if (attachments.isEmpty) {
        UiHelper.showNotification('attachment_validate'.tr());
      } else {
        ref.read(acceptQuotationProvider.notifier).acceptQuotation(
              data.copyWith(
                customerPurchaseOrderNo: widget.purchaseNumber,
                deliverTo: _receiverController.text,
                mobileNo: _phoneNumberController.text,
                location: ref.read(selectedLocationProvider)?.address ?? '',
                rejectNotes: widget.notes,
                fileName: attachments.first.fileName,
                fileContent: attachments.first.fileContent,
                fileContentType: attachments.first.fileContentType,
                googleMapLocation:
                    '${ref.read(selectedLocationProvider)?.latitude},${ref.read(selectedLocationProvider)?.longitude}',
              ),
            );
      }
    }
  }
}
