import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/notifications/domain/entities/notifications_entity.dart';
import 'package:mdd/features/notifications/domain/usecases/notifications.dart';

final notificationsProvider =
    StateNotifierProvider<NotificationsProvider, ViewState>(
  (ref) => NotificationsProvider(
    ref.watch(notificationsUseCaseProvider),
  ),
);

class NotificationsProvider extends BaseProvider<List<NotificationsEntity>> {
  final Notifications _notifications;

  NotificationsProvider(
    this._notifications,
  );
  Future<void> getNotifications() async {
    setLoadingState();
    final response = await _notifications.call(NoParams());
    response.fold(
      (failure) {
        setErrorState(failure.message);
      },
      setLoadedState,
    );
  }
}
