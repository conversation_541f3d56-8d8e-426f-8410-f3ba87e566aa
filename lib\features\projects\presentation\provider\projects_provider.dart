import 'package:dartz/dartz.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/paginated_provider.dart';
import 'package:mdd/features/projects/data/models/projects_model.dart';
import 'package:mdd/features/projects/domain/usecases/projects.dart';
import 'package:mdd/features/projects/presentation/provider/project_text_field_provider.dart';

final projectsProvider =
    StateNotifierProvider.autoDispose<ProjectsProvider, ViewState>(
        (ref) => ProjectsProvider(
              ref.watch(projectsUseCaseProvider),
              ref.watch(projectSearchControllerProvider),
            ));

class ProjectsProvider extends PaginatedProvider<ProjectsModel> {
  final Projects _projects;
  final TextEditingController? _projectSearchController;

  ProjectsProvider(
    this._projects,
    this._projectSearchController,
  ) : super(InitialViewState());

  Future<void> fetchProjects([String? organizationId]) async {
    state = LoadingViewState();
    final response = await fetchList(organizationId);
    response.fold((failure) {
      state = ErrorViewState(errorMessage: failure.message);
    }, (projects) {
      state = LoadedViewState(projects);
    });
  }

  @override
  Future<Either<Failure, List<ProjectsModel>>> fetchList(
      [String? organizationId]) {
    return _projects.call(ProjectsParams(
      organizationId: organizationId,
      page: pageNumber++,
      find: _projectSearchController?.text,
    ));
  }
}
