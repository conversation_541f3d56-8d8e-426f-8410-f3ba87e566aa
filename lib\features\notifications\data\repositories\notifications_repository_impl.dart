import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/exceptions/exceptions.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/notifications/data/datasources/notifications_remote_data_source.dart';
import 'package:mdd/features/notifications/domain/entities/notifications_entity.dart';
import 'package:mdd/features/notifications/domain/repositories/notifications_repository.dart';

final notificationsRepositoryImpl = Provider<NotificationsRepositoryImpl>(
  (ref) => NotificationsRepositoryImpl(
    ref.watch(notificationsRemoteDataSourceImpl),
  ),
);

class NotificationsRepositoryImpl implements NotificationsRepository {
  final NotificationsRemoteDataSource _notificationsRemoteDataSource;
  NotificationsRepositoryImpl(this._notificationsRemoteDataSource);
  @override
  Future<Either<Failure, List<NotificationsEntity>>> getNotifications(
      NoParams params) async {
    try {
      final res = await _notificationsRemoteDataSource.getNotifications(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }
}
