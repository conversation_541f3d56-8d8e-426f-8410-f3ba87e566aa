import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/orders/data/models/orders_model.dart';
import 'package:mdd/features/orders/data/repositories/orders_repository_impl.dart';
import 'package:mdd/features/orders/domain/repositories/orders_repository.dart';

final updateOrderUseCaseProvider =
    Provider<UpdateOrder>((ref) => UpdateOrder(ref.watch(orderRepositoryImpl)));

class UpdateOrder implements UseCase<ResultModel, UpdateOrderParams> {
  final OrdersRepository ordersRepository;

  UpdateOrder(this.ordersRepository);

  @override
  Future<Either<Failure, ResultModel>> call(UpdateOrderParams params) async {
    return ordersRepository.updateOrder(params);
  }
}

class UpdateOrderParams {
  final OrdersModel? order;

  UpdateOrderParams(this.order);
}
