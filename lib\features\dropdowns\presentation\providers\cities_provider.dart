import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/domain/usecases/cities.dart';

final selectedCityProvider =
    StateProvider.autoDispose<DropDownEntity?>((ref) => null);

final citiesListProvider = StateNotifierProvider<CitiesListProvider, ViewState>(
  (ref) => CitiesListProvider(ref.watch(citiesUseCaseProvider)),
);

class CitiesListProvider extends BaseProvider<List<DropDownEntity>> {
  final Cities _cities;

  CitiesListProvider(this._cities);

  Future<void> fetchCitiesList() async {
    setLoadingState();
    final response = await _cities.call(NoParams());
    response.fold(
      (failure) => setErrorState(failure.message),
      setLoadedState,
    );
  }
}
