import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/domain/usecases/reject_reasons.dart';

final selectedRejectReasonProvider =
    StateProvider.autoDispose<DropDownEntity?>((ref) => null);

final rejectReasonsProvider =
    StateNotifierProvider<RejectReasonsProvider, ViewState>(
  (ref) => RejectReasonsProvider(
    ref.watch(rejectReasonsUseCaseProvider),
  ),
);

class RejectReasonsProvider extends BaseProvider<List<DropDownEntity>> {
  final RejectReasons _rejectReasons;

  RejectReasonsProvider(this._rejectReasons);

  Future<void> fetchRejectReasons() async {
    setLoadingState();
    final response = await _rejectReasons.call(NoParams());
    response.fold(
      (failure) => setErrorState(failure.message),
      setLoadedState,
    );
  }
}
