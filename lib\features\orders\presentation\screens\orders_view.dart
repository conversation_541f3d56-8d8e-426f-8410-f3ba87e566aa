import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/description_widget/card_item_widget.dart';
import 'package:mdd/core/widgets/description_widget/description_card_item_widget.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/features/orders/domain/entities/orders_entity.dart';
import 'package:mdd/features/orders/presentation/provider/order_details_provider.dart';
import 'package:mdd/features/orders/presentation/provider/orders_provider.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/utils/constants/constants.dart';
import 'package:mdd/utils/constants/keys.dart';
import 'package:mdd/utils/enum_extensions.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/helper_functions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class OrdersView extends ConsumerStatefulWidget {
  const OrdersView({
    super.key,
  });

  @override
  ConsumerState createState() => _OrdersViewState();
}

class _OrdersViewState extends ConsumerState<OrdersView> {
  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      _fetch();
    });
    super.initState();
  }

  void _fetch() {
    ref.read(ordersProvider.notifier).fetchGetOrders();
  }

  Future _refresh() async {
    ref.read(ordersProvider.notifier)
      ..resetState()
      ..fetchGetOrders();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      final state = ref.watch(ordersProvider);

      if (state is LoadingViewState) {
        return const LoaderWidget();
      }
      if (state is ErrorViewState) {
        return Text(state.errorMessage);
      }
      if (state is LoadedViewState<List<OrdersEntity>>) {
        final provider = ref.read(ordersProvider.notifier);
        return RefreshIndicator(
          onRefresh: _refresh,
          child: ListView.builder(
              itemCount: state.data.length + 1,
              key: const PageStorageKey(AppKeys.ordersListKey),
              itemBuilder: (BuildContext context, int index) {
                if (index == state.data.length) {
                  if (provider.hasMoreData &&
                      state.data.length >= AppConstants.paginationLimit) {
                    provider.loadMore();
                    return const Padding(
                      padding: EdgeInsets.all(10),
                      child: SizedBox(
                        child: LoaderWidget(),
                      ),
                    );
                  } else {
                    return const SizedBox();
                  }
                }
                return InkWell(
                  onTap: () => state.data[index].orderStatus ==
                              OrderStatusType.created &&
                          state.data[index].orderID != null
                      ? ref
                          .read(orderDetailsProvider.notifier)
                          .fetchGetOrderById(state.data[index].orderID!)
                          .then((val) {
                          context.pushRoute(
                            CreateOrderRoute(
                              order: ref
                                  .watch(orderDetailsProvider.notifier)
                                  .order,
                            ),
                          );
                        })
                      : context.pushRoute(
                          OrderDetailsRoute(order: state.data[index]),
                        ),
                  child: CardItemsWidget(
                    title: state.data[index].projectName ?? '',
                    subtitle: state.data[index].orderNo?.toString() ?? '',
                    image: 'assets/images/orders_image.png',
                    imageBackground: AppColors.brownCard,
                    statusName:
                        state.data[index].orderStatus?.translatedName ?? '',
                    statusColor: state.data[index].orderStatus?.statusColor,
                    descriptionWidget: [
                      DescriptionCardItemWidget(
                        title: 'order_by'.tr(),
                        trailing: state.data[index].customerName ?? '',
                      ),
                      DescriptionCardItemWidget(
                        title: 'order_date'.tr(),
                        trailing: state.data[index].createdOn != null
                            ? HelperFunctions.formatDate(
                                state.data[index].createdOn!)
                            : '',
                      ),
                      DescriptionCardItemWidget(
                        title: 'order_type'.tr(),
                        trailing: state.data[index].orderTypeName ?? '',
                      ),
                      DescriptionCardItemWidget(
                        title: 'employee_name'.tr(),
                        trailing: state.data[index].employeeName ?? '',
                      ),
                    ],
                  ),
                );
              }),
        );
      }
      return const SizedBox();
    });
  }
}
