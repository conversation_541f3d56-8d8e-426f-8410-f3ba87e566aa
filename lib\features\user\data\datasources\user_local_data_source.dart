import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/repositories/token_repository.dart';
import 'package:mdd/features/user/data/models/user_model.dart';
import 'package:mdd/utils/sentry_reporter.dart';

abstract class UserLocalDataSource {
  Future<void> cashUserData(UserModel user, String password);
  Future<Map<String, String>?> getUserData();
}

final userLocalDataSourceImpl = Provider<UserLocalDataSourceImpl>(
    (ref) => UserLocalDataSourceImpl(ref.watch(tokenRepositoryProvider)));

class UserLocalDataSourceImpl implements UserLocalDataSource {
  final TokenRepository _tokenRepository;
  UserLocalDataSourceImpl(this._tokenRepository);

  @override
  Future<void> cashUserData(UserModel user, String password) async {
    try {
      await _tokenRepository.saveUserAccess(user, password);
      log('cashed successfully');
    } catch (e, stackTrace) {
      SentryReporter.genericThrow(e, stackTrace: stackTrace);
      log('error cashed', error: e, stackTrace: stackTrace);
    }
  }

  @override
  Future<Map<String, String>?> getUserData() async {
    return await _tokenRepository.readUserAccess();
  }
}
