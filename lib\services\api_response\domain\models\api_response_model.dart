import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/services/api_response/data/entities/api_response_entity.dart';
import 'package:mdd/services/api_response/domain/models/search_model.dart';

part 'api_response_model.g.dart';

@JsonSerializable()
class ApiResponseModel extends ApiResponseEntity {
  ApiResponseModel({
    required super.data,
    required super.title,
    required super.status,
    required super.message,
    required super.messageAr,
    required SearchModel? super.search,
  });

  factory ApiResponseModel.fromJson(Map<String, dynamic> json) =>
      _$ApiResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$ApiResponseModelToJson(this);

  @override
  String toString() => '';
}
