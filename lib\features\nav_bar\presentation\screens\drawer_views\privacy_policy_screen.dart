import 'package:auto_route/annotations.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/core/widgets/subtitle_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/theme/dimensions.dart';

@RoutePage()
class PrivacyPolicyScreen extends ConsumerStatefulWidget {
  const PrivacyPolicyScreen({
    super.key,
  });

  @override
  ConsumerState createState() => _PrivacyPolicyScreenState();
}

class _PrivacyPolicyScreenState extends ConsumerState<PrivacyPolicyScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MainAppBar(),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DetailsCustomBar(title: 'terms_and_conditions'.tr()),
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.kSizeMedium,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextWidget(
                      'contractual'.tr(),
                      fontWeight: FontWeight.bold,
                    ),
                    TextWidget(
                      'read_t_and_c_message'.tr(),
                    ),
                    const SizedBox(
                      height: AppDimensions.kSizeMedium,
                    ),
                    TextWidget(
                      "1. ${'t_and_c_1'.tr()}",
                      fontSize: AppDimensions.kSizeMedium2,
                    ),
                    const SizedBox(
                      height: AppDimensions.kSizeMedium,
                    ),
                    TextWidget(
                      "2. ${'t_and_c_2'.tr()}",
                      fontSize: AppDimensions.kSizeMedium2,
                    ),
                    const SizedBox(
                      height: AppDimensions.kSizeMedium,
                    ),
                    TextWidget(
                      "3. ${'t_and_c_3'.tr()}",
                      fontSize: AppDimensions.kSizeMedium2,
                    ),
                    const SizedBox(
                      height: AppDimensions.kSizeMedium,
                    ),
                    TextWidget(
                      "4. ${'t_and_c_4'.tr()}",
                      fontSize: AppDimensions.kSizeMedium2,
                    ),
                    const SizedBox(
                      height: AppDimensions.kSizeMedium,
                    ),
                    TextWidget(
                      "5. ${'t_and_c_5'.tr()}",
                      fontSize: AppDimensions.kSizeMedium2,
                    ),
                    SubtitleWidget(
                      "- ${'t_and_c_5_1'.tr()}",
                    ),
                    SubtitleWidget(
                      "- ${'t_and_c_5_2'.tr()}",
                    ),
                    SubtitleWidget(
                      "- ${'t_and_c_5_3'.tr()}",
                    ),
                    SubtitleWidget(
                      "- ${'t_and_c_5_4'.tr()}",
                    ),
                    SubtitleWidget(
                      "- ${'t_and_c_5_5'.tr()}",
                    ),
                    SubtitleWidget(
                      "- ${'t_and_c_5_6'.tr()}",
                    ),
                    const SizedBox(
                      height: AppDimensions.kSizeMedium,
                    ),
                    TextWidget(
                      "6. ${'t_and_c_6'.tr()}",
                      fontSize: AppDimensions.kSizeMedium2,
                    ),
                    const SizedBox(
                      height: AppDimensions.kSizeMedium,
                    ),
                    TextWidget(
                      "7. ${'t_and_c_7'.tr()}",
                      fontSize: AppDimensions.kSizeMedium2,
                    ),
                    const SizedBox(
                      height: AppDimensions.kSizeMedium,
                    ),
                    TextWidget(
                      "8. ${'t_and_c_8'.tr()}",
                      fontSize: AppDimensions.kSizeMedium2,
                    ),
                    const SizedBox(
                      height: AppDimensions.kSizeMedium,
                    ),
                    TextWidget(
                      "9. ${'t_and_c_9'.tr()}",
                      fontSize: AppDimensions.kSizeMedium2,
                    ),
                    TextWidget(
                      "* ${'t_and_c_9_note1'.tr()}",
                      fontSize: AppDimensions.kSizeMedium2,
                    ),
                    SubtitleWidget(
                      "- ${'t_and_c_9_1'.tr()}",
                    ),
                    SubtitleWidget(
                      "- ${'t_and_c_9_2'.tr()}",
                    ),
                    SubtitleWidget(
                      "- ${'t_and_c_9_3'.tr()}",
                    ),
                    const SizedBox(
                      height: AppDimensions.kSizeMedium,
                    ),
                    TextWidget(
                      "10. ${'t_and_c_10'.tr()}",
                      fontSize: AppDimensions.kSizeMedium2,
                    ),
                    const SizedBox(
                      height: AppDimensions.kSizeXXLarge,
                    ),
                  ],
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
