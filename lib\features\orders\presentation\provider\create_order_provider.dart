import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/orders/data/models/create_order_model.dart';
import 'package:mdd/features/orders/domain/usecases/create_order.dart';
import 'package:mdd/features/orders/presentation/provider/add_order_provider.dart';

final createOrderProvider =
    StateNotifierProvider.autoDispose<CreateOrderProvider, ViewState>(
        (ref) => CreateOrderProvider(
              ref.watch(createOrderUseCaseProvider),
              ref.watch(addOrderProvider).getCreatedOrder(),
            ));

class CreateOrderProvider extends BaseProvider<ResultModel> {
  final CreateOrder _createOrder;
  final CreateOrderModel _createOrderModel;
  CreateOrderProvider(
    this._createOrder,
    this._createOrderModel,
  );

  Future<void> fetchCreateOrder({bool isDraft = false}) async {
    setLoadingState();
    final response = await _createOrder.call(
      CreateOrderParams(createdOrder: _createOrderModel, isDraft: isDraft),
    );
    response.fold((failure) {
      setErrorState(failure.message);
    }, (data) {
      setLoadedState(data);
    });
  }
}
