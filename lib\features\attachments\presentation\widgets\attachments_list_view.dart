import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/attachment_item_widget.dart';
import 'package:mdd/core/widgets/card_view_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/attachments/presentation/provider/attachments_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';

class AttachmentsListView extends StatelessWidget {
  final bool primary;
  const AttachmentsListView({
    super.key,
    this.primary = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding:
              const EdgeInsets.symmetric(horizontal: AppDimensions.kSizeMedium),
          child: Row(
            children: [
              TextWidget(
                'attachments_list'.tr(),
                color: AppColors.listTileTitleTextGrey,
              ),
              Consumer(builder: (context, ref, child) {
                final totalAttachments =
                    ref.watch(attachmentsProvider).totalAttachments;
                return CardViewWidget(
                  title: totalAttachments.toString(),
                  isDisabled: false,
                );
              })
            ],
          ),
        ),
        Consumer(builder: (context, ref, child) {
          final attachments = ref.watch(attachmentsProvider).attachments;
          return ListView.builder(
            itemCount: attachments.length,
            shrinkWrap: true,
            primary: primary,
            itemBuilder: (context, index) {
              return AttachmentItemWidget(
                key: ValueKey(attachments[index].orderAttachmentId),
                fileName: attachments[index].fileName ?? '',
                onPressed: () {
                  ref
                      .read(attachmentsProvider)
                      .removeAttachment(attachments[index].orderAttachmentId);
                },
              );
            },
          );
        }),
      ],
    );
  }
}
