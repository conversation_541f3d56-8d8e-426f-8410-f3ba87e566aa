import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/core/widgets/title_item_widget.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/presentation/providers/categories_provider.dart';
import 'package:mdd/features/dropdowns/presentation/providers/products_list_provider.dart';
import 'package:mdd/features/dropdowns/presentation/providers/projects_list_provider.dart';
import 'package:mdd/features/dropdowns/presentation/providers/units_list_provider.dart';
import 'package:mdd/features/dropdowns/presentation/widgets/categories_view.dart';
import 'package:mdd/features/dropdowns/presentation/widgets/projects_list_drop_down_widget.dart';
import 'package:mdd/features/orders/data/models/orders_model.dart';
import 'package:mdd/features/orders/presentation/provider/add_order_provider.dart';
import 'package:mdd/features/orders/presentation/provider/qty_order_controller_provider.dart';
import 'package:mdd/features/orders/presentation/provider/selected_order_type_provider.dart';
import 'package:mdd/features/orders/presentation/screens/create_order/order_type_view.dart';
import 'package:mdd/features/orders/presentation/widgets/bottom_order_widget.dart';
import 'package:mdd/features/orders/presentation/widgets/list_tile_item_widget.dart';
import 'package:mdd/features/products/presentation/screens/products_screen.dart';
import 'package:mdd/features/units/presentation/screens/units_view.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/constants/regex_constants.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

@RoutePage()
class CreateOrderScreen extends ConsumerStatefulWidget {
  final OrdersModel? order;
  const CreateOrderScreen({
    super.key,
    this.order,
  });

  @override
  ConsumerState<CreateOrderScreen> createState() => _CreateOrderScreenState();
}

class _CreateOrderScreenState extends ConsumerState<CreateOrderScreen> {
  @override
  void initState() {
    super.initState();
    UiHelper.postBuildCallback((p0) {
      if (widget.order != null) {
        ref.read(addOrderProvider.notifier).setDraftOrder(widget.order!);
      } else {
        ref.read(addOrderProvider.notifier).clearAll();
      }
    });
  }

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MainAppBar(),
      body: Column(
        children: [
          DetailsCustomBar(
            title: 'add_order'.tr(),
            activeIndex: 0,
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.kSizeMedium,
                    ),
                    child: ProjectsListDropDownWidget(
                      value: widget.order?.projectID,
                    ),
                  ),
                  Consumer(builder: (context, ref, child) {
                    final state = ref.watch(projectsListProvider);
                    return state is LoadedViewState<List<DropDownEntity>>
                        ? Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppDimensions.kSizeMedium,
                            ),
                            child: OrderTypeView(
                              value: widget.order?.orderType,
                            ),
                          )
                        : const SizedBox();
                  }),
                  const SizedBox(
                    height: AppDimensions.kSizeLarge,
                  ),
                  Consumer(
                    builder: (context, ref, child) {
                      final selectedProjectState =
                          ref.watch(selectedProjectProvider);
                      final selectedOrderTypeState =
                          ref.watch(selectedOrderTypeProvider);
                      if (selectedProjectState != null &&
                          selectedOrderTypeState != null) {
                        return Column(
                          children: [
                            TitleItemWidget(title: 'details'.tr()),
                            const SizedBox(
                              height: AppDimensions.kSizeLarge,
                            ),
                            Card(
                              color: AppColors.cardDetailsBackground,
                              elevation: 5,
                              margin: const EdgeInsets.symmetric(
                                  horizontal: AppDimensions.kSizeMedium),
                              shadowColor: AppColors.black.withOpacity(0.3),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: AppDimensions.kSizeLarge),
                                child: Column(
                                  children: [
                                    const SizedBox(
                                      height: AppDimensions.kSizeSmall,
                                    ),
                                    Consumer(builder: (context, ref, child) {
                                      final selectedProduct =
                                          ref.watch(selectedProductProvider);
                                      return ListTileItemWidget(
                                        title: selectedProduct == null
                                            ? 'product_name'.tr()
                                            : selectedProduct.textAr ??
                                                selectedProduct.textEn ??
                                                '',
                                        isSelected: selectedProduct != null,
                                        onTap: () async {
                                          await showDialog(
                                            context: context,
                                            builder: (_) {
                                              return AlertDialog(
                                                  contentPadding:
                                                      EdgeInsets.zero,
                                                  backgroundColor: AppColors
                                                      .cardDetailsBackground,
                                                  shape: RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              10)),
                                                  content: SizedBox(
                                                      height:
                                                          context.heightR(0.6),
                                                      width:
                                                          context.widthR(0.8),
                                                      child:
                                                          const ProductsScreen()));
                                            },
                                          ).then((value) => ref
                                              .read(
                                                  productsSearchControllerProvider
                                                      .state)
                                              .state
                                              .text = '');
                                        },
                                      );
                                    }),
                                    const SizedBox(
                                      height: AppDimensions.kSizeSmall,
                                    ),
                                    Consumer(builder: (context, ref, child) {
                                      final selectedCategory =
                                          ref.watch(selectedCategoryProvider);
                                      return ListTileItemWidget(
                                        title: selectedCategory == null
                                            ? 'category'.tr()
                                            : (context.locale.languageCode ==
                                                        'ar'
                                                    ? selectedCategory.textAr
                                                    : selectedCategory
                                                        .textEn) ??
                                                '',
                                        isSelected: selectedCategory != null,
                                        onTap: () async {
                                          await showDialog(
                                            context: context,
                                            builder: (_) {
                                              return AlertDialog(
                                                  contentPadding:
                                                      EdgeInsets.zero,
                                                  backgroundColor: AppColors
                                                      .cardDetailsBackground,
                                                  shape: RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              10)),
                                                  content: SizedBox(
                                                      height:
                                                          context.heightR(0.6),
                                                      width:
                                                          context.widthR(0.8),
                                                      child:
                                                          const CategoriesView()));
                                            },
                                          );
                                        },
                                      );
                                    }),
                                    const SizedBox(
                                      height: AppDimensions.kSizeSmall,
                                    ),
                                    Row(children: [
                                      Expanded(
                                        child: Consumer(
                                            builder: (context, ref, child) {
                                          final selectedUnit =
                                              ref.watch(selectedUnitProvider);
                                          return ListTileItemWidget(
                                            title: selectedUnit == null
                                                ? 'unit'.tr()
                                                : (context.locale
                                                                .languageCode ==
                                                            'ar'
                                                        ? selectedUnit.textAr
                                                        : selectedUnit
                                                            .textEn) ??
                                                    '',
                                            isSelected: selectedUnit != null,
                                            onTap: () async {
                                              await showDialog(
                                                context: context,
                                                builder: (_) {
                                                  return AlertDialog(
                                                      contentPadding:
                                                          EdgeInsets.zero,
                                                      backgroundColor: AppColors
                                                          .cardDetailsBackground,
                                                      shape: RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      10)),
                                                      content: SizedBox(
                                                          height: context
                                                              .heightR(0.6),
                                                          width: context
                                                              .widthR(0.8),
                                                          child:
                                                              const UnitsView()));
                                                },
                                              );
                                            },
                                          );
                                        }),
                                      ),
                                      const SizedBox(
                                        width: AppDimensions.kSize5XLarge,
                                      ),
                                      Expanded(
                                          child: Column(
                                        children: [
                                          Consumer(
                                              builder: (context, ref, child) {
                                            final controller = ref.watch(
                                                qtyOrderControllerProvider);
                                            return TextFormField(
                                              key: _formKey,
                                              controller: controller,
                                              scrollPadding: EdgeInsets.only(
                                                  bottom: MediaQuery.of(context)
                                                          .viewInsets
                                                          .bottom +
                                                      context.heightR(0.2)),
                                              textAlignVertical:
                                                  TextAlignVertical.center,
                                              validator: (value) {
                                                if (value != null &&
                                                    int.parse(value) <= 0) {
                                                  return 'quantity_validate'
                                                      .tr();
                                                }
                                                return null;
                                              },
                                              keyboardType:
                                                  TextInputType.number,
                                              inputFormatters: [
                                                FilteringTextInputFormatter
                                                    .allow(RegexConstants
                                                        .kNumberRegex),
                                              ],
                                              decoration: InputDecoration(
                                                fillColor: Colors.transparent,
                                                focusedBorder:
                                                    const UnderlineInputBorder(
                                                        borderSide: BorderSide(
                                                  color: Colors.transparent,
                                                )),
                                                enabledBorder:
                                                    const UnderlineInputBorder(
                                                        borderSide: BorderSide(
                                                  color: Colors.transparent,
                                                )),
                                                hintText: "quantity".tr(),
                                                hintStyle: context
                                                    .textTheme.bodySmall
                                                    ?.copyWith(
                                                  color: AppColors.tabTextColor,
                                                  fontSize:
                                                      AppDimensions.kSizeLarge,
                                                ),
                                                contentPadding:
                                                    const EdgeInsets.symmetric(
                                                  vertical:
                                                      AppDimensions.kSizeXSmall,
                                                ),
                                              ),
                                            );
                                          }),
                                          const Divider(),
                                        ],
                                      )),
                                    ]),
                                    const SizedBox(
                                      height: AppDimensions.kSizeMedium,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: AppDimensions.kSizeMedium,
                                  horizontal: AppDimensions.kSizeMedium),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  CupertinoButton(
                                      onPressed: () {
                                        ref
                                            .read(addOrderProvider.notifier)
                                            .resetItems();
                                      },
                                      child: TextWidget('reset'.tr())),
                                  ButtonWidget(
                                    onPressed: _canAddItem()
                                        ? () {
                                            ref
                                                .read(addOrderProvider.notifier)
                                                .addItem();
                                          }
                                        : null,
                                    title: 'save'.tr(),
                                    disabledColor: AppColors.white,
                                    shapeRadius: AppDimensions.kMediumRadius,
                                    disabledBackground:
                                        AppColors.disabledColor1,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        );
                      }
                      return const SizedBox();
                    },
                  ),
                ],
              ),
            ),
          ),
          BottomOrderWidget(
            onNextPressed: () =>
                context.pushRoute(const AddOrderAttachmentsRoute()),
          ),
        ],
      ),
    );
  }

  bool _canAddItem() {
    return ref.watch(selectedProductProvider) != null &&
        ref.watch(selectedCategoryProvider) != null &&
        ref.watch(selectedUnitProvider) != null;
  }
}
