import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/customer/data/models/customers_model.dart';
import 'package:mdd/features/customer/data/repositories/customers_repository_impl.dart';
import 'package:mdd/features/customer/domain/repositories/customers_repository.dart';

final customersUseCaseProvider =
    Provider<Customers>((ref) => Customers(ref.watch(customersRepositoryImpl)));

class Customers implements UseCase<List<CustomersModel>, CustomersParams> {
  final CustomersRepository _customersRepository;

  Customers(this._customersRepository);

  @override
  Future<Either<Failure, List<CustomersModel>>> call(
      CustomersParams params) async {
    return await _customersRepository.getCustomers(params);
  }
}

class CustomersParams {
  final int? page;
  final int? limit;
  final String? find;

  const CustomersParams({
    required this.page,
    this.limit,
    this.find,
  });
}
