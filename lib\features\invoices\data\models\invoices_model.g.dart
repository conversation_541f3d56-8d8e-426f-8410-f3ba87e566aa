// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invoices_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

InvoicesModel _$InvoicesModelFromJson(Map<String, dynamic> json) =>
    InvoicesModel(
      invoiceID: json['invoiceID'] as String?,
      orderID: json['orderID'] as String?,
      dueDate: json['dueDate'] as String?,
      createOn: json['createOn'] as String?,
      amount: (json['amount'] as num?)?.toDouble(),
      paidAmount: (json['paidAmount'] as num?)?.toDouble(),
      pendingAmount: (json['pendingAmount'] as num?)?.toDouble(),
      status: $enumDecodeNullable(_$InvoicesStatusTypeEnumMap, json['status']),
      employeeName: json['employeeName'] as String?,
      orderNo: json['orderNo'] as int?,
      statusName: json['statusName'] as String?,
      invoicePath: json['invoicePath'] as String?,
    );

Map<String, dynamic> _$InvoicesModelToJson(InvoicesModel instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'invoiceID': instance.invoiceID,
      'orderID': instance.orderID,
      'paidAmount': instance.paidAmount,
      'pendingAmount': instance.pendingAmount,
      'createOn': instance.createOn,
      'dueDate': instance.dueDate,
      'orderNo': instance.orderNo,
      'employeeName': instance.employeeName,
      'status': _$InvoicesStatusTypeEnumMap[instance.status],
      'statusName': instance.statusName,
      'invoicePath': instance.invoicePath,
    };

const _$InvoicesStatusTypeEnumMap = {
  InvoicesStatusType.draft: 0,
  InvoicesStatusType.unpaid: 1,
  InvoicesStatusType.overDuo: 2,
  InvoicesStatusType.paid: 3,
  InvoicesStatusType.paidPartial: 4,
  InvoicesStatusType.canceled: 5,
  InvoicesStatusType.creditNote: 6
};
