// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'orders_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrdersModel _$OrdersModelFromJson(Map<String, dynamic> json) => OrdersModel(
      orderID: json['orderID'] as String?,
      projectID: json['projectID'] as String?,
      projectName: json['projectName'] as String?,
      orderNo: json['orderNo'] as int?,
      prNumber: json['prNumber'] as String?,
      description: json['description'] as String?,
      createdOn: json['createdOn'] as String?,
      orderStatus:
          $enumDecodeNullable(_$OrderStatusTypeEnumMap, json['orderStatus']),
      orderStatusName: json['orderStatusName'] as String?,
      orderType: $enumDecodeNullable(_$OrderTypesEnumMap, json['orderType']),
      orderTypeName: json['orderTypeName'] as String?,
      submitedOn: json['submitedOn'] as String?,
      organizationID: json['organizationID'] as String?,
      organizationName: json['organizationName'] as String?,
      employeeID: json['employeeID'] as String?,
      employeeName: json['employeeName'] as String?,
      isDelayed: json['isDelayed'] as bool?,
      customerID: json['customerID'] as String?,
      customerName: json['customerName'] as String?,
      dnSentOn: json['dnSentOn'] as String?,
      isDeleted: json['isDeleted'] as bool?,
      cancelBy: json['cancelBy'] as String?,
      cancelByName: json['cancelByName'] as String?,
      canceledOn: json['canceledOn'] as String?,
      orderCancelReasonID: json['orderCancelReasonID'] as String?,
      orderCancelReasonName: json['orderCancelReasonName'] as String?,
      cancelNote: json['cancelNote'] as String?,
      isCreatedByEmployee: json['isCreatedByEmployee'] as bool?,
      createdBy: json['createdBy'] as String?,
      createdByName: json['createdByName'] as String?,
      isPricingFeeRequired: json['isPricingFeeRequired'] as bool?,
      pricingFee: (json['pricingFee'] as num?)?.toDouble(),
      pricingFeeDate: json['pricingFeeDate'] as String?,
      pricingFeeBy: json['pricingFeeBy'] as String?,
      pricingFeeNotes: json['pricingFeeNotes'] as String?,
      isPricingFeePaid: json['isPricingFeePaid'] as bool?,
      hasDeliveryNote: json['hasDeliveryNote'] as bool?,
      hasReview: json['hasReview'] as bool?,
      isFromEmail: json['isFromEmail'] as bool?,
      emailTitle: json['emailTitle'] as String?,
      quotationTime: json['quotationTime'] as int?,
      deliveryTime: json['deliveryTime'] as int?,
      quotationActualTime: (json['quotationActualTime'] as num?)?.toDouble(),
      deliveryActualTime: (json['deliveryActualTime'] as num?)?.toDouble(),
      evaluationPer: (json['evaluationPer'] as num?)?.toDouble(),
      sosActive: json['sosActive'] as bool?,
      isSAPAccepted: json['isSAPAccepted'] as bool?,
      sapFileContent: json['sapFileContent'] as String?,
      sapFileContentType: json['sapFileContentType'] as String?,
      sapFileName: json['sapFileName'] as String?,
      sapAcceptedOn: json['sapAcceptedOn'] as String?,
      expectedBudget: (json['expectedBudget'] as num?)?.toDouble(),
      termsAndConditions: json['termsAndConditions'] as String?,
      recommendedSuppliers: json['recommendedSuppliers'] as String?,
      currentApprover: json['currentApprover'] as String?,
      currentApproverName: json['currentApproverName'] as String?,
      details: (json['details'] as List<dynamic>?)
          ?.map((e) => OrderDetailsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      quotations: (json['quotations'] as List<dynamic>?)
          ?.map((e) => QuotationsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      accepttedQuotation: json['accepttedQuotation'] == null
          ? null
          : QuotationsModel.fromJson(
              json['accepttedQuotation'] as Map<String, dynamic>),
      leftLeftProcessTime: (json['leftLeftProcessTime'] as num?)?.toDouble(),
      comments: (json['comments'] as List<dynamic>?)
          ?.map((e) => CommentsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      attachments: (json['attachments'] as List<dynamic>?)
          ?.map((e) => AttachmentsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      employee: json['employee'] as String?,
      customer: json['customer'] as String?,
      approvals: (json['approvals'] as List<dynamic>?)
          ?.map((e) => ApprovalModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$OrdersModelToJson(OrdersModel instance) =>
    <String, dynamic>{
      'orderID': instance.orderID,
      'projectID': instance.projectID,
      'projectName': instance.projectName,
      'orderNo': instance.orderNo,
      'createdOn': instance.createdOn,
      'orderStatus': _$OrderStatusTypeEnumMap[instance.orderStatus],
      'orderType': _$OrderTypesEnumMap[instance.orderType],
      'orderTypeName': instance.orderTypeName,
      'organizationID': instance.organizationID,
      'organizationName': instance.organizationName,
      'employeeID': instance.employeeID,
      'employeeName': instance.employeeName,
      'customerID': instance.customerID,
      'customerName': instance.customerName,
      'cancelNote': instance.cancelNote,
      'isSAPAccepted': instance.isSAPAccepted,
      'sapFileContent': instance.sapFileContent,
      'sapFileContentType': instance.sapFileContentType,
      'sapFileName': instance.sapFileName,
      'sapAcceptedOn': instance.sapAcceptedOn,
      'currentApprover': instance.currentApprover,
      'currentApproverName': instance.currentApproverName,
      'details': instance.details,
      'quotations': instance.quotations,
      'attachments': instance.attachments,
      'approvals': instance.approvals,
      'comments': instance.comments,
      'description': instance.description,
      'prNumber': instance.prNumber,
      'orderStatusName': instance.orderStatusName,
      'submitedOn': instance.submitedOn,
      'isDelayed': instance.isDelayed,
      'dnSentOn': instance.dnSentOn,
      'isDeleted': instance.isDeleted,
      'cancelBy': instance.cancelBy,
      'cancelByName': instance.cancelByName,
      'canceledOn': instance.canceledOn,
      'orderCancelReasonID': instance.orderCancelReasonID,
      'orderCancelReasonName': instance.orderCancelReasonName,
      'isCreatedByEmployee': instance.isCreatedByEmployee,
      'createdBy': instance.createdBy,
      'createdByName': instance.createdByName,
      'isPricingFeeRequired': instance.isPricingFeeRequired,
      'pricingFee': instance.pricingFee,
      'pricingFeeDate': instance.pricingFeeDate,
      'pricingFeeBy': instance.pricingFeeBy,
      'pricingFeeNotes': instance.pricingFeeNotes,
      'isPricingFeePaid': instance.isPricingFeePaid,
      'hasDeliveryNote': instance.hasDeliveryNote,
      'hasReview': instance.hasReview,
      'isFromEmail': instance.isFromEmail,
      'emailTitle': instance.emailTitle,
      'quotationTime': instance.quotationTime,
      'deliveryTime': instance.deliveryTime,
      'quotationActualTime': instance.quotationActualTime,
      'deliveryActualTime': instance.deliveryActualTime,
      'evaluationPer': instance.evaluationPer,
      'sosActive': instance.sosActive,
      'expectedBudget': instance.expectedBudget,
      'termsAndConditions': instance.termsAndConditions,
      'recommendedSuppliers': instance.recommendedSuppliers,
      'accepttedQuotation': instance.accepttedQuotation,
      'leftLeftProcessTime': instance.leftLeftProcessTime,
      'employee': instance.employee,
      'customer': instance.customer,
    };

const _$OrderStatusTypeEnumMap = {
  OrderStatusType.none: 0,
  OrderStatusType.created: 1,
  OrderStatusType.submitted: 2,
  OrderStatusType.underProcess: 3,
  OrderStatusType.quotationSubmitted: 4,
  OrderStatusType.quotationApproved: 5,
  OrderStatusType.quotationDeclient: 6,
  OrderStatusType.paymentWaiting: 7,
  OrderStatusType.paymentDone: 8,
  OrderStatusType.paymentApproved: 9,
  OrderStatusType.payemntDeclinet: 10,
  OrderStatusType.deliverProcess: 11,
  OrderStatusType.deliveryNoteSent: 12,
  OrderStatusType.completed: 13,
  OrderStatusType.closed: 14,
  OrderStatusType.canceled: 15,
  OrderStatusType.poWaitingForRequest: 16,
  OrderStatusType.ipoSubmitted: 17,
  OrderStatusType.ipoClosed: 18,
  OrderStatusType.quotationReturned: 19,
  OrderStatusType.quotationExpired: 20,
  OrderStatusType.quotationReNewed: 21,
  OrderStatusType.pricingFeeWaiting: 22,
  OrderStatusType.quotationReturnedAfterApproval: 23,
  OrderStatusType.ipoReturnd: 24,
  OrderStatusType.ipoRepoend: 25,
  OrderStatusType.waiting: 26,
  OrderStatusType.rejected: 27,
  OrderStatusType.preparingShipment: 28,
  OrderStatusType.ipoMissingDocs: 29,
  OrderStatusType.ipoInComplete: 30,
  OrderStatusType.invocing: 31,
};

const _$OrderTypesEnumMap = {
  OrderTypes.directPO: 1,
  OrderTypes.tender: 2,
  OrderTypes.lvp: 3,
  OrderTypes.payBill: 4,
  OrderTypes.etimadTender: 5,
};
