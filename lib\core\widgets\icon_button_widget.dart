import 'package:flutter/material.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';

class IconButtonWidget extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget widget;
  final Color widgetColor;
  final Color disabledColor;
  final Color backgroundColor;
  final Color borderColor;
  final Color disabledBackground;
  final double shapeRadius;
  final double horizontalPadding;
  const IconButtonWidget({
    super.key,
    required this.onPressed,
    required this.widget,
    this.widgetColor = AppColors.white,
    this.disabledColor = AppColors.disabledTitleColor1,
    this.backgroundColor = AppColors.cardDetailsBackground,
    this.borderColor = Colors.transparent,
    this.disabledBackground = AppColors.disabledColor1,
    this.shapeRadius = AppDimensions.kSmallRadius,
    this.horizontalPadding = AppDimensions.kSizeXSmall,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ButtonStyle(
        elevation: WidgetStateProperty.all(0),
        padding: WidgetStateProperty.all(
            EdgeInsets.symmetric(horizontal: horizontalPadding)),
        backgroundColor: WidgetStateProperty.resolveWith<Color>(
          (Set<WidgetState> states) {
            if (states.contains(WidgetState.pressed)) {
              return backgroundColor.withOpacity(0.7);
            } else if (states.contains(WidgetState.disabled)) {
              return disabledBackground;
            }
            return backgroundColor;
          },
        ),
        shape: WidgetStateProperty.resolveWith<RoundedRectangleBorder>(
          (Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return RoundedRectangleBorder(
                  side: const BorderSide(color: Colors.transparent),
                  borderRadius: BorderRadius.circular(shapeRadius));
            }
            return RoundedRectangleBorder(
                side: BorderSide(color: borderColor),
                borderRadius: BorderRadius.circular(shapeRadius));
          },
        ),
      ),
      child: widget,
    );
  }
}
