import 'package:dartz/dartz.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/features/payments/domain/entities/payments_entity.dart';
import 'package:mdd/features/payments/domain/usecases/create_new_payment.dart';
import 'package:mdd/features/payments/domain/usecases/payments.dart';

abstract class PaymentsRepository {
  Future<Either<Failure, List<PaymentsEntity>>> getPayments(
      PaymentsParams params);

  Future<Either<Failure, ResultModel>> createNewPayment(
      CreateNewPaymentParams params);
}
