// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'monthly_summary_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MonthlySummaryModel _$MonthlySummaryModelFromJson(Map<String, dynamic> json) =>
    MonthlySummaryModel(
      orgName: json['orgName'] as String?,
      monthName: json['monthName'] as String,
      supplychainer: json['supplychainer'] as String?,
      ordersCount: json['ordersCount'] as int,
      ordersAmount: (json['ordersAmount'] as num).toDouble(),
      ordersProfits: (json['ordersProfits'] as num).toDouble(),
      directOrdersCount: json['directOrdersCount'] as int,
      directOrdersAmount: (json['directOrdersAmount'] as num).toDouble(),
      directOrdersProfits: (json['directOrdersProfits'] as num).toDouble(),
      tenderOrdersCount: json['tenderOrdersCount'] as int,
      tenderOrdersAmount: (json['tenderOrdersAmount'] as num).toDouble(),
      tenderOrdersProfits: (json['tenderOrdersProfits'] as num).toDouble(),
    );

Map<String, dynamic> _$MonthlySummaryModelToJson(
        MonthlySummaryModel instance) =>
    <String, dynamic>{
      'orgName': instance.orgName,
      'monthName': instance.monthName,
      'supplychainer': instance.supplychainer,
      'ordersCount': instance.ordersCount,
      'ordersAmount': instance.ordersAmount,
      'ordersProfits': instance.ordersProfits,
      'directOrdersCount': instance.directOrdersCount,
      'directOrdersAmount': instance.directOrdersAmount,
      'directOrdersProfits': instance.directOrdersProfits,
      'tenderOrdersCount': instance.tenderOrdersCount,
      'tenderOrdersAmount': instance.tenderOrdersAmount,
      'tenderOrdersProfits': instance.tenderOrdersProfits,
    };
