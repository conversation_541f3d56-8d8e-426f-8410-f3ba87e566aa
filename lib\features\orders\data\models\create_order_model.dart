import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/attachments/data/models/attachments_model.dart';
import 'package:mdd/features/orders/data/models/order_details_model.dart';
part 'create_order_model.g.dart';

@JsonSerializable()
class CreateOrderModel {
  final String projectID;
  final String customerID;
  final String? orderID;
  final String organizationID;
  final int orderType;
  final String? description;
  final List<AttachmentsModel>? attachments;
  final List<OrderDetailsModel>? orderDetails;

  CreateOrderModel(
      {required this.projectID,
      required this.customerID,
      this.orderID,
      required this.organizationID,
      required this.orderType,
      this.description,
      this.attachments,
      this.orderDetails});

  factory CreateOrderModel.fromJson(Map<String, dynamic> json) =>
      _$CreateOrderModelFromJson(json);

  Map<String, dynamic> toJson() => _$CreateOrderModelToJson(this);
}
