import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/form_field_widget.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/customer/domain/usecases/create_new_customer.dart';
import 'package:mdd/features/customer/presentation/provider/create_customer_booleans.dart';
import 'package:mdd/features/customer/presentation/provider/create_new_customer_provider.dart';
import 'package:mdd/features/customer/presentation/provider/customers_provider.dart';
import 'package:mdd/features/customer/presentation/provider/selected_customer_type_provider.dart';
import 'package:mdd/features/customer/presentation/widgets/customers_type_drop_down_widget.dart';
import 'package:mdd/features/dropdowns/presentation/providers/cities_provider.dart';
import 'package:mdd/features/dropdowns/presentation/widgets/cities_list_drop_down_widget.dart';
import 'package:mdd/features/location_picker/presentation/provider/selected_location_provider.dart';
import 'package:mdd/features/user/domain/entities/user_entity.dart';
import 'package:mdd/features/user/presentation/provider/user_provider.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/constants/regex_constants.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

@RoutePage()
class AddCustomerScreen extends ConsumerStatefulWidget {
  const AddCustomerScreen({
    super.key,
  });

  @override
  ConsumerState createState() => _AddContactScreenState();
}

class _AddContactScreenState extends ConsumerState<AddCustomerScreen> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _fullNameController;
  late final TextEditingController _mobileController;
  late final TextEditingController _emailController;

  @override
  void initState() {
    _fullNameController = TextEditingController();
    _mobileController = TextEditingController();
    _emailController = TextEditingController();
    super.initState();
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _mobileController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(createNewCustomerProvider, (previous, state) {
      if (state is LoadingViewState) {
        UiHelper.showLoadingDialog(context);
      }
      if (state is LoadedViewState<ResultModel>) {
        Navigator.pop(context);
        Navigator.pop(context);
        ref.read(customersProvider.notifier)
          ..resetState()
          ..fetchCustomers();
        UiHelper.showNotification(
          'add_customer_success'.tr(),
          notificationType: NotificationType.success,
        );
      }
      if (state is ErrorViewState) {
        context.maybePop();
        UiHelper.showNotification(state.errorMessage);
      }
    });

    return Scaffold(
      appBar: const MainAppBar(),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DetailsCustomBar(title: 'add_customer'.tr()),
              const SizedBox(height: AppDimensions.kSizeLarge),
              FormFieldWidget(
                controller: _fullNameController,
                fillColor: AppColors.cardDetailsBackground,
                borderColor: AppColors.cardDetailsBackground,
                textInputType: TextInputType.text,
                icon: 'assets/icons/name_icon.svg',
                validator: (value) {
                  if (value != null && value.isEmpty) {
                    return 'enter_valid_name'.tr();
                  }
                  return null;
                },
                hintText: 'name'.tr(),
              ),
              FormFieldWidget(
                controller: _mobileController,
                fillColor: AppColors.cardDetailsBackground,
                borderColor: AppColors.cardDetailsBackground,
                textInputType: TextInputType.phone,
                icon: 'assets/icons/phone_icon.svg',
                validator: (value) {
                  if (value != null && value.length == 10) {
                    return null;
                  }
                  return 'enter_valid_phone'.tr();
                },
                inputFormatters: [
                  LengthLimitingTextInputFormatter(10),
                  FilteringTextInputFormatter.allow(RegexConstants.kNumberRegex)
                ],
                hintText: 'phone_number'.tr(),
              ),
              FormFieldWidget(
                controller: _emailController,
                fillColor: AppColors.cardDetailsBackground,
                borderColor: AppColors.cardDetailsBackground,
                textInputType: TextInputType.emailAddress,
                validator: (value) {
                  if (value != null &&
                      !RegexConstants.kEmailRegex.hasMatch(value)) {
                    return 'enter_valid_email'.tr();
                  }
                  return null;
                },
                icon: 'assets/icons/email_icon.svg',
                hintText: 'email'.tr(),
              ),
              const CustomersTypeDropDownWidget(),
              const CitiesListDropDownWidget(),
              FormFieldWidget(
                controller: TextEditingController(
                  text: ref.watch(selectedLocationProvider)?.address,
                ),
                readOnly: true,
                onTap: () => context.pushRoute(const LocationPickerRoute()),
                fillColor: AppColors.cardDetailsBackground,
                borderColor: AppColors.cardDetailsBackground,
                icon: 'assets/icons/location_icon.svg',
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'address_validate'.tr();
                  }
                  return null;
                },
                hintText: 'address'.tr(),
              ),
              Row(
                children: [
                  Checkbox(
                    value: ref.watch(isActiveProvider),
                    activeColor: AppColors.bostonBlueColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppDimensions.kDefaultRadius,
                      ),
                    ),
                    onChanged: (value) {
                      if (value != null) {
                        ref.read(isActiveProvider.notifier).state = value;
                      }
                    },
                  ),
                  Expanded(
                    child: TextWidget(
                      'is_active_user'.tr(),
                      color: ref.watch(isActiveProvider)
                          ? AppColors.bostonBlueColor
                          : null,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Checkbox(
                    value: ref.watch(isEmailNotificationActiveProvider),
                    activeColor: AppColors.bostonBlueColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppDimensions.kDefaultRadius,
                      ),
                    ),
                    onChanged: (value) {
                      if (value != null) {
                        ref
                            .read(isEmailNotificationActiveProvider.notifier)
                            .state = value;
                      }
                    },
                  ),
                  Expanded(
                    child: TextWidget(
                      'is_email_notification_active_provider'.tr(),
                      color: ref.watch(isEmailNotificationActiveProvider)
                          ? AppColors.bostonBlueColor
                          : null,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Checkbox(
                    value: ref.watch(isSMSNotificationActiveProvider),
                    activeColor: AppColors.bostonBlueColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppDimensions.kDefaultRadius,
                      ),
                    ),
                    onChanged: (value) {
                      if (value != null) {
                        ref
                            .read(isSMSNotificationActiveProvider.notifier)
                            .state = value;
                      }
                    },
                  ),
                  Expanded(
                    child: TextWidget(
                      'is_SMS_notification_active_provider'.tr(),
                      color: ref.watch(isSMSNotificationActiveProvider)
                          ? AppColors.bostonBlueColor
                          : null,
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: AppDimensions.kSizeMedium2,
              ),
              ButtonWidget(
                onPressed: _callAddCustomer,
                title: 'create'.tr(),
                horizontalPadding: AppDimensions.kSizeXXLarge,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _callAddCustomer() async {
    if (_formKey.currentState!.validate()) {
      final customer =
          (ref.read(userProvider) as LoadedViewState<UserEntity>).data.customer;
      ref.read(createNewCustomerProvider.notifier).fetchCreateNewCustomer(
            CreateCustomerParams(
              fullName: _fullNameController.text,
              mobile: _mobileController.text.arabicNumberConverter(),
              email: _emailController.text,
              address: ref.read(selectedLocationProvider.state).state?.address,
              cityID: ref.read(selectedCityProvider)?.id,
              type: ref.read(selectedCustomerTypeProvider),
              organizationID: customer?.organizationID,
              isActive: ref.read(isActiveProvider.state).state,
              isEmailNotificationActive:
                  ref.read(isEmailNotificationActiveProvider.state).state,
              isSMSNotificationActive:
                  ref.read(isSMSNotificationActiveProvider.state).state,
            ),
          );
    }
  }
}
