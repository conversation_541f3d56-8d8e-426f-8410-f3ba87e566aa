import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/exceptions/exceptions.dart';
import 'package:mdd/features/home/<USER>/datasources/dashboard_remote_data_source.dart';
import 'package:mdd/features/home/<USER>/entities/dashboard_entity.dart';
import 'package:mdd/features/home/<USER>/repositories/dashboard_repository.dart';
import 'package:mdd/features/home/<USER>/usecases/dashboard.dart';

final dashboardRepositoryImpl =
    Provider<DashboardRepositoryImpl>((ref) => DashboardRepositoryImpl(
          ref.watch(dashboardRemoteDataSourceImpl),
        ));

class DashboardRepositoryImpl implements DashboardRepository {
  final DashboardRemoteDataSource dashboardDataSource;
  DashboardRepositoryImpl(this.dashboardDataSource);

  @override
  Future<Either<Failure, DashboardEntity>> getUserDashboard(
      DashboardParams params) async {
    try {
      final dashboardResponse =
          await dashboardDataSource.getUserDashboard(params);
      return Right(dashboardResponse!);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }
}
