import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/attachments/domain/entites/attachments_entity.dart';
import 'package:mdd/features/invoices/data/repositories/invoices_repository_impl.dart';
import 'package:mdd/features/invoices/domain/repositories/invoices_repository.dart';

final invoiceFileUseCaseProvider = Provider<InvoiceFile>(
    (ref) => InvoiceFile(ref.watch(invoicesRepositoryImpl)));

class InvoiceFile implements UseCase<AttachmentsEntity?, InvoiceFileParams> {
  final InvoicesRepository invoicesRepository;

  InvoiceFile(this.invoicesRepository);

  @override
  Future<Either<Failure, AttachmentsEntity?>> call(
      InvoiceFileParams params) async {
    return invoicesRepository.getInvoiceFileByID(params);
  }
}

class InvoiceFileParams {
  final String? id;
  InvoiceFileParams({required this.id});
}
