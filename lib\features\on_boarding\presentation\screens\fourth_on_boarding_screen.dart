import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/core/widgets/title_widget.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/extensions.dart';

@RoutePage()
class FourthOnBoardingScreen extends StatelessWidget {
  const FourthOnBoardingScreen({super.key});
  //FIXME App Hanging on this screen
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            constraints: const BoxConstraints.expand(),
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/images/login_background.png'),
                opacity: 0.2,
                fit: BoxFit.cover,
              ),
              gradient: LinearGradient(
                colors: [
                  Color(0xFF0C8991),
                  Color(0xFF2D9260),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          Stack(
            alignment: Alignment.bottomCenter,
            children: [
              Image.asset('assets/images/on_boarding_image4.png'),
              SafeArea(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      children: [
                        const SizedBox(height: AppDimensions.kSize7XLarge),
                        TextWidget(
                          'on_boarding_title4'.tr(),
                          color: AppColors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: context.textTheme.headlineMedium?.fontSize,
                        ),
                        const SizedBox(height: AppDimensions.kSizeXLarge),
                        const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircleAvatar(
                              radius: 5,
                              backgroundColor: AppColors.white,
                            ),
                            SizedBox(width: AppDimensions.kSizeSmall),
                            CircleAvatar(
                              radius: 5,
                              backgroundColor: AppColors.white,
                            ),
                            SizedBox(width: AppDimensions.kSizeSmall),
                            CircleAvatar(
                              radius: 5,
                              backgroundColor: AppColors.white,
                            ),
                            SizedBox(width: AppDimensions.kSizeSmall),
                            CircleAvatar(
                              radius: 5,
                              backgroundColor: AppColors.white,
                            ),
                          ],
                        ),
                      ],
                    ),
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: InkWell(
                        onTap: () =>
                            context.replaceRoute(const UserLoginRoute()),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            TitleWidget(
                              'start_now'.tr(),
                              color: AppColors.white,
                              fontSize: AppDimensions.kSize4XLarge,
                            ),
                            const SizedBox(height: AppDimensions.kSizeMedium),
                            Center(
                              child: Image.asset(
                                'assets/images/bottom_arrow_icon.png',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
