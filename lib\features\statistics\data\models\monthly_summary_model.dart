import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/statistics/domain/entity/monthly_summary_entity.dart';

part 'monthly_summary_model.g.dart';

@JsonSerializable()
class MonthlySummaryModel extends MonthlySummaryEntity {
  MonthlySummaryModel(
      {required super.orgName,
      required super.monthName,
      required super.supplychainer,
      required super.ordersCount,
      required super.ordersAmount,
      required super.ordersProfits,
      required super.directOrdersCount,
      required super.directOrdersAmount,
      required super.directOrdersProfits,
      required super.tenderOrdersCount,
      required super.tenderOrdersAmount,
      required super.tenderOrdersProfits});

  factory MonthlySummaryModel.fromJson(Map<String, dynamic> json) =>
      _$MonthlySummaryModelFromJson(json);

  Map<String, dynamic> toJson() => _$MonthlySummaryModelToJson(this);
}
