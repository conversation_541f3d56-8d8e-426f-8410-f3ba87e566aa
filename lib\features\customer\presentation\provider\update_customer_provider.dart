import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/customer/domain/entities/customers_entity.dart';
import 'package:mdd/features/customer/domain/usecases/update_customer.dart';

final updateCustomerProvider =
    StateNotifierProvider.autoDispose<UserCustomerProvider, ViewState>(
        (ref) => UserCustomerProvider(
              ref.watch(updateCustomerUseCaseProvider),
            ));

class UserCustomerProvider extends BaseProvider<ResultModel> {
  final UpdateCustomer _updateCustomer;

  UserCustomerProvider(this._updateCustomer);

  Future<void> updateCustomer(CustomersEntity customersEntity) async {
    setLoadingState();
    final response = await _updateCustomer.call(
      UpdateCustomerParams(customersEntity),
    );
    response.fold((failure) {
      setErrorState(failure.message);
    }, (user) {
      setLoadedState(user);
    });
  }
}
