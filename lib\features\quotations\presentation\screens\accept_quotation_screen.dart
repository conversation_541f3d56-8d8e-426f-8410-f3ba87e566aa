import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/form_field_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/core/widgets/title_widget.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/presentation/provider/accept_approval_quotation_provider.dart';
import 'package:mdd/features/quotations/presentation/provider/quotation_details_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class AcceptApprovalQuotationScreen extends ConsumerStatefulWidget {
  final QuotationsModel quotation;

  const AcceptApprovalQuotationScreen(
      this.quotation, {
        Key? key,
      }) : super(key: key);

  @override
  ConsumerState createState() => _AcceptApprovalQuotationScreenState();
}

class _AcceptApprovalQuotationScreenState
    extends ConsumerState<AcceptApprovalQuotationScreen> {
  final _notesController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    ref.listen(acceptApprovalQuotationProvider, (previous, state) {
      if (state is LoadingViewState) {
        UiHelper.showLoadingDialog(context);
      }
      if (state is LoadedViewState<ResultModel>) {
        Navigator.pop(context);
        Navigator.pop(context);
        UiHelper.showNotification('accept_quotation_success'.tr(),
            notificationType: NotificationType.success);
        ref
            .read(quotationDetailsProvider.notifier)
            .fetchQuotationDetails(widget.quotation.quotationID);
      }
      if (state is ErrorViewState) {
        context.popRoute();
        UiHelper.showNotification(state.errorMessage);
      }
    });
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding:
            const EdgeInsets.symmetric(vertical: AppDimensions.kSizeLarge),
            child: TitleWidget(
              'accept_quotation_question'.tr(),
              color: AppColors.disabledColor1,
              fontSize: AppDimensions.kSizeLarge2,
              fontWeight: FontWeight.bold,
            ),
          ),
          FormFieldWidget(
            controller: _notesController,
            fillColor: AppColors.white,
            borderColor: AppColors.cardDetailsBackground,
            hintText: 'notes'.tr(),
            textInputType: TextInputType.text,
            maxLines: 5,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.kSizeLarge,
              vertical: AppDimensions.kSizeLarge,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => context.popRoute(),
                  child: TextWidget('cancel'.tr()),
                ),
                ButtonWidget(
                  onPressed: () {
                    ref
                        .read(acceptApprovalQuotationProvider.notifier)
                        .acceptApprovalQuotation(widget.quotation.copyWith(
                      rejectNotes: _notesController.text,
                    ));
                  },
                  title: 'accept'.tr(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
