import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/features/comments/data/models/comments_model.dart';
import 'package:mdd/features/comments/domain/entities/comments_entity.dart';
import 'package:mdd/features/comments/presentation/provider/add_comments_provider.dart';
import 'package:mdd/features/comments/presentation/provider/order_comments_provider.dart';
import 'package:mdd/features/comments/presentation/widgets/comments_widget.dart';
import 'package:mdd/features/orders/presentation/provider/order_details_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class CommentsDetailsTapView extends ConsumerStatefulWidget {
  final List<CommentsEntity> comments;
  final String orderId;
  const CommentsDetailsTapView(
      {super.key, required this.comments, required this.orderId});

  @override
  ConsumerState<CommentsDetailsTapView> createState() =>
      _CommentsDetailsTapViewState();
}

class _CommentsDetailsTapViewState
    extends ConsumerState<CommentsDetailsTapView> {
  final TextEditingController _textEditingController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      // _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    ref
      ..listen(addCommentsProvider, (previous, state) {
        if (state is LoadingViewState) {
          UiHelper.showLoadingDialog(context);
        }
        if (state is LoadedViewState<ResultModel>) {
          context.popRoute();
          ref
              .read(orderCommentsProvider.notifier)
              .orderComments(widget.orderId);
          // _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
        }
        if (state is ErrorViewState) {
          context.popRoute();
          UiHelper.showNotification("حدث خطأ");
        }
      })
      ..listen(orderCommentsProvider, (previous, state) {
        if (state is LoadedViewState<List<CommentsModel>>) {
          ref.read(orderDetailsProvider.notifier).addComment(state.data);
        }
      });

    return Expanded(
      child: Column(
        children: [
          Consumer(builder: (context, ref, child) {
            return Expanded(
              child: ListView.builder(
                controller: _scrollController,
                itemCount: widget.comments.length,
                shrinkWrap: true,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.kSizeMedium,
                ),
                itemBuilder: (context, index) {
                  return CommentsWidget(
                    content: widget.comments[index].conetent ?? '',
                    commentUserType: widget.comments[index].userType,
                    timeAgo: widget.comments[index].createdOn,
                    senderName: widget.comments[index].fullName ?? '',
                  );
                },
              ),
            );
          }),
          Container(
            decoration: const BoxDecoration(color: AppColors.white, boxShadow: [
              BoxShadow(
                  color: Colors.black12,
                  offset: Offset(0, -1),
                  blurRadius: AppDimensions.kSizeSmall)
            ]),
            child: Padding(
              padding: const EdgeInsets.symmetric(
                vertical: AppDimensions.kSizeXXLarge,
                horizontal: AppDimensions.kSizeMedium,
              ),
              child: Row(
                children: [
                  // CircleAvatar(
                  //   radius: 25,
                  //   backgroundColor: AppColors.cardDetailsBackground,
                  //   child: SvgPicture.asset('assets/icons/message_icon.svg'),
                  // ),
                  // const SizedBox(
                  //   width: AppDimensions.kSizeSmall,
                  // ),
                  Expanded(
                      child: TextField(
                    controller: _textEditingController,
                    decoration: InputDecoration(
                      enabledBorder: OutlineInputBorder(
                          borderSide:
                              const BorderSide(color: Colors.transparent),
                          borderRadius: BorderRadius.circular(30)),
                      focusedBorder: OutlineInputBorder(
                          borderSide:
                              const BorderSide(color: Colors.transparent),
                          borderRadius: BorderRadius.circular(30)),
                      fillColor: AppColors.cardDetailsBackground,
                      hintText: 'أضف تعليق',
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: AppDimensions.kSizeMedium),
                      hintStyle:
                          const TextStyle(color: AppColors.hintTextColor),
                    ),
                  )),
                  const SizedBox(
                    width: AppDimensions.kSizeSmall,
                  ),
                  Consumer(builder: (context, ref, child) {
                    return InkWell(
                      onTap: () {
                        if (_textEditingController.text.isNotEmpty) {
                          ref.read(addCommentsProvider.notifier).addComment(
                                widget.orderId,
                                _textEditingController.text,
                              );
                          _textEditingController.clear();
                        }
                      },
                      child: CircleAvatar(
                        backgroundColor: AppColors.primary,
                        radius: 25,
                        child: SvgPicture.asset('assets/icons/send_icon.svg'),
                      ),
                    );
                  }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
