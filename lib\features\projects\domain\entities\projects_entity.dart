import 'package:mdd/core/models/paginated_model.dart';
import 'package:mdd/features/customer/data/models/customers_model.dart';

class ProjectsEntity implements PaginatedModel {
  final String? projectID;
  final String? organizationID;
  final String? name;
  final String? location;
  final String? description;
  final String? customerID;
  final CustomersModel? contactPerson;

  ProjectsEntity({
    this.projectID,
    this.customerID,
    this.organizationID,
    this.name,
    this.location,
    this.description,
    this.contactPerson,
  });
}
