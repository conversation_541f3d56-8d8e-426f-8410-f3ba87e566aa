import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/drop_down_widget.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/presentation/providers/banks_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class BanksListDropDownWidget extends ConsumerStatefulWidget {
  const BanksListDropDownWidget({
    Key? key,
    this.value,
  }) : super(key: key);

  final String? value;
  @override
  ConsumerState<BanksListDropDownWidget> createState() =>
      _BanksListDropDownWidgetState();
}

class _BanksListDropDownWidgetState
    extends ConsumerState<BanksListDropDownWidget> {
  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      final state = ref.watch(banksProvider);
      if (state is! LoadedViewState<List<DropDownEntity>>) {
        ref.read(banksProvider.notifier).fetchBanks();
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(banksProvider);
    if (state is LoadingViewState) {
      return const LoaderWidget();
    }
    if (state is LoadedViewState<List<DropDownEntity>>) {
      return Padding(
        padding: const EdgeInsets.symmetric(
          vertical: AppDimensions.kSizeSmall,
          horizontal: AppDimensions.kSizeMedium,
        ),
        child: DropDownWidget<DropDownEntity>(
          title: 'bank_name'.tr(),
          validatorMessage: 'bank_name_validate'.tr(),
          onChanged: (bank) {
            ref.watch(selectedBankProvider.notifier).state = bank;
          },
          borderColor: AppColors.cardDetailsBackground,
          compareFn: (item, sItem) => item.id == sItem.id,
          showSelectedItems: true,
          itemAsString: (bank) =>
              (context.locale.languageCode == 'en'
                  ? bank.textEn ?? bank.textAr
                  : bank.textAr ?? bank.textEn) ??
              '',
          items: state.data,
          value: widget.value != null ? getSelectedBank(state.data) : null,
        ),
      );
    }
    return const SizedBox();
  }

  DropDownEntity? getSelectedBank(List<DropDownEntity> banks) {
    for (final bank in banks) {
      if (bank.id == widget.value) return bank;
    }
    return null;
  }
}
