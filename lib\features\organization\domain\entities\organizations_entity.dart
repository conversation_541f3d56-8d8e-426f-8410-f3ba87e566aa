import 'package:mdd/core/models/paginated_model.dart';
import 'package:mdd/utils/enums.dart';

class OrgsEntity implements PaginatedModel {
  OrgsEntity({
    this.organizationID,
    this.name,
    this.cr,
    this.landLine,
    this.street,
    this.zipCode,
    this.cityID,
    this.cityName,
    this.email,
    this.website,
    this.creditBalance,
    this.creditDays,
    this.status,
    this.type,
    this.vatNumber,
    this.isPORequiredOnApproval,
    this.isPONumberAutoGenerated,
    this.isAllowedApproveQuotation,
  });

  String? organizationID;
  String? name;
  String? cr;
  String? landLine;
  String? street;
  String? zipCode;
  String? cityID;
  String? cityName;
  String? email;
  String? website;
  double? creditBalance;
  int? creditDays;
  OrganizationStatus? status;
  OrganizationType? type;
  String? vatNumber;
  bool? isPORequiredOnApproval;
  bool? isPONumberAutoGenerated;
  bool? isAllowedApproveQuotation;
}
