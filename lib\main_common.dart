import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_flavor/flutter_flavor.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart' as log;
import 'package:mdd/core/app.dart';
import 'package:mdd/services/firebase_cloud_messaging/firebase_cloud_messaging.dart';
import 'package:mdd/services/package_info.dart';
import 'package:mdd/utils/config_reader.dart';
import 'package:mdd/utils/constants/constants.dart';
import 'package:mdd/utils/constants/environment.dart';
import 'package:mdd/utils/sentry_reporter.dart';

import 'firebase_options.dart';
import 'services/shared_prefs.dart';

Future<void> mainCommon(String env) async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

  if (!kIsWeb) {
    await setupFlutterNotifications();
  }

  // Load the JSON config into memory
  await ConfigReader.initialize();

  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(statusBarColor: Colors.transparent),
  );
  //* Force Portrait Mode:------------------------
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  //* Log everything in debug builds.  Log warnings (and up) in release builds.
  log.Logger.root.level = kDebugMode ? log.Level.ALL : log.Level.WARNING;
  log.Logger.root.onRecord.listen((log.LogRecord rec) {
    debugPrint('${rec.level.name}: ${rec.time}: ${rec.message}');
  });

  // final List<Locale>? systemLocales = WidgetsBinding.instance.window.locales;
  // String? languageCode = systemLocales?.first.languageCode;
  // Locale locale =
  //     languageCode != null && (languageCode == "en" || languageCode == "ar")
  //         ? Locale(languageCode)
  //         : const Locale("ar");

  final container = ProviderContainer();
  // init sharedPrefs
  await container.read(sharedPrefsFutureProvider.future);
  // init packageInfo
  await container.read(packageInfoFutureProvider.future);

  //* Init easy localization
  await EasyLocalization.ensureInitialized();
  //* Init Flavor
  FlavorConfig(
    name: env == Environment.prod ? "Production" : "Development",
    color: Colors.green,
    location: BannerLocation.bottomStart,
    variables: <String, dynamic>{
      "flavor": env,
      "baseUrl": env == Environment.prod
          ? AppConstants.kBaseUrl
          : AppConstants.kBaseUrlDebug,
    },
  );
  //* Init sentry
  kDebugMode
      ? runApp(
    ProviderScope(
      parent: container,
      child: const App(),
    ),
  )
      : await SentryReporter.setup(parent: container);
}
