import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/user/data/repositories/user_repository_impl.dart';
import 'package:mdd/features/user/domain/repositories/user_repository.dart';

final deleteNotificationTokenUseCaseProvider =
    Provider<DeleteNotificationToken>(
        (ref) => DeleteNotificationToken(ref.watch(userRepositoryImpl)));

class DeleteNotificationToken implements UseCase<ResultModel, String> {
  final UserRepository userRepository;

  DeleteNotificationToken(this.userRepository);

  @override
  Future<Either<Failure, ResultModel>> call(String notificationToken) async {
    return await userRepository.deleteNotificationToken(notificationToken);
  }
}
