import 'package:flutter/material.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/presentation/widgets/quotation_item_widget.dart';

class QuotationDetailsTapView extends StatelessWidget {
  final List<QuotationsModel> quotations;
  const QuotationDetailsTapView({super.key, required this.quotations});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: ListView.builder(
        itemCount: quotations.length,
        itemBuilder: (context, index) {
          return QuotationItemWidget(quotation: quotations[index]);
        },
      ),
    );
  }
}
