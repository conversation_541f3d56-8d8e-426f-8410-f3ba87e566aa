import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/form_field_widget.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/customer/data/models/customers_model.dart';
import 'package:mdd/features/customer/presentation/provider/create_customer_booleans.dart';
import 'package:mdd/features/customer/presentation/provider/customers_provider.dart';
import 'package:mdd/features/customer/presentation/provider/selected_customer_type_provider.dart';
import 'package:mdd/features/customer/presentation/provider/update_customer_provider.dart';
import 'package:mdd/features/customer/presentation/widgets/customers_type_drop_down_widget.dart';
import 'package:mdd/features/dropdowns/presentation/providers/cities_provider.dart';
import 'package:mdd/features/dropdowns/presentation/widgets/cities_list_drop_down_widget.dart';
import 'package:mdd/features/location_picker/presentation/provider/selected_location_provider.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/constants/regex_constants.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

@RoutePage()
class EditCustomerScreen extends ConsumerStatefulWidget {
  const EditCustomerScreen(
    this.customer, {
    super.key,
  });

  final CustomersModel customer;
  @override
  ConsumerState createState() => _EditCustomerScreenState();
}

class _EditCustomerScreenState extends ConsumerState<EditCustomerScreen> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _mobileController = TextEditingController();
  final _emailController = TextEditingController();

  @override
  void initState() {
    _fullNameController.text = widget.customer.fullName ?? '';
    _mobileController.text = widget.customer.mobile ?? '';
    _emailController.text = widget.customer.email ?? '';
    UiHelper.postBuildCallback((p0) {
      ref.read(isActiveProvider.notifier).state =
          widget.customer.isActive ?? false;
      ref.read(isEmailNotificationActiveProvider.notifier).state =
          widget.customer.isEmailNotificationActive ?? false;
      ref.read(isSMSNotificationActiveProvider.notifier).state =
          widget.customer.isSMSNotificationActive ?? false;
    });
    super.initState();
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _mobileController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(updateCustomerProvider, (previous, state) {
      if (state is LoadingViewState) {
        UiHelper.showLoadingDialog(context);
      }
      if (state is LoadedViewState<ResultModel>) {
        Navigator.pop(context);
        Navigator.pop(context);
        ref.read(customersProvider.notifier)
          ..resetState()
          ..fetchCustomers();
        UiHelper.showNotification(
          'edit_customer_success'.tr(),
          notificationType: NotificationType.success,
        );
      }
      if (state is ErrorViewState) {
        context.maybePop();
        UiHelper.showNotification('edit_customer_failure'.tr());
      }
    });

    return Scaffold(
      appBar: const MainAppBar(),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DetailsCustomBar(title: 'edit_customer'.tr()),
              const SizedBox(height: AppDimensions.kSizeLarge),
              FormFieldWidget(
                controller: _fullNameController,
                fillColor: AppColors.cardDetailsBackground,
                borderColor: AppColors.cardDetailsBackground,
                textInputType: TextInputType.text,
                icon: 'assets/icons/name_icon.svg',
                validator: (value) {
                  if (value != null && value.isEmpty) {
                    return 'enter_valid_name'.tr();
                  }
                  return null;
                },
                hintText: 'name'.tr(),
              ),
              FormFieldWidget(
                controller: _mobileController,
                fillColor: AppColors.cardDetailsBackground,
                borderColor: AppColors.cardDetailsBackground,
                textInputType: TextInputType.phone,
                icon: 'assets/icons/phone_icon.svg',
                validator: (value) {
                  if (value != null && value.length == 10) {
                    return null;
                  }
                  return 'enter_valid_phone'.tr();
                },
                inputFormatters: [
                  LengthLimitingTextInputFormatter(10),
                  FilteringTextInputFormatter.allow(RegexConstants.kNumberRegex)
                ],
                hintText: 'phone_number'.tr(),
              ),
              FormFieldWidget(
                controller: _emailController,
                fillColor: AppColors.cardDetailsBackground,
                borderColor: AppColors.cardDetailsBackground,
                textInputType: TextInputType.emailAddress,
                validator: (value) {
                  if (value != null &&
                      !RegexConstants.kEmailRegex.hasMatch(value)) {
                    return 'enter_valid_email'.tr();
                  }
                  return null;
                },
                icon: 'assets/icons/email_icon.svg',
                hintText: 'email'.tr(),
              ),
              CustomersTypeDropDownWidget(
                value: ref.watch(selectedCustomerTypeProvider) ??
                    widget.customer.type,
              ),
              CitiesListDropDownWidget(
                value: ref.watch(selectedCityProvider)?.id ??
                    widget.customer.cityID,
              ),
              FormFieldWidget(
                controller: TextEditingController(
                  text: ref.watch(selectedLocationProvider)?.address ??
                      widget.customer.address,
                ),
                readOnly: true,
                onTap: () => context.pushRoute(const LocationPickerRoute()),
                fillColor: AppColors.cardDetailsBackground,
                borderColor: AppColors.cardDetailsBackground,
                icon: 'assets/icons/location_icon.svg',
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'address_validate'.tr();
                  }
                  return null;
                },
                hintText: 'address'.tr(),
              ),
              Row(
                children: [
                  Checkbox(
                    value: ref.watch(isActiveProvider),
                    activeColor: AppColors.bostonBlueColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppDimensions.kDefaultRadius,
                      ),
                    ),
                    onChanged: (value) {
                      if (value != null) {
                        ref.read(isActiveProvider.notifier).state = value;
                      }
                    },
                  ),
                  Expanded(
                    child: TextWidget(
                      'is_active_user'.tr(),
                      color: ref.watch(isActiveProvider)
                          ? AppColors.bostonBlueColor
                          : null,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Checkbox(
                    value: ref.watch(isEmailNotificationActiveProvider),
                    activeColor: AppColors.bostonBlueColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppDimensions.kDefaultRadius,
                      ),
                    ),
                    onChanged: (value) {
                      if (value != null) {
                        ref
                            .read(isEmailNotificationActiveProvider.notifier)
                            .state = value;
                      }
                    },
                  ),
                  Expanded(
                    child: TextWidget(
                      'is_email_notification_active_provider'.tr(),
                      color: ref.watch(isEmailNotificationActiveProvider)
                          ? AppColors.bostonBlueColor
                          : null,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Checkbox(
                    value: ref.watch(isSMSNotificationActiveProvider),
                    activeColor: AppColors.bostonBlueColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppDimensions.kDefaultRadius,
                      ),
                    ),
                    onChanged: (value) {
                      if (value != null) {
                        ref
                            .read(isSMSNotificationActiveProvider.notifier)
                            .state = value;
                      }
                    },
                  ),
                  Expanded(
                    child: TextWidget(
                      'is_SMS_notification_active_provider'.tr(),
                      color: ref.watch(isSMSNotificationActiveProvider)
                          ? AppColors.bostonBlueColor
                          : null,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.kSizeMedium2),
              ButtonWidget(
                onPressed: _callEditCustomer,
                title: 'edit'.tr(),
                horizontalPadding: AppDimensions.kSizeXXLarge,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _callEditCustomer() async {
    if (_formKey.currentState!.validate()) {
      ref.read(updateCustomerProvider.notifier).updateCustomer(
            widget.customer.copyWith(
              fullName: _fullNameController.text,
              mobile: _mobileController.text.arabicNumberConverter(),
              email: _emailController.text,
              address:
                  ref.read(selectedLocationProvider.notifier).state?.address,
              cityID: ref.read(selectedCityProvider)?.id,
              type: ref.read(selectedCustomerTypeProvider),
              isActive: ref.read(isActiveProvider.notifier).state,
              isEmailNotificationActive:
                  ref.read(isEmailNotificationActiveProvider.notifier).state,
              isSMSNotificationActive:
                  ref.read(isSMSNotificationActiveProvider.notifier).state,
            ),
          );
    }
  }
}
