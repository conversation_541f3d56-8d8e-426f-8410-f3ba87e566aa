import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/features/attachments/presentation/provider/attachments_provider.dart';
import 'package:mdd/features/dropdowns/presentation/providers/categories_provider.dart';
import 'package:mdd/features/dropdowns/presentation/providers/products_list_provider.dart';
import 'package:mdd/features/dropdowns/presentation/providers/projects_list_provider.dart';
import 'package:mdd/features/dropdowns/presentation/providers/units_list_provider.dart';
import 'package:mdd/features/orders/data/models/create_order_model.dart';
import 'package:mdd/features/orders/data/models/order_details_model.dart';
import 'package:mdd/features/orders/data/models/orders_model.dart';
import 'package:mdd/features/orders/presentation/provider/qty_order_controller_provider.dart';
import 'package:mdd/features/orders/presentation/provider/selected_order_type_provider.dart';
import 'package:mdd/features/user/domain/entities/user_entity.dart';
import 'package:mdd/features/user/presentation/provider/user_provider.dart';
import 'package:mdd/utils/enum_extensions.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/helper_functions.dart';

final addOrderProvider =
    ChangeNotifierProvider.autoDispose(AddOrderProvider.new);

class AddOrderProvider with ChangeNotifier {
  final ChangeNotifierProviderRef ref;
  AddOrderProvider(this.ref);

  final Map<String, OrderDetailsModel> _items = {};
  OrdersModel? _order;

  Map<String, OrderDetailsModel> get items => _items;
  int get totalProducts => _items.length;
  OrdersModel? get order => _order;

  CreateOrderModel getCreatedOrder() {
    return CreateOrderModel(
      projectID: ref.read(selectedProjectProvider)?.id ?? '',
      customerID: (ref.read(userProvider) as LoadedViewState<UserEntity>)
              .data
              .customer
              ?.customerID ??
          '',
      organizationID: (ref.read(userProvider) as LoadedViewState<UserEntity>)
              .data
              .customer
              ?.organizationID ??
          '',
      orderType: int.parse(ref.read(selectedOrderTypeProvider)!.orderTypeId),
      attachments: ref.read(attachmentsProvider.notifier).attachments,
      orderDetails: _items.values.toList(),
      description: ref.read(descriptionOrderProvider).text,
    );
  }

  OrdersModel? getOrder() {
    return _order?.copyWith(
      projectID: ref.read(selectedProjectProvider)?.id ?? '',
      customerID: (ref.read(userProvider) as LoadedViewState<UserEntity>)
              .data
              .customer
              ?.customerID ??
          '',
      organizationID: (ref.read(userProvider) as LoadedViewState<UserEntity>)
              .data
              .customer
              ?.organizationID ??
          '',
      orderType: ref.read(selectedOrderTypeProvider),
      attachments: ref.read(attachmentsProvider.notifier).attachments,
      details: _items.values.toList(),
      description: ref.read(descriptionOrderProvider).text,
    );
  }

  void resetItems() {
    ref.read(selectedProductProvider.notifier).state = null;
    ref.read(selectedCategoryProvider.notifier).state = null;
    ref.read(selectedUnitProvider.notifier).state = null;
    ref.read(qtyOrderControllerProvider).clear();
  }

  void addItem() {
    final item = OrderDetailsModel(
      sNo: totalProducts + 1,
      productID: ref.read(selectedProductProvider)?.id,
      productName: ref.read(selectedProductProvider)?.textAr,
      categoryID: ref.read(selectedCategoryProvider)?.id,
      categoryName: ref.read(selectedCategoryProvider)?.textAr,
      unitID: ref.read(selectedUnitProvider)?.id,
      unitName: ref.read(selectedUnitProvider)?.textAr,
      qty: ref.read(qtyOrderControllerProvider).text.isNotEmpty
          ? int.tryParse(
              ref.read(qtyOrderControllerProvider).text.arabicNumberConverter())
          : 1,
    );
    final currentKey = HelperFunctions.generateId();

    if (_items.containsKey(currentKey)) {
      _items.update(
        currentKey,
        (existingItem) => existingItem.copyWith(
            qty: (existingItem.qty ?? 0) + (item.qty ?? 0)),
      );
    } else {
      _items.putIfAbsent(currentKey, () => item);
    }
    resetItems();
    notifyListeners();
  }

  void setDraftOrder(OrdersModel order) {
    _order = order;

    _addAllItems(order.details ?? []);
    ref.read(attachmentsProvider.notifier).attachments =
        order.attachments ?? [];

    ref.read(descriptionOrderProvider).text = order.cancelNote ?? '';
  }

  void _addAllItems(List<OrderDetailsModel> items) {
    {
      for (var item in items) {
        final currentKey = HelperFunctions.generateId();

        if (_items.containsKey(currentKey)) {
          _items.update(
            currentKey,
            (existingItem) => existingItem.copyWith(
                qty: (existingItem.qty ?? 0) + (item.qty ?? 0)),
          );
        } else {
          _items.putIfAbsent(currentKey, () => item);
        }
      }
      notifyListeners();
    }
  }

  void removeItem({
    required String key,
    required BuildContext context,
  }) {
    _items.remove(key);
    context.maybePop();
    notifyListeners();
  }

  void clearAll() {
    _items.clear();
    ref.read(attachmentsProvider).clearAll();
    resetItems();
  }
}
