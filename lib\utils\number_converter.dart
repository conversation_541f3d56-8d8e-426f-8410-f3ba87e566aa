//
// String arabicNumberConverter(String input) {
//
//   final output = StringBuffer();
//
//   for (final rune in input.runes) {
//     final char = String.fromCharCode(rune);
//     if (english.contains(char) || char == '.') {
//       output.write(char);
//       continue;
//     }
//     final newNumber = arabic.indexOf(char);
//     output.write(newNumber.toString());
//   }
//   return output.toString();
// }
