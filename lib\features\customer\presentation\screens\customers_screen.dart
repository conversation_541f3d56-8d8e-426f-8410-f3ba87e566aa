import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/core/widgets/search_bar_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/customer/presentation/provider/customers_provider.dart';
import 'package:mdd/features/customer/presentation/screens/customers_view.dart';
import 'package:mdd/features/projects/presentation/provider/customer_text_field_provider.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

@RoutePage()
class CustomersScreen extends ConsumerStatefulWidget {
  const CustomersScreen({
    super.key,
  });

  @override
  ConsumerState createState() => _CustomersScreenState();
}

class _CustomersScreenState extends ConsumerState<CustomersScreen> {
  @override
  void initState() {
    super.initState();
    UiHelper.postBuildCallback((p0) {
      _fetch();
    });
  }

  void _fetch() {
    ref.read(customersProvider.notifier).fetchCustomers();
  }

  @override
  Widget build(BuildContext context) {
    final customerSearchController =
        ref.watch(customerSearchControllerProvider);
    final customerProvider = ref.read(customersProvider.notifier);
    return Scaffold(
      appBar: const MainAppBar(),
      floatingActionButton: FloatingActionButton.extended(
        heroTag: 'floating_action_customers_screen',
        onPressed: () {
          context.pushRoute(const AddCustomerRoute());
        },
        label: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.kSizeSmall,
          ),
          child: TextWidget(
            '+ ${'add_customer'.tr()}',
            color: AppColors.white,
          ),
        ),
      ),
      body: Column(
        children: [
          DetailsCustomBar(title: 'customers'.tr()),
          SearchBarWidget(
            keyboardType: TextInputType.text,
            showClearIcon: ref.watch(customerSuffixIconProvider),
            onPressedSearch: () {
              if (customerSearchController.text.isNotEmpty) {
                customerProvider.resetPagination();
                _fetch();
              }
            },
            onPressedClear: () {
              ref.read(customerSuffixIconProvider.state).state = false;
              customerSearchController.clear();
              customerProvider.resetPagination();
              _fetch();
            },
            textController: customerSearchController,
            onChange: (String value) {
              if (value.isEmpty) {
                ref.read(customerSuffixIconProvider.state).state = false;
                customerProvider.resetPagination();
                _fetch();
                return;
              }
              ref.read(customerSuffixIconProvider.state).state = true;
            },
          ),
          const Expanded(child: CustomersView()),
        ],
      ),
    );
  }
}
