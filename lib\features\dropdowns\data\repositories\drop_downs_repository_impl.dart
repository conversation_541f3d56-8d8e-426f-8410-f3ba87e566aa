import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/exceptions/exceptions.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/dropdowns/data/datasources/drop_downs_remote_data_source.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/domain/repositories/drop_downs_repository.dart';
import 'package:mdd/features/dropdowns/domain/usecases/customers_list.dart';
import 'package:mdd/features/dropdowns/domain/usecases/products_list.dart';
import 'package:mdd/features/dropdowns/domain/usecases/projects_list.dart';

final dropDownsRepositoryImpl = Provider<DropDownsRepositoryImpl>(
  (ref) => DropDownsRepositoryImpl(
    ref.watch(dropDownsRemoteDataSourceImpl),
  ),
);

class DropDownsRepositoryImpl implements DropDownsRepository {
  final DropDownsRemoteDataSource _dropDownsRemoteDataSource;

  DropDownsRepositoryImpl(this._dropDownsRemoteDataSource);

  @override
  Future<Either<Failure, List<DropDownEntity>>> customersList(
      CustomersListParams params) async {
    try {
      final res = await _dropDownsRemoteDataSource.customersList(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException catch (e) {
      return Left(InternalServerErrorFailure(message: e.error?.toString()));
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.error?.toString()));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException catch (e) {
      return Left(OtherFailure(message: e.error?.toString()));
    }
  }

  @override
  Future<Either<Failure, List<DropDownEntity>>> citiesList(
    NoParams params,
  ) async {
    try {
      final response = await _dropDownsRemoteDataSource.citiesList(params);
      return Right(response);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException catch (e) {
      return Left(InternalServerErrorFailure(message: e.error?.toString()));
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.error?.toString()));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException catch (e) {
      return Left(OtherFailure(message: e.error?.toString()));
    }
  }

  @override
  Future<Either<Failure, List<DropDownEntity>>> banksList(
      NoParams params) async {
    try {
      final response = await _dropDownsRemoteDataSource.banksList(params);
      return Right(response);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException catch (e) {
      return Left(InternalServerErrorFailure(message: e.error?.toString()));
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.error?.toString()));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException catch (e) {
      return Left(OtherFailure(message: e.error?.toString()));
    }
  }

  @override
  Future<Either<Failure, List<DropDownEntity>>> projectsList(
      ProjectsListParams params) async {
    try {
      final res = await _dropDownsRemoteDataSource.projectsList(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException catch (e) {
      return Left(InternalServerErrorFailure(message: e.error?.toString()));
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.error?.toString()));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException catch (e) {
      return Left(OtherFailure(message: e.error?.toString()));
    }
  }

  @override
  Future<Either<Failure, List<DropDownEntity>>> rejectReasonsList(
      NoParams params) async {
    try {
      final res = await _dropDownsRemoteDataSource.rejectReasonsList(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException catch (e) {
      return Left(InternalServerErrorFailure(message: e.error?.toString()));
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.error?.toString()));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException catch (e) {
      return Left(OtherFailure(message: e.error?.toString()));
    }
  }

  @override
  Future<Either<Failure, List<DropDownEntity>>> unitsList(
      NoParams params) async {
    try {
      final res = await _dropDownsRemoteDataSource.unitsList(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException catch (e) {
      return Left(InternalServerErrorFailure(message: e.error?.toString()));
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.error?.toString()));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException catch (e) {
      return Left(OtherFailure(message: e.error?.toString()));
    }
  }

  @override
  Future<Either<Failure, List<DropDownEntity>>> categoriesList(
      NoParams params) async {
    try {
      final res = await _dropDownsRemoteDataSource.categoriesList(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException catch (e) {
      return Left(InternalServerErrorFailure(message: e.error?.toString()));
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.error?.toString()));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException catch (e) {
      return Left(OtherFailure(message: e.error?.toString()));
    }
  }

  @override
  Future<Either<Failure, List<DropDownEntity>>> productsList(
      ProductsListParams params) async {
    try {
      final res = await _dropDownsRemoteDataSource.productsList(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException catch (e) {
      return Left(InternalServerErrorFailure(message: e.error?.toString()));
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.error?.toString()));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException catch (e) {
      return Left(OtherFailure(message: e.error?.toString()));
    }
  }
}
