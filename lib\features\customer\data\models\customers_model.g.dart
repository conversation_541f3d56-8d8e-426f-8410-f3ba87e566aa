// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customers_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomersModel _$CustomersModelFromJson(Map<String, dynamic> json) =>
    CustomersModel(
          customerID: json['customerID'] as String?,
          fullName: json['fullName'] as String?,
          mobile: json['mobile'] as String?,
          email: json['email'] as String?,
          userName: json['userName'] as String?,
          address: json['address'] as String?,
          password: json['password'] as String?,
          language: json['language'] as String?,
          organizationID: json['organizationID'] as String?,
          organizationName: json['organizationName'] as String?,
          cityID: json['cityID'] as String?,
          cityName: json['cityName'] as String?,
          jobtitleID: json['jobtitleID'] as String?,
          jobtitleName: json['jobtitleName'] as String?,
          departmentID: json['departmentID'] as String?,
          departmentName: json['departmentName'] as String?,
          isEmailConfrimd: json['isEmailConfrimd'] as bool?,
          isActive: json['isActive'] as bool?,
          isPasswordNeedToChange: json['isPasswordNeedToChange'] as bool?,
          lastPasswordChage: json['lastPasswordChage'] == null
              ? null
              : DateTime.parse(json['lastPasswordChage'] as String),
          isMfaEnabled: json['isMfaEnabled'] as bool?,
          mfaCode: json['mfaCode'] as String?,
          image: json['image'] as String?,
          imageFullPath: json['imageFullPath'] as String?,
          type: $enumDecodeNullable(_$CustomerTypeEnumMap, json['type']),
          typeName: json['typeName'] as String?,
          creditBalance: (json['creditBalance'] as num?)?.toDouble(),
          isCredit: json['isCredit'] as bool?,
          isSMSNotificationActive: json['isSMSNotificationActive'] as bool?,
          isEmailNotificationActive: json['isEmailNotificationActive'] as bool?,
    );

Map<String, dynamic> _$CustomersModelToJson(CustomersModel instance) =>
    <String, dynamic>{
          'customerID': instance.customerID,
          'fullName': instance.fullName,
          'mobile': instance.mobile,
          'email': instance.email,
          'userName': instance.userName,
          'address': instance.address,
          'password': instance.password,
          'language': instance.language,
          'organizationID': instance.organizationID,
          'organizationName': instance.organizationName,
          'cityID': instance.cityID,
          'cityName': instance.cityName,
          'jobtitleID': instance.jobtitleID,
          'jobtitleName': instance.jobtitleName,
          'departmentID': instance.departmentID,
          'departmentName': instance.departmentName,
          'isEmailConfrimd': instance.isEmailConfrimd,
          'isActive': instance.isActive,
          'isPasswordNeedToChange': instance.isPasswordNeedToChange,
          'lastPasswordChage': instance.lastPasswordChage?.toIso8601String(),
          'isMfaEnabled': instance.isMfaEnabled,
          'mfaCode': instance.mfaCode,
          'image': instance.image,
          'imageFullPath': instance.imageFullPath,
          'type': _$CustomerTypeEnumMap[instance.type],
          'typeName': instance.typeName,
          'creditBalance': instance.creditBalance,
          'isCredit': instance.isCredit,
          'isSMSNotificationActive': instance.isSMSNotificationActive,
          'isEmailNotificationActive': instance.isEmailNotificationActive,
    };

const _$CustomerTypeEnumMap = {
      CustomerType.non: 0,
      CustomerType.demander: 1,
      CustomerType.authorizer: 2,
      CustomerType.fullAuthority: 3,
      CustomerType.receiver: 4,
};