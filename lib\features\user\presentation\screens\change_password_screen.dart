import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/repositories/token_repository.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/form_field_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/core/widgets/title_widget.dart';
import 'package:mdd/features/user/presentation/provider/user_password_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/constants/storage_keys_constants.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class ChangePasswordScreen extends ConsumerStatefulWidget {
  const ChangePasswordScreen({
    super.key,
  });

  @override
  ConsumerState createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends ConsumerState<ChangePasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _currentPassword = TextEditingController();
  final _newPassword = TextEditingController();
  final _confirmNewPassword = TextEditingController();
  String? _savedPassword;
  @override
  void initState() {
    _readUserData();
    super.initState();
  }

  _readUserData() async {
    final res = await ref.read(tokenRepositoryProvider).readUserAccess();
    setState(() {
      _savedPassword = res?[kPassword];
    });
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(updateUserPasswordProvider, (previous, state) {
      if (state is LoadingViewState) {
        UiHelper.showLoadingDialog(context);
      }

      if (state is LoadedViewState<bool>) {
        Navigator.pop(context);
        Navigator.pop(context);
        ref
            .read(tokenRepositoryProvider)
            .updatePassword(_confirmNewPassword.text);
        UiHelper.showNotification('change_password_success'.tr(),
            notificationType: NotificationType.success);
      }
      if (state is ErrorViewState) {
        context.maybePop();
        UiHelper.showNotification(
          'change_password_failure'.tr(),
        );
      }
    });

    return SingleChildScrollView(
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Card(
              elevation: 0,
              color: AppColors.cardDetailsBackground,
              margin: const EdgeInsets.all(AppDimensions.kSizeLarge),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                AppDimensions.kDefaultRadius,
              )),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: AppDimensions.kSizeXXLarge,
                  horizontal: AppDimensions.kSizeXXLarge,
                ),
                child: Image.asset(
                  'assets/images/locker_icon.png',
                  height: context.widthR(0.3),
                ),
              ),
            ),
            TitleWidget(
              'change_password'.tr(),
              color: AppColors.disabledColor1,
              fontSize: AppDimensions.kSizeLarge2,
              fontWeight: FontWeight.bold,
            ),
            const SizedBox(
              height: AppDimensions.kSizeLarge,
            ),
            FormFieldWidget(
              controller: _currentPassword,
              autoValidateMode: AutovalidateMode.disabled,
              fillColor: AppColors.white,
              borderColor: AppColors.cardDetailsBackground,
              hintText: 'current_password'.tr(),
              obscureText: true,
              validator: (value) {
                if (value == _savedPassword) {
                  return null;
                }
                return 'current_password_validate'.tr();
              },
            ),
            FormFieldWidget(
              controller: _newPassword,
              autoValidateMode: AutovalidateMode.disabled,
              fillColor: AppColors.white,
              borderColor: AppColors.cardDetailsBackground,
              hintText: 'new_password'.tr(),
              obscureText: true,
              validator: (value) {
                if (_confirmNewPassword.text != value) {
                  return 'password_not_match'.tr();
                }
                if (value != null && value.length >= 6) {
                  return null;
                }

                return 'password_validate'.tr();
              },
            ),
            FormFieldWidget(
              controller: _confirmNewPassword,
              autoValidateMode: AutovalidateMode.disabled,
              fillColor: AppColors.white,
              borderColor: AppColors.cardDetailsBackground,
              hintText: 'confirm_new_password'.tr(),
              obscureText: true,
              validator: (value) {
                if (value != _newPassword.text) {
                  return 'password_not_match'.tr();
                }
                if (value != null && value.length >= 6) {
                  return null;
                }

                return 'password_validate'.tr();
              },
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.kSizeLarge,
                vertical: AppDimensions.kSizeLarge,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                      onPressed: () => context.maybePop(),
                      child: TextWidget('cancel'.tr())),
                  ButtonWidget(
                    onPressed: _callChangePassword,
                    title: 'confirm'.tr(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _callChangePassword() async {
    if (_formKey.currentState!.validate()) {
      await ref.read(updateUserPasswordProvider.notifier).updatePassword(
            oldPassword: _currentPassword.text,
            newPassword: _newPassword.text,
            confirmPassword: _confirmNewPassword.text,
          );
    }
  }
}
