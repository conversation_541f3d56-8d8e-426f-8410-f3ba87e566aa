import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:mdd/core/widgets/description_widget/card_item_widget.dart';
import 'package:mdd/core/widgets/description_widget/description_card_item_widget.dart';
import 'package:mdd/core/widgets/order_details_item_widget.dart';
import 'package:mdd/features/orders/domain/entities/orders_entity.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enum_extensions.dart';
import 'package:mdd/utils/helper_functions.dart';

class OrderDetailsTapView extends StatelessWidget {
  final OrdersEntity order;
  const OrderDetailsTapView({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: ListView(
        children: [
          CardItemsWidget(
            title: order.projectName ?? '',
            subtitle: order.orderNo?.toString() ?? '',
            image: 'assets/images/orders_image.png',
            imageBackground: AppColors.brownCard,
            statusName: order.orderStatus?.translatedName ?? '',
            statusColor: order.orderStatus?.statusColor,
            descriptionWidget: [
              DescriptionCardItemWidget(
                title: 'order_by'.tr(),
                trailing: order.customerName ?? '',
              ),
              DescriptionCardItemWidget(
                title: 'order_date'.tr(),
                trailing: order.createdOn != null
                    ? HelperFunctions.formatDate(order.createdOn!)
                    : '',
              ),
              DescriptionCardItemWidget(
                title: 'order_type'.tr(),
                trailing: order.orderTypeName ?? '',
              ),
              DescriptionCardItemWidget(
                title: 'employee_name'.tr(),
                trailing: order.employeeName ?? '',
              ),
            ],
          ),
          ListView.builder(
            itemCount: order.details?.length,
            shrinkWrap: true,
            primary: false,
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.kSizeMedium,
            ),
            itemBuilder: (context, index) {
              return OrderDetailsItemWidget(
                productName: order.details?[index].productName ?? '',
                categoryName: order.details?[index].categoryName ?? '',
                unitName: order.details?[index].unitName ?? '',
                qty: order.details?[index].qty.toString() ?? '0',
              );
            },
          ),
        ],
      ),
    );
  }
}
