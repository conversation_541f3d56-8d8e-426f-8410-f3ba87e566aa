import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/orders/domain/usecases/approve_order.dart';

final approveOrderProvider =
    StateNotifierProvider.autoDispose<ApproveOrderProvider, ViewState>(
        (ref) => ApproveOrderProvider(ref.watch(approveOrderUseCaseProvider)));

class ApproveOrderProvider extends BaseProvider<ResultModel> {
  final ApproveOrder _approveOrder;
  ApproveOrderProvider(this._approveOrder);

  Future<void> approveOrder(ApproveOrderParams params) async {
    setLoadingState();
    final response = await _approveOrder.call(params);
    response.fold(
      (failure) {
        setErrorState(failure.message);
      },
      setLoadedState,
    );
  }
}
