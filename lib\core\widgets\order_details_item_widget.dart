import 'package:flutter/material.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';

class OrderDetailsItemWidget extends StatelessWidget {
  final String productName;
  final String categoryName;
  final String unitName;
  final String qty;
  final void Function()? onPressed;

  const OrderDetailsItemWidget({
    super.key,
    required this.productName,
    required this.categoryName,
    required this.unitName,
    required this.qty,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      color: AppColors.cardDetailsBackground,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.kSizeMedium,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(
              height: AppDimensions.kSizeMedium,
            ),
            Row(
              children: [
                Expanded(
                  child: TextWidget(
                    productName,
                    color: AppColors.tabBackgroundColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (onPressed != null)
                  IconButton(
                    icon: const Icon(Icons.delete_outlined),
                    onPressed: onPressed,
                  ),
              ],
            ),
            const SizedBox(
              height: AppDimensions.kSizeMedium,
            ),
            TextWidget(
              categoryName,
              color: AppColors.grey2,
            ),
            const SizedBox(
              height: AppDimensions.kSizeMedium,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Card(
                  color: AppColors.purple,
                  margin: EdgeInsets.zero,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: AppDimensions.kSizeSmall,
                      horizontal: AppDimensions.kSizeMedium,
                    ),
                    child: TextWidget(
                      unitName,
                      color: AppColors.white,
                    ),
                  ),
                ),
                Card(
                  color: AppColors.tabBackgroundColor,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.kSizeSmall,
                      vertical: AppDimensions.kSizeXSmall / 2,
                    ),
                    child: TextWidget(
                      qty,
                      color: AppColors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: AppDimensions.kSizeMedium,
            ),
          ],
        ),
      ),
    );
  }
}
