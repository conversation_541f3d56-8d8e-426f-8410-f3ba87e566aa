import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/customer/data/models/customers_model.dart';
import 'package:mdd/features/user/data/repositories/user_repository_impl.dart';
import 'package:mdd/features/user/domain/repositories/user_repository.dart';

final profileUserCaseProvider =
    Provider<Profile>((ref) => Profile(ref.watch(userRepositoryImpl)));

class Profile implements UseCase<CustomersModel, NoParams> {
  final UserRepository userRepository;

  Profile(this.userRepository);

  @override
  Future<Either<Failure, CustomersModel>> call(NoParams params) async {
    return await userRepository.getProfile(params);
  }
}
