import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';

part 'drop_down_model.g.dart';

@JsonSerializable()
class DropDownModel extends DropDownEntity {
  DropDownModel({
    required super.id,
    required super.textAr,
    required super.textEn,
  });

  factory DropDownModel.fromJson(Map<String, dynamic> json) =>
      _$DropDownModelFromJson(json);

  Map<String, dynamic> toJson() => _$DropDownModelToJson(this);
}
