import 'package:auto_route/annotations.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/search_bar_widget.dart';
import 'package:mdd/core/widgets/tap_menu_widget.dart';
import 'package:mdd/features/invoices/presentation/provider/invoice_text_field_provider.dart';
import 'package:mdd/features/invoices/presentation/provider/invoices_provider.dart';
import 'package:mdd/features/invoices/presentation/screens/invoices_view.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enum_extensions.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/extensions.dart';

@RoutePage()
class InvoicesScreen extends ConsumerStatefulWidget {
  const InvoicesScreen({
    super.key,
  });

  @override
  ConsumerState createState() => _InvoicesScreenState();
}

class _InvoicesScreenState extends ConsumerState<InvoicesScreen> {
  void _fetch() {
    ref.read(invoicesProvider.notifier).fetchInvoices();
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final invoiceStatusState = ref.watch(invoiceStatusProvider.state);
    final invoiceSearchController = ref.watch(invoiceSearchControllerProvider);
    return Column(
      children: [
        const SizedBox(
          height: AppDimensions.kSizeLarge,
        ),
        SearchBarWidget(
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          keyboardType: TextInputType.number,
          showClearIcon: ref.watch(invoiceSuffixIconProvider),
          onPressedSearch: () {
            if (invoiceSearchController.text.isNotEmpty) {
              ref.read(invoicesProvider.notifier).resetPagination();
              _fetch();
            }
          },
          onPressedClear: () {
            ref.read(invoiceSuffixIconProvider.state).state = false;
            invoiceSearchController.clear();
            ref.read(invoicesProvider.notifier).resetPagination();
            _fetch();
          },
          textController: invoiceSearchController,
          onChange: (String value) {
            if (value.isEmpty) {
              ref.read(invoiceSuffixIconProvider.state).state = false;
              ref.read(invoicesProvider.notifier).resetPagination();
              _fetch();
              return;
            }
            ref.read(invoiceSuffixIconProvider.state).state = true;
          },
        ),
        SizedBox(
          height: context.heightR(.05),
          child: ListView(
            padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.kSizeMedium),
            scrollDirection: Axis.horizontal,
            children: [
              TapMenuWidget(
                isActive: invoiceStatusState.state == null,
                tabTitle: 'all_status'.tr(),
                onTap: () {
                  if (invoiceStatusState.state != null) {
                    invoiceStatusState.state = null;
                    _fetch();
                  }
                },
              ),
              TapMenuWidget(
                isActive: invoiceStatusState.state == 1,
                tabTitle: InvoicesStatusType.unpaid.translatedName,
                onTap: () {
                  if (invoiceStatusState.state != 1) {
                    invoiceStatusState.state = 1;
                    _fetch();
                  }
                },
              ),
              TapMenuWidget(
                isActive: invoiceStatusState.state == 2,
                tabTitle: InvoicesStatusType.overDuo.translatedName,
                onTap: () {
                  if (invoiceStatusState.state != 2) {
                    invoiceStatusState.state = 2;
                    _fetch();
                  }
                },
              ),
              TapMenuWidget(
                isActive: invoiceStatusState.state == 3,
                tabTitle: InvoicesStatusType.paid.translatedName,
                onTap: () {
                  if (invoiceStatusState.state != 3) {
                    invoiceStatusState.state = 3;
                    _fetch();
                  }
                },
              ),
              TapMenuWidget(
                isActive: invoiceStatusState.state == 4,
                tabTitle: InvoicesStatusType.paidPartial.translatedName,
                onTap: () {
                  if (invoiceStatusState.state != 4) {
                    invoiceStatusState.state = 4;
                    _fetch();
                  }
                },
              ),     TapMenuWidget(
                isActive: invoiceStatusState.state == 5,
                tabTitle: InvoicesStatusType.canceled.translatedName,
                onTap: () {
                  if (invoiceStatusState.state != 5) {
                    invoiceStatusState.state = 5;
                    _fetch();
                  }
                },
              ),  TapMenuWidget(
                isActive: invoiceStatusState.state == 6,
                tabTitle: InvoicesStatusType.creditNote.translatedName,
                onTap: () {
                  if (invoiceStatusState.state != 6) {
                    invoiceStatusState.state = 6;
                    _fetch();
                  }
                },
              ),
            ],
          ),
        ),
        const Expanded(child: InvoicesView()),
      ],
    );
  }
}
