import 'package:dartz/dartz.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/exceptions/exceptions.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/customer/data/models/customers_model.dart';
import 'package:mdd/features/user/data/datasources/user_local_data_source.dart';
import 'package:mdd/features/user/data/datasources/user_remote_data_source.dart';
import 'package:mdd/features/user/domain/entities/user_entity.dart';
import 'package:mdd/features/user/domain/repositories/user_repository.dart';
import 'package:mdd/features/user/domain/usecases/notification_token.dart';
import 'package:mdd/features/user/domain/usecases/update_password.dart';
import 'package:mdd/features/user/domain/usecases/update_profile.dart';

final userRepositoryImpl = Provider<UserRepositoryImpl>(
  (ref) => UserRepositoryImpl(
    ref.watch(userRemoteDataSourceImpl),
    ref.watch(userLocalDataSourceImpl),
  ),
);

class UserRepositoryImpl implements UserRepository {
  final UserRemoteDataSource userRemoteDataSource;
  final UserLocalDataSource userLocalDataSource;
  UserRepositoryImpl(this.userRemoteDataSource, this.userLocalDataSource);
  @override
  Future<Either<Failure, UserEntity>> login(
      String userName, String password) async {
    try {
      final userResponse = await userRemoteDataSource.login(userName, password);
      userLocalDataSource.cashUserData(userResponse!, password);
      return Right(userResponse);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(
        InternalServerErrorFailure(message: "الخدمة غير متاحة حالياً"),
      );
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException {
      return Left(NotFoundFailure(message: 'incorrect_user_or_password'.tr()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }

  @override
  Future<Either<Failure, CustomersModel>> getProfile(NoParams params) async {
    try {
      final userResponse = await userRemoteDataSource.getProfile(params);
      return Right(userResponse!);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }

  @override
  Future<Either<Failure, ResultModel>> updateProfile(
      UpdateProfileParams params) async {
    try {
      final res = await userRemoteDataSource.update(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }

  @override
  Future<Either<Failure, ResultModel>> updatePassword(
      UpdatePasswordParams params) async {
    try {
      final response = await userRemoteDataSource.updatePassword(params);
      return Right(response);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException {
      return const Left(OtherFailure());
    }
  }

  @override
  Future<Either<Failure, ResultModel>> addNotificationToken(
      NotificationTokenParams params) async {
    try {
      final response = await userRemoteDataSource.addNotificationToken(params);
      return Right(response);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure(
          message: 'notification token already registered'));
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on OtherException catch (e) {
      return Left(OtherFailure(message: e.message));
    }
  }

  @override
  Future<Either<Failure, ResultModel>> deleteNotificationToken(
      String notificationToken) async {
    try {
      final res =
          await userRemoteDataSource.deleteNotificationToken(notificationToken);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException {
      return const Left(InternalServerErrorFailure());
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException {
      return const Left(UnauthorizedFailure());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on OtherException catch (e) {
      return Left(OtherFailure(message: e.message));
    }
  }
}
