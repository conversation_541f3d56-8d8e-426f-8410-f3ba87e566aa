import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/core/widgets/subtitle_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/core/widgets/timeago_widget.dart';
import 'package:mdd/features/notifications/domain/entities/notifications_entity.dart';
import 'package:mdd/features/notifications/presentation/provider/notifications_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class NotificationsView extends ConsumerStatefulWidget {
  const NotificationsView({
    super.key,
  });

  @override
  ConsumerState createState() => _NotificationsViewState();
}

class _NotificationsViewState extends ConsumerState<NotificationsView> {
  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      _fetch();
    });
    super.initState();
  }

  _fetch() {
    ref.read(notificationsProvider.notifier).getNotifications();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(notificationsProvider);

    if (state is LoadingViewState) {
      return const Center(child: LoaderWidget());
    }
    if (state is EmptyViewState) {
      return Center(
        child: TextWidget('empty_notifications'.tr()),
      );
    }
    if (state is LoadedViewState<List<NotificationsEntity>>) {
      return Expanded(
        child: Scrollbar(
          child: ListView.builder(
            itemCount: state.data.length,
            padding:
                const EdgeInsets.symmetric(vertical: AppDimensions.kSizeSmall),
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.symmetric(

                  horizontal: AppDimensions.kSizeMedium,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Card(
                      color: AppColors.cardColor,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                              AppDimensions.kMediumRadius)),
                      elevation: 0,
                      shadowColor: AppColors.lightGrey.withOpacity(0.1),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: AppDimensions.kSizeMedium,
                          horizontal: AppDimensions.kSizeMedium,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(
                              state.data[index].title ?? '',
                              color: AppColors.primary,
                            ),
                            const SizedBox(
                              height: AppDimensions.kSizeSmall,
                            ),
                            SubtitleWidget(
                              state.data[index].content ?? '',
                            ),
                          ],
                        ),
                      ),
                    ),
                    if (state.data[index].createdOn != null)
                      TimeagoWidget(
                        createdOn: state.data[index].createdOn!,
                      ),
                  ],
                ),
              );
            },
          ),
        ),
      );
    }
    return const SizedBox();
  }
}
