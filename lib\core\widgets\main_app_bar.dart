import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/extensions.dart';

class MainAppBar extends ConsumerWidget implements PreferredSizeWidget {
  final bool showDrawer;
  final bool showProfile;
  const MainAppBar({super.key, this.showDrawer = false, this.showProfile = true});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppBar(
      backgroundColor: AppColors.primary,
      title: SvgPicture.asset('assets/icons/mdd-logo-white.svg'),
      leadingWidth: context.widthR(0.20),
      centerTitle: true,
      leading: Visibility(
        visible: showDrawer,
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.kSizeMedium,
          ),
          child: Consumer(builder: (context, ref, child) {
            return CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () => Scaffold.of(context).openDrawer(),
              child: Card(
                elevation: 5,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5),
                ),
                color: AppColors.primary,
                child: Padding(
                  padding: const EdgeInsets.all(AppDimensions.kSizeSmall),
                  child: SvgPicture.asset('assets/icons/menu_icon.svg'),
                ),
              ),
            );
          }),
        ),
      ),
      actions: [
        Visibility(
          visible: showProfile,
          child: InkWell(
            onTap: () => context.pushRoute(const UserProfileRoute()),
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.kSizeSmall,
                vertical: AppDimensions.kSizeXSmall,
              ),
              child: SvgPicture.asset('assets/icons/avatar_icon.svg'),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(AppBar().preferredSize.height);
}
