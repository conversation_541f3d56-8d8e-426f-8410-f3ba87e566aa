import 'package:mdd/core/models/paginated_model.dart';
import 'package:mdd/features/quotations/data/models/quotation_details_model.dart';
import 'package:mdd/utils/enums.dart';

class QuotationsEntity implements PaginatedModel {
  final String quotationID;
  final String orderID;
  final double total;
  final double price;
  final double? vat;
  final double? vatPer;
  final double? serviceFees;
  double? serviceFeesCalculated;
  final double? serviceFeesDiscount;
  final double? additionalFees;
  final double? deliveryFees;
  final String? quotationNo;
  final int? orderNo;
  final QuotationsStatusType? quotationStatus;
  final String? expiredDate;
  final String? submitDate;
  final String? organizationID;
  final int? orderType;
  final List<QuotationDetailsModel>? items;
  final String? conditions;
  final int? deliveryInDays;

  QuotationsEntity({
    required this.quotationID,
    required this.quotationNo,
    required this.orderID,
    this.orderNo,
    this.expiredDate,
    this.submitDate,
    required this.price,
    this.vat,
    this.vatPer,
    this.serviceFees,
    this.serviceFeesCalculated,
    this.serviceFeesDiscount,
    this.additionalFees,
    this.deliveryFees,
    required this.total,
    this.conditions,
    this.quotationStatus,
    this.organizationID,
    this.orderType,
    this.items,
    this.deliveryInDays,
  });
}
