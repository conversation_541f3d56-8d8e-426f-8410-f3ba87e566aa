// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quotation_statuses_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuotationStatusesModel _$QuotationStatusesModelFromJson(
        Map<String, dynamic> json) =>
    QuotationStatusesModel(
      status: $enumDecode(_$QuotationsStatusTypeEnumMap, json['status']),
      statusName: json['statusName'] as String,
      quotationsCount: json['quotationsCount'] as int,
      quotationsAmount: (json['quotationsAmount'] as num).toDouble(),
    );

Map<String, dynamic> _$QuotationStatusesModelToJson(
        QuotationStatusesModel instance) =>
    <String, dynamic>{
      'status': _$QuotationsStatusTypeEnumMap[instance.status]!,
      'statusName': instance.statusName,
      'quotationsCount': instance.quotationsCount,
      'quotationsAmount': instance.quotationsAmount,
    };

const _$QuotationsStatusTypeEnumMap = {
  QuotationsStatusType.created: 0,
  QuotationsStatusType.submitted: 1,
  QuotationsStatusType.accepted: 2,
  QuotationsStatusType.rejected: 3,
  QuotationsStatusType.expired: 4,
};
