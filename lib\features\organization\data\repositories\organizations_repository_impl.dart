import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/exceptions/exceptions.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/organization/data/datasources/organization_remote_data_source.dart';
import 'package:mdd/features/organization/data/models/organizations_model.dart';
import 'package:mdd/features/organization/domain/repositories/organizations_repository.dart';
import 'package:mdd/features/organization/domain/usecases/update_organization.dart';

final orgsRepositoryImpl = Provider<OrgsRepositoryImpl>(
  (ref) => OrgsRepositoryImpl(
    ref.watch(orgsRemoteDataSourceImpl),
  ),
);

class OrgsRepositoryImpl implements OrgsRepository {
  final OrgsRemoteDataSource _orgsRemoteDataSource;
  OrgsRepositoryImpl(this._orgsRemoteDataSource);

  @override
  Future<Either<Failure, OrgsModel>> orgDetails(NoParams params) async {
    try {
      final response = await _orgsRemoteDataSource.orgDetails(params);
      return Right(response);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException catch (e) {
      return Left(InternalServerErrorFailure(message: e.error?.toString()));
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.error?.toString()));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException catch (e) {
      return Left(OtherFailure(message: e.error?.toString()));
    }
  }

  @override
  Future<Either<Failure, ResultModel>> update(UpdateOrgParams params) async {
    try {
      final res = await _orgsRemoteDataSource.update(params);
      return Right(res);
    } on BadRequestException catch (e) {
      return Left(BadRequestFailure(message: e.error?.toString()));
    } on InternalServerErrorException catch (e) {
      return Left(InternalServerErrorFailure(message: e.error?.toString()));
    } on ConflictException catch (e) {
      return Left(ConflictFailure(message: e.error?.toString()));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.error?.toString()));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.error?.toString()));
    } on DeadlineExceededException {
      return const Left(DeadlineExceededFailure());
    } on InvalidAccessTokenException {
      return const Left(InvalidAccessTokenFailure());
    } on OtherException catch (e) {
      return Left(OtherFailure(message: e.error?.toString()));
    }
  }
}
