import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/description_widget/card_item_widget.dart';
import 'package:mdd/core/widgets/description_widget/description_card_item_widget.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/features/payments/domain/entities/payments_entity.dart';
import 'package:mdd/features/payments/presentation/provider/payments_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/utils/constants/constants.dart';
import 'package:mdd/utils/constants/keys.dart';
import 'package:mdd/utils/enum_extensions.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/helper_functions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class PaymentsView extends ConsumerStatefulWidget {
  const PaymentsView({
    super.key,
  });

  @override
  ConsumerState createState() => _PaymentsViewState();
}

class _PaymentsViewState extends ConsumerState<PaymentsView> {
  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      _fetch();
    });
    super.initState();
  }

  void _fetch() {
    ref.read(paymentsProvider.notifier).fetchPayments();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      final state = ref.watch(paymentsProvider);

      if (state is LoadingViewState) {
        return const LoaderWidget();
      }
      if (state is ErrorViewState) {
        return Text(state.errorMessage);
      }
      if (state is LoadedViewState<List<PaymentsEntity>>) {
        final provider = ref.read(paymentsProvider.notifier);
        return ListView.builder(
            itemCount: state.data.length + 1,
            key: const PageStorageKey(AppKeys.paymentsListKey),
            itemBuilder: (BuildContext context, int index) {
              if (index == state.data.length) {
                if (provider.hasMoreData &&
                    state.data.length >= AppConstants.paginationLimit) {
                  provider.loadMore();
                  return const Padding(
                    padding: EdgeInsets.all(10),
                    child: SizedBox(
                      child: LoaderWidget(),
                    ),
                  );
                } else {
                  return const SizedBox();
                }
              }
              return CardItemsWidget(
                title:
                    state.data[index].paidAmount.toString().groupingSeparator(),
                subtitle: state.data[index].orderNo?.toString() ?? '',
                image: 'assets/images/payments_image.png',
                imageBackground: AppColors.primary,
                statusName: state.data[index].status.translatedName,
                statusColor: state.data[index].status.statusColor,
                descriptionWidget: [
                  DescriptionCardItemWidget(
                    title: 'transaction_number'.tr(),
                    trailing: state.data[index].transactionNo ?? '',
                  ),
                  DescriptionCardItemWidget(
                    title: 'bank_name'.tr(),
                    trailing: state.data[index].bankName ?? '',
                  ),
                  DescriptionCardItemWidget(
                    title: 'paid_date'.tr(),
                    trailing: state.data[index].paymentDate != null
                        ? HelperFunctions.formatDate(
                            state.data[index].paymentDate!)
                        : '',
                  ),
                  DescriptionCardItemWidget(
                    title: 'paid_amount'.tr(),
                    trailing: state.data[index].paidAmount.toFormattedPrice(),
                  ),
                  DescriptionCardItemWidget(
                    title: 'approved_amount'.tr(),
                    trailing:
                        state.data[index].approvedAmount.toFormattedPrice(),
                  ),
                ],
              );
            });
      }
      return const SizedBox();
    });
  }
}
