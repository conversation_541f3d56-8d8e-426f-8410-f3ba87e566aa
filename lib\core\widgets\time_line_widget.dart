import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mdd/core/widgets/subtitle_widget.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';

class TimeLineWidget extends StatelessWidget {
  final int activeIndex;
  const TimeLineWidget({super.key, required this.activeIndex});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: AppDimensions.kSizeXSmall),
      child: Row(
        children: [
          const CircleAvatar(
            backgroundColor: AppColors.purple,
            radius: AppDimensions.kSizeMedium,
            child: SubtitleWidget(
              '1',
              color: AppColors.white,
            ),
          ),
          const SizedBox(
            width: AppDimensions.kSizeSmall,
          ),
          SvgPicture.asset(
            'assets/icons/three_dots.svg',
            colorFilter: ColorFilter.mode(
              activeIndex == 2 ? AppColors.purple : AppColors.grey,
              BlendMode.srcIn,
            ),
          ),
          const SizedBox(
            width: AppDimensions.kSizeSmall,
          ),
          CircleAvatar(
            backgroundColor: activeIndex != 0
                ? AppColors.purple
                : AppColors.cardDetailsBackground,
            radius: AppDimensions.kSizeMedium,
            child: SubtitleWidget(
              '2',
              color:
                  activeIndex != 0 ? AppColors.white : AppColors.tabTextColor,
            ),
          ),
          const SizedBox(
            width: AppDimensions.kSizeSmall,
          ),
          SvgPicture.asset(
            'assets/icons/three_dots.svg',
            colorFilter: ColorFilter.mode(
              activeIndex == 2 ? AppColors.purple : AppColors.grey,
              BlendMode.srcIn,
            ),
          ),
          const SizedBox(
            width: AppDimensions.kSizeSmall,
          ),
          CircleAvatar(
            backgroundColor: activeIndex == 2
                ? AppColors.purple
                : AppColors.cardDetailsBackground,
            radius: AppDimensions.kSizeMedium,
            child: SubtitleWidget(
              '3',
              color:
                  activeIndex == 2 ? AppColors.white : AppColors.tabTextColor,
            ),
          ),
        ],
      ),
    );
  }
}
