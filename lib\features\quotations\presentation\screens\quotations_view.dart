import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/features/attachments/domain/entites/attachments_entity.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/presentation/provider/pdf_quotation_provider.dart';
import 'package:mdd/features/quotations/presentation/provider/quotations_provider.dart';
import 'package:mdd/features/quotations/presentation/widgets/quotation_item_widget.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/utils/constants/constants.dart';
import 'package:mdd/utils/constants/keys.dart';
import 'package:mdd/utils/files_handler.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class QuotationsView extends ConsumerStatefulWidget {
  const QuotationsView({super.key});

  @override
  ConsumerState createState() => _QuotationsViewState();
}

class _QuotationsViewState extends ConsumerState<QuotationsView> {
  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      _fetch();
    });
    super.initState();
  }

  void _fetch() {
    ref.read(quotationsProvider.notifier).fetchQuotations();
  }

  Future<void> _refresh() async {
    ref.read(quotationsProvider.notifier).resetState();
    await ref.read(quotationsProvider.notifier).fetchQuotations();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(pdfQuotationProvider, (previous, state) async {
      if (state is LoadingViewState) {
        UiHelper.showLoadingDialog(context);
      }
      if (state is LoadedViewState<AttachmentsEntity>) {
        Navigator.of(context).pop();
        await FilesHandler(state.data).openFile();
      } else if (state is ErrorViewState) {
        Navigator.pop(context);
        UiHelper.showNotification(state.errorMessage);
      }
    });
    return Consumer(builder: (context, ref, child) {
      final state = ref.watch(quotationsProvider);

      if (state is LoadingViewState) {
        return const LoaderWidget();
      }
      if (state is ErrorViewState) {
        return Text(state.errorMessage);
      }
      if (state is LoadedViewState<List<QuotationsModel>>) {
        final provider = ref.read(quotationsProvider.notifier);
        return RefreshIndicator(
          onRefresh: _refresh,
          child: ListView.builder(
            itemCount: state.data.length + 1,
            key: const PageStorageKey(AppKeys.quotationsListKey),
            itemBuilder: (BuildContext context, int index) {
              if (index == state.data.length) {
                if (provider.hasMoreData &&
                    state.data.length >= AppConstants.paginationLimit) {
                  provider.loadMore();
                  return const Padding(
                    padding: EdgeInsets.all(10),
                    child: SizedBox(
                      child: LoaderWidget(),
                    ),
                  );
                } else {
                  return const SizedBox();
                }
              }
              return InkWell(
                onTap: () => context.pushRoute(QuotationDetailsRoute(
                  quotationId: state.data[index].quotationID,
                )),
                child: QuotationItemWidget(
                  quotation: state.data[index],
                ),
              );
            },
          ),
        );
      }
      return const SizedBox();
    });
  }
}
