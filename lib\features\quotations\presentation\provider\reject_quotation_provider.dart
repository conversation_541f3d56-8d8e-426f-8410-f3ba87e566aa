import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/features/quotations/domain/usecases/reject_quotation.dart';

final rejectQuotationProvider =
    StateNotifierProvider.autoDispose<RejectQuotationProvider, ViewState>(
        (ref) => RejectQuotationProvider(
              ref.watch(rejectQuotationUseCaseProvider),
            ));

class RejectQuotationProvider extends BaseProvider<ResultModel> {
  final RejectQuotation _rejectQuotation;
  RejectQuotationProvider(
    this._rejectQuotation,
  );

  Future<void> reject(QuotationsModel model) async {
    setLoadingState();
    final response = await _rejectQuotation.call(RejectQuotationParams(model));
    response.fold(
      (failure) => setErrorState(failure.message),
      setLoadedState,
    );
  }
}
