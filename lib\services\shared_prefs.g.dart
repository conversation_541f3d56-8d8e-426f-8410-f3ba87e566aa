// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shared_prefs.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sharedPrefsHash() => r'c77d6b40bc1b9a5a17451754f33353cac58349b3';

/// See also [sharedPrefs].
@ProviderFor(sharedPrefs)
final sharedPrefsProvider = Provider<SharedPreferences>.internal(
  sharedPrefs,
  name: r'sharedPrefsProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$sharedPrefsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef SharedPrefsRef = ProviderRef<SharedPreferences>;
String _$sharedPrefsFutureHash() => r'ca11f53b52df9380d420114b1313821460f8b517';

/// See also [sharedPrefsFuture].
@ProviderFor(sharedPrefsFuture)
final sharedPrefsFutureProvider = FutureProvider<SharedPreferences>.internal(
  sharedPrefsFuture,
  name: r'sharedPrefsFutureProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$sharedPrefsFutureHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef SharedPrefsFutureRef = FutureProviderRef<SharedPreferences>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
