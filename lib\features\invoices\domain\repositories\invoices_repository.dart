import 'package:dartz/dartz.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/features/attachments/domain/entites/attachments_entity.dart';
import 'package:mdd/features/invoices/domain/entities/invoices_entity.dart';
import 'package:mdd/features/invoices/domain/usecases/invoice_file.dart';
import 'package:mdd/features/invoices/domain/usecases/invoices.dart';

abstract class InvoicesRepository {
  Future<Either<Failure, List<InvoicesEntity>>> getInvoices(
      InvoicesParams params);
  Future<Either<Failure, AttachmentsEntity>> getInvoiceFileByID(
      InvoiceFileParams params);
}
