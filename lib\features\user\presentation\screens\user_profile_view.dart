import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/form_field_widget.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/core/widgets/title_item_widget.dart';
import 'package:mdd/features/customer/data/models/customers_model.dart';
import 'package:mdd/features/user/presentation/provider/update_user_provider.dart';
import 'package:mdd/features/user/presentation/provider/user_profile_provider.dart';
import 'package:mdd/features/user/presentation/screens/change_password_screen.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/constants/regex_constants.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class UserProfileView extends ConsumerStatefulWidget {
  const UserProfileView({
    super.key,
  });

  @override
  ConsumerState createState() => _UserProfileViewState();
}

class _UserProfileViewState extends ConsumerState<UserProfileView> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneNumberController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();

  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      ref.read(userProfileProvider.notifier).fetchProfile();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    ref
      ..listen(userProfileProvider, (previous, state) {
        if (state is LoadedViewState<CustomersModel>) {
          _nameController.text = state.data.fullName ?? '';
          _emailController.text = state.data.email ?? '';
          _phoneNumberController.text = state.data.mobile ?? '';
        }
      })
      ..listen(updateUserProvider, (previous, state) {
        if (state is LoadedViewState<ResultModel>) {
          UiHelper.showNotification(
            state.data.messageAr ?? '',
            notificationType: NotificationType.success,
          );
          ref.read(userProfileProvider.notifier).fetchProfile();
        }
      });
    final state = ref.watch(userProfileProvider);
    if (state is LoadingViewState) {
      return const LoaderWidget();
    }
    if (state is LoadedViewState<CustomersModel>) {
      final editingProfile = ref.watch(enableEditingProfile);
      return Column(
        children: [
          state.data.image == null
              ? SvgPicture.asset('assets/icons/avatar_icon.svg')
              : CircleAvatar(
                  radius: context.widthR(0.25) / 2,
                  backgroundColor: AppColors.white,
                  child: Image.memory(
                    base64Decode(state.data.image!.split(',')[1]),
                    fit: BoxFit.cover, // Adjust the BoxFit property as needed
                  ),
                ),
          ButtonWidget(
            onPressed: () {
              showDialog(
                context: context,
                builder: (_) => Dialog(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      AppDimensions.kMediumRadius,
                    ),
                  ),
                  insetPadding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.kSizeXXLarge,
                  ),
                  child: const ChangePasswordScreen(),
                ),
              );
            },
            title: 'change_password'.tr(),
            backgroundColor: AppColors.purple,
          ),
          Row(
            children: [
              TitleItemWidget(title: 'personal_info'.tr()),
              const Spacer(),
              Consumer(builder: (context, ref, child) {
                final editingProfile = ref.watch(enableEditingProfile);
                return Visibility(
                  visible: editingProfile,
                  child: ButtonWidget(
                    onPressed: () {
                      if (_formKey.currentState != null &&
                          _formKey.currentState!.validate()) {
                        _updateProfile(state.data);
                      }
                    },
                    title: 'save'.tr(),
                  ),
                );
              }),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.kSizeMedium,
                ),
                child: Consumer(builder: (context, ref, child) {
                  final editingProfile = ref.watch(enableEditingProfile);
                  return ButtonWidget(
                    onPressed: () {
                      ref
                          .read(enableEditingProfile.notifier)
                          .update((state) => !state);
                      if (!ref.read(enableEditingProfile) &&
                          ref.read(userProfileProvider)
                              is LoadedViewState<CustomersModel>) {
                        _nameController.text = state.data.fullName ?? '';
                        _emailController.text = state.data.email ?? '';
                        _phoneNumberController.text = state.data.mobile ?? '';
                      }
                    },
                    title: editingProfile ? 'end_edit'.tr() : 'edit'.tr(),
                    backgroundColor:
                        editingProfile ? AppColors.red : AppColors.primary,
                  );
                }),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.kSizeSmall),
          Form(
            key: _formKey,
            child: Column(
              children: [
                FormFieldWidget(
                  controller: _nameController,
                  icon: 'assets/icons/name_icon.svg',
                  validator: (value) {
                    if (value != null && value.isEmpty) {
                      return 'enter_valid_name'.tr();
                    }
                    return null;
                  },
                  editingForm: editingProfile,
                  hintText: 'name'.tr(),
                ),
                FormFieldWidget(
                  controller: _phoneNumberController,
                  icon: 'assets/icons/phone_icon.svg',
                  validator: (value) {
                    if (value != null && value.length == 10) {
                      return null;
                    }
                    return 'enter_valid_phone'.tr();
                  },
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(10),
                    FilteringTextInputFormatter.allow(
                      RegexConstants.kNumberRegex,
                    )
                  ],
                  editingForm: editingProfile,
                  hintText: 'phone_number'.tr(),
                  textInputType: TextInputType.phone,
                ),
                FormFieldWidget(
                  controller: _emailController,
                  icon: 'assets/icons/email_icon.svg',
                  validator: (value) {
                    if (value != null &&
                        !RegexConstants.kEmailRegex.hasMatch(value)) {
                      return 'enter_valid_email'.tr();
                    }
                    return null;
                  },
                  editingForm: editingProfile,
                  hintText: 'email'.tr(),
                ),
                const SizedBox(
                  height: AppDimensions.kSizeXXLarge,
                ),
              ],
            ),
          ),
        ],
      );
    }
    return const SizedBox();
  }

  void _updateProfile(CustomersModel employee) {
    if (_formKey.currentState != null && _formKey.currentState!.validate()) {
      ref.read(updateUserProvider.notifier).updateProfile(
            employee.copyWith(
              fullName: _nameController.text,
              mobile: _phoneNumberController.text.arabicNumberConverter(),
              email: _emailController.text,
            ),
          );
    }
  }
}
