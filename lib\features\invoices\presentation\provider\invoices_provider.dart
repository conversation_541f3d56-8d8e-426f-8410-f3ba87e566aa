import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/paginated_provider.dart';
import 'package:mdd/features/invoices/domain/entities/invoices_entity.dart';
import 'package:mdd/features/invoices/domain/usecases/invoices.dart';
import 'package:mdd/features/invoices/presentation/provider/invoice_text_field_provider.dart';

final invoiceStatusProvider = StateProvider<int?>((ref) => null);
final invoicesProvider = StateNotifierProvider<InvoicesProvider, ViewState>(
    (ref) => InvoicesProvider(
          ref.watch(invoicesUseCaseProvider),
          ref.watch(invoiceStatusProvider),
          ref.watch(invoiceSearchControllerProvider),
        ));

class InvoicesProvider extends PaginatedProvider<InvoicesEntity> {
  final Invoices _invoices;
  final int? _invoiceStatus;
  final TextEditingController? _invoiceSearchController;

  InvoicesProvider(
    this._invoices,
    this._invoiceStatus,
    this._invoiceSearchController,
  ) : super(InitialViewState());

  Future<void> fetchInvoices() async {
    state = LoadingViewState();
    final response = await fetchList();
    response.fold((failure) {
      state = ErrorViewState(errorMessage: failure.message);
    }, (invoices) {
      state = LoadedViewState(invoices);
    });
  }

  @override
  Future<Either<Failure, List<InvoicesEntity>>> fetchList() {
    return _invoices.call(InvoicesParams(
      page: pageNumber++,
      invoiceNo: _invoiceSearchController?.text,
      invoiceStatus: _invoiceStatus,
    ));
  }
}
