import 'package:auto_route/annotations.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/core/widgets/title_item_widget.dart';
import 'package:mdd/features/statistics/domain/entity/monthly_summary_entity.dart';
import 'package:mdd/features/statistics/domain/entity/order_statuses_entity.dart';
import 'package:mdd/features/statistics/domain/entity/quotation_statuses_entity.dart';
import 'package:mdd/features/statistics/domain/entity/statisctics_entity.dart';
import 'package:mdd/features/statistics/presentation/provider/statistics_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enum_extensions.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

@RoutePage()
class StatisticsScreen extends ConsumerStatefulWidget {
  const StatisticsScreen({super.key});

  @override
  ConsumerState<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends ConsumerState<StatisticsScreen> {
  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      ref.read(statisticsProvider.notifier).getStatistics();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(statisticsProvider);
    if (state is LoadingViewState) {
      return const LoaderWidget();
    }
    if (state is LoadedViewState<StatisticsEntity>) {
      return SingleChildScrollView(
        child: Padding(
          padding:
              const EdgeInsets.symmetric(horizontal: AppDimensions.kSizeMedium),
          child: Column(
            children: [
              const SizedBox(
                height: AppDimensions.kSizeMedium,
              ),
              TitleItemWidget(title: 'orders'.tr()),
              const SizedBox(
                height: AppDimensions.kSizeMedium,
              ),
              ClipRRect(
                borderRadius: BorderRadius.circular(
                  AppDimensions.kDefaultRadius,
                ),
                child: SfCircularChart(
                    title: ChartTitle(
                        text:
                            '${'total_orders'.tr()} ${ref.read(statisticsProvider.notifier).totalOrders()}',
                        textStyle: context.textTheme.bodyLarge),
                    legend: Legend(
                        isVisible: true,
                        textStyle: context.textTheme.bodySmall),
                    backgroundColor: AppColors.cardDetailsBackground,
                    palette: const [
                      Color(0xFF26E92D),
                      Color(0xFF2197DB),
                      Color(0xFF9452F7),
                      Color(0xFFEA9424),
                      AppColors.brownCard,
                      AppColors.red,
                    ],
                    series: <DoughnutSeries<OrderStatusesEntity, String>>[
                      DoughnutSeries<OrderStatusesEntity, String>(
                          explode: true,
                          dataSource: state.data.orderStatuses,
                          explodeOffset: '2',
                          explodeGesture: ActivationMode.none,
                          explodeAll: true,
                          innerRadius: "50",
                          strokeColor:
                              AppColors.cardDetailsBackground.withOpacity(0.5),
                          strokeWidth: 2,
                          xValueMapper: (OrderStatusesEntity data, _) =>
                              data.status.translatedName,
                          yValueMapper: (OrderStatusesEntity data, _) =>
                              data.ordersCount,
                          dataLabelSettings: DataLabelSettings(
                            isVisible: true,
                            textStyle: context.textTheme.bodySmall!.copyWith(
                              color: AppColors.white,
                            ),
                          )),
                    ]),
              ),
              const SizedBox(
                height: AppDimensions.kSizeMedium,
              ),
              const SizedBox(
                height: AppDimensions.kSizeMedium,
              ),
              ClipRRect(
                borderRadius: BorderRadius.circular(
                  AppDimensions.kDefaultRadius,
                ),
                child: SfCartesianChart(
                  plotAreaBorderWidth: 0,
                  backgroundColor: AppColors.cardDetailsBackground,
                  title: ChartTitle(
                      text:
                          '${'total_amount_of_orders'.tr()} ${ref.read(statisticsProvider.notifier).totalAmountOfOrders().toString().groupingSeparator()}',
                      textStyle: context.textTheme.bodyLarge),
                  primaryXAxis: const CategoryAxis(
                    majorGridLines: MajorGridLines(width: 0),
                  ),
                  palette: const [
                    AppColors.primary,
                  ],
                  primaryYAxis: const NumericAxis(
                      axisLine: AxisLine(width: 0),
                      majorTickLines: MajorTickLines(size: 0)),
                  series: <ColumnSeries<MonthlySummaryEntity, String>>[
                    ColumnSeries<MonthlySummaryEntity, String>(
                      dataSource:
                          state.data.monthlySummary?.take(6).toList() ?? [],
                      xValueMapper: (MonthlySummaryEntity summary, _) =>
                          summary.monthName,
                      yValueMapper: (MonthlySummaryEntity summary, _) =>
                          summary.ordersAmount,
                      dataLabelMapper: (MonthlySummaryEntity summary, _) =>
                          summary.ordersAmount
                              .round()
                              .toString()
                              .groupingSeparator(),
                      borderRadius:
                          BorderRadius.circular(AppDimensions.kSmallRadius),
                      dataLabelSettings: const DataLabelSettings(
                          isVisible: true, textStyle: TextStyle(fontSize: 10)),
                    )
                  ],
                ),
              ),
              const SizedBox(
                height: AppDimensions.kSizeMedium,
              ),
              TitleItemWidget(title: 'quotations'.tr()),
              const SizedBox(
                height: AppDimensions.kSizeMedium,
              ),
              ClipRRect(
                borderRadius: BorderRadius.circular(
                  AppDimensions.kDefaultRadius,
                ),
                child: SfCircularChart(
                    title: ChartTitle(
                        text:
                            '${'total_quotations'.tr()} ${ref.read(statisticsProvider.notifier).totalQuotations()}',
                        textStyle: context.textTheme.bodyLarge),
                    legend: Legend(
                        isVisible: true,
                        textStyle: context.textTheme.bodySmall),
                    backgroundColor: AppColors.cardDetailsBackground,
                    palette: const [
                      Color(0xFFEA9424),
                      AppColors.red,
                      Color(0xFF26E92D),
                    ],
                    series: <DoughnutSeries<QuotationStatusesEntity, String>>[
                      DoughnutSeries<QuotationStatusesEntity, String>(
                          explode: true,
                          dataSource: state.data.quotationStatuses,
                          explodeOffset: '2',
                          explodeGesture: ActivationMode.none,
                          explodeAll: true,
                          innerRadius: "50",
                          strokeColor:
                              AppColors.cardDetailsBackground.withOpacity(0.5),
                          strokeWidth: 2,
                          xValueMapper: (QuotationStatusesEntity data, _) =>
                              data.status.translatedName,
                          yValueMapper: (QuotationStatusesEntity data, _) =>
                              data.quotationsCount,
                          dataLabelSettings: DataLabelSettings(
                            isVisible: true,
                            textStyle: context.textTheme.bodySmall!.copyWith(
                              color: AppColors.white,
                            ),
                          )),
                    ]),
              ),
            ],
          ),
        ),
      );
    }
    return const SizedBox();
  }
}
