import 'package:json_annotation/json_annotation.dart';
import 'package:mdd/features/organization/domain/entities/organizations_entity.dart';
import 'package:mdd/utils/enums.dart';

part 'organizations_model.g.dart';

@JsonSerializable()
class OrgsModel extends OrgsEntity {
  OrgsModel({
    super.organizationID,
    this.organizationNo,
    super.name,
    super.cr,
    this.fileName,
    this.fileContent,
    this.fileContentType,
    super.landLine,
    super.street,
    super.zipCode,
    super.cityID,
    super.cityName,
    super.email,
    super.website,
    this.registerDate,
    super.creditBalance,
    super.creditDays,
    super.status,
    this.statusName,
    super.type,
    this.typeName,
    super.isPORequiredOnApproval,
    super.isPONumberAutoGenerated,
    super.isAllowedApproveQuotation,
    this.isCredit,
    this.logo,
    super.vatNumber,
  });

  int? organizationNo;
  String? fileName;
  String? fileContent;
  String? fileContentType;
  String? registerDate;
  String? statusName;
  String? typeName;

  bool? isCredit;
  String? logo;

  factory OrgsModel.fromJson(Map<String, dynamic> json) =>
      _$OrgsModelFromJson(json);

  Map<String, dynamic> toJson() => _$OrgsModelToJson(this);

  OrgsModel copyWith({
    String? organizationID,
    int? organizationNo,
    String? name,
    String? cr,
    String? fileName,
    String? fileContent,
    String? fileContentType,
    String? landLine,
    String? street,
    String? zipCode,
    String? cityID,
    String? cityName,
    String? email,
    String? website,
    String? registerDate,
    double? creditBalance,
    int? creditDays,
    OrganizationStatus? status,
    String? statusName,
    OrganizationType? type,
    String? typeName,
    bool? isPORequiredOnApproval,
    bool? isPONumberAutoGenerated,
    bool? isAllowedApproveQuotation,
    bool? isCredit,
    String? vatNumber,
    String? logo,
  }) {
    return OrgsModel(
      organizationID: organizationID ?? this.organizationID,
      organizationNo: organizationNo ?? this.organizationNo,
      name: name ?? this.name,
      cr: cr ?? this.cr,
      fileName: fileName ?? this.fileName,
      fileContent: fileContent ?? this.fileContent,
      fileContentType: fileContentType ?? this.fileContentType,
      landLine: landLine ?? this.landLine,
      street: street ?? this.street,
      zipCode: zipCode ?? this.zipCode,
      cityID: cityID ?? this.cityID,
      cityName: cityName ?? this.cityName,
      email: email ?? this.email,
      website: website ?? this.website,
      registerDate: registerDate ?? this.registerDate,
      creditBalance: creditBalance ?? this.creditBalance,
      creditDays: creditDays ?? this.creditDays,
      status: status ?? this.status,
      statusName: statusName ?? this.statusName,
      type: type ?? this.type,
      typeName: typeName ?? this.typeName,
      isPORequiredOnApproval:
          isPORequiredOnApproval ?? this.isPORequiredOnApproval,
      isPONumberAutoGenerated:
          isPONumberAutoGenerated ?? this.isPONumberAutoGenerated,
      isAllowedApproveQuotation:
          isAllowedApproveQuotation ?? this.isAllowedApproveQuotation,
      isCredit: isCredit ?? this.isCredit,
      vatNumber: vatNumber ?? this.vatNumber,
      logo: logo ?? this.logo,
    );
  }
}
