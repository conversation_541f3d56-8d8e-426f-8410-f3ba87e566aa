import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/user/data/repositories/user_repository_impl.dart';
import 'package:mdd/features/user/domain/entities/user_entity.dart';
import 'package:mdd/features/user/domain/repositories/user_repository.dart';

final loginProvider =
    Provider<Login>((ref) => Login(ref.watch(userRepositoryImpl)));

class Login implements UseCase<UserEntity, LoginParams> {
  final UserRepository userRepository;

  Login(this.userRepository);

  @override
  Future<Either<Failure, UserEntity>> call(LoginParams params) async {
    return await userRepository.login(params.userName, params.password);
  }
}

class LoginParams extends Equatable {
  final String userName;
  final String password;

  const LoginParams({required this.userName, required this.password});

  @override
  List<Object?> get props => [userName, password];
}
