import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/dropdowns/data/repositories/drop_downs_repository_impl.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/domain/repositories/drop_downs_repository.dart';

final customersListUseCaseProvider = Provider<CustomersList>(
  (ref) => CustomersList(ref.watch(dropDownsRepositoryImpl)),
);

class CustomersList
    implements UseCase<List<DropDownEntity>, CustomersListParams> {
  final DropDownsRepository _repository;

  CustomersList(this._repository);

  @override
  Future<Either<Failure, List<DropDownEntity>>> call(
      CustomersListParams params) async {
    return _repository.customersList(params);
  }
}

class CustomersListParams {
  final String? orgID;

  const CustomersListParams({required this.orgID});
}
