import 'package:flutter/material.dart';
import 'package:mdd/theme/dimensions.dart';

class TitleWidget extends StatelessWidget {
  final String title;
  final TextAlign? textAlign;
  final TextDirection? textDirection;
  final double? fontSize;
  final TextOverflow? overflow;
  final Color? color;
  final FontWeight? fontWeight;
  final String? fontFamily;

  const TitleWidget(this.title,
      {super.key,
      this.textAlign,
      this.textDirection,
      this.fontSize,
      this.overflow,
      this.fontWeight,
      this.fontFamily,
      this.color});
  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: Theme.of(context).textTheme.bodySmall!.copyWith(
          color: color,
          fontWeight: fontWeight,
          fontSize: fontSize ?? AppDimensions.kSizeXLarge,
          fontFamily: fontFamily),
      textAlign: textAlign ?? TextAlign.start,
      textDirection: textDirection,
      overflow: overflow ?? TextOverflow.visible,
    );
  }
}
