import 'package:auto_route/annotations.dart';
import 'package:bottom_picker/bottom_picker.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/details_custom_bar.dart';
import 'package:mdd/core/widgets/form_field_widget.dart';
import 'package:mdd/core/widgets/main_app_bar.dart';
import 'package:mdd/features/attachments/presentation/provider/attachments_provider.dart';
import 'package:mdd/features/attachments/presentation/screens/attachments_view.dart';
import 'package:mdd/features/dropdowns/presentation/providers/banks_provider.dart';
import 'package:mdd/features/dropdowns/presentation/widgets/banks_list_drop_down_widget.dart';
import 'package:mdd/features/invoices/presentation/provider/invoices_provider.dart';
import 'package:mdd/features/payments/data/models/payments_model.dart';
import 'package:mdd/features/payments/presentation/provider/create_new_payment_provider.dart';
import 'package:mdd/features/payments/presentation/provider/payments_provider.dart';
import 'package:mdd/features/payments/presentation/widgets/card_transfer_info.dart';
import 'package:mdd/features/user/domain/entities/user_entity.dart';
import 'package:mdd/features/user/presentation/provider/user_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/constants/regex_constants.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/extensions.dart';
import 'package:mdd/utils/helper_functions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

@RoutePage()
class CreatePaymentScreen extends ConsumerStatefulWidget {
  final String? orderId;
  final String? invoiceId;
  final double? amount;

  const CreatePaymentScreen({
    super.key,

    this.orderId,
    this.invoiceId,
    this.amount,

  });

  @override
  ConsumerState createState() => _CreatePaymentScreenState();
}

class _CreatePaymentScreenState extends ConsumerState<CreatePaymentScreen> {
  late TextEditingController _amountController;
  late TextEditingController _transactionNumber;
  late TextEditingController _dateController;
  late TextEditingController _formattedDateController;
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    _amountController = TextEditingController(text: widget.amount?.toString());
    _transactionNumber = TextEditingController();
    _dateController = TextEditingController();
    _formattedDateController = TextEditingController();

    super.initState();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _transactionNumber.dispose();
    _dateController.dispose();
    _formattedDateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(createNewPaymentProvider, (previous, state) {
      if (state is LoadingViewState) {
        return UiHelper.showLoadingDialog(context);
      }
      if (state is LoadedViewState<ResultModel>) {
        Navigator.pop(context);
        Navigator.pop(context);
        UiHelper.showNotification('create_payment_success'.tr(),
            notificationType: NotificationType.success);
      }
      if (widget.invoiceId != null) {
        ref.read(invoicesProvider.notifier).resetPagination();
        ref.read(invoicesProvider.notifier).fetchInvoices();
      } else {
        ref.read(paymentsProvider.notifier).resetPagination();
        ref.read(paymentsProvider.notifier).fetchPayments();
      }
      if (state is ErrorViewState) {
        Navigator.pop(context);
        UiHelper.showNotification('create_payment_failure'.tr());
      }
    });
    return Scaffold(
      appBar: const MainAppBar(),
      body: Column(
        children: [
          DetailsCustomBar(title: 'create_payment'.tr()),
          Expanded(
            child: SingleChildScrollView(
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    const CardTransferInfo(),
                    FormFieldWidget(
                      controller: _amountController,
                      hintText: 'amount'.tr(),
                      fillColor: AppColors.cardDetailsBackground,
                      borderColor: AppColors.cardDetailsBackground,
                      inputFormatters: [
                        LengthLimitingTextInputFormatter(10),
                        FilteringTextInputFormatter.allow(
                            RegexConstants.kNumberRegex),
                      ],
                      textInputType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          return null;
                        }
                        return 'amount_validate'.tr();
                      },
                    ),
                    FormFieldWidget(
                      controller: _formattedDateController,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          return null;
                        }
                        return 'date_validate'.tr();
                      },
                      hintText: 'paid_date'.tr(),
                      onTap: () {
                        BottomPicker.date(
                          pickerTitle: Text(
                            "paid_date_validate".tr(),
                            style: const TextStyle(color: AppColors.black),
                          ),
                          onSubmit: (date) {
                            if (date != null) {
                              _dateController.text =
                                  DateTime.parse(date.toString())
                                      .toIso8601String();
                              _formattedDateController.text =
                                  HelperFunctions.formatDate(date.toString());
                            }
                          },
                          onClose: () {
                            debugPrint("Picker closed");
                          },
                          buttonContent: Text(
                            'choose'.tr(),
                            style: const TextStyle(color: AppColors.white),
                          ),
                          buttonSingleColor: AppColors.primary,
                          dismissable: true,
                          displaySubmitButton: true,
                        ).show(context);
                      },
                      suffixWidget: Padding(
                        padding: const EdgeInsets.all(AppDimensions.kSizeSmall),
                        child: Card(
                          color: AppColors.primary,
                          child: Padding(
                            padding:
                                const EdgeInsets.all(AppDimensions.kSizeSmall),
                            child: SvgPicture.asset(
                              'assets/icons/date_icon.svg',
                            ),
                          ),
                        ),
                      ),
                      readOnly: true,
                      fillColor: AppColors.cardDetailsBackground,
                      borderColor: AppColors.cardDetailsBackground,
                    ),
                    const BanksListDropDownWidget(),
                    FormFieldWidget(
                      controller: _transactionNumber,
                      hintText: 'transaction_number'.tr(),
                      fillColor: AppColors.cardDetailsBackground,
                      borderColor: AppColors.cardDetailsBackground,
                      textInputType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          return null;
                        }
                        return 'transaction_number_validate'.tr();
                      },
                    ),
                    const SizedBox(
                      height: AppDimensions.kSizeMedium,
                    ),
                    AttachmentsView(
                      buttonTitle: 'add_attachment'.tr(),
                      primary: false,
                      maximumAttachments: 1,
                    ),
                    ButtonWidget(
                      onPressed: _createNewPayment,
                      title: 'create'.tr(),
                      horizontalPadding: AppDimensions.kSizeXXLarge,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _createNewPayment() {
    final attachments = ref.read(attachmentsProvider).attachments;
    final user = (ref.read(userProvider) as LoadedViewState<UserEntity>).data;
    if (_formKey.currentState!.validate()) {
      if (attachments.isEmpty) {
        UiHelper.showNotification('attachment_validate'.tr());
        return;
      }

      ref.read(createNewPaymentProvider.notifier).fetchCreateNewPayment(
            PaymentsModel(
              paymentID: HelperFunctions.generateId(),
              paidAmount:
                  double.parse(_amountController.text.arabicNumberConverter()),
              paymentDate: _dateController.text,
              transactionNo: _transactionNumber.text.arabicNumberConverter(),
              bankID: ref.read(selectedBankProvider)?.id,
              fileName: attachments.first.fileName,
              fileContent: attachments.first.fileContent,
              fileContentType: attachments.first.fileContentType,
              orderID: widget.orderId,
              invoiceID: widget.invoiceId,
              approvedAmount: 0,
              status: PaymentsStatusType.waiting,
              isDebit: true,
              isFromDebitPayment: false,
              employeeID: user.userID,
              organizationID: user.customer?.organizationID,
            ),
          );
    }
  }
}
