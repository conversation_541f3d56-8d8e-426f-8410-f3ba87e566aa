import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/orders/data/models/create_order_model.dart';
import 'package:mdd/features/orders/data/repositories/orders_repository_impl.dart';

final createOrderUseCaseProvider =
    Provider<CreateOrder>((ref) => CreateOrder(ref.watch(orderRepositoryImpl)));

class CreateOrder implements UseCase<ResultModel, CreateOrderParams> {
  final OrdersRepositoryImpl createOrderRepository;

  CreateOrder(this.createOrderRepository);

  @override
  Future<Either<Failure, ResultModel>> call(CreateOrderParams params) async {
    return await createOrderRepository.createOrder(params);
  }
}

class CreateOrderParams {
  final CreateOrderModel createdOrder;
  final bool isDraft;

  CreateOrderParams({
    required this.createdOrder,
    this.isDraft = false,
  });
}
