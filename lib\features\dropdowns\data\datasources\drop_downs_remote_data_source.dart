import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/endPoints/end_points.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/dropdowns/data/models/drop_down_model.dart';
import 'package:mdd/features/dropdowns/domain/usecases/customers_list.dart';
import 'package:mdd/features/dropdowns/domain/usecases/products_list.dart';
import 'package:mdd/features/dropdowns/domain/usecases/projects_list.dart';
import 'package:mdd/services/dio_client.dart';

abstract class DropDownsRemoteDataSource {
  Future<List<DropDownModel>> customersList(CustomersListParams params);
  Future<List<DropDownModel>> citiesList(NoParams params);
  Future<List<DropDownModel>> categoriesList(NoParams params);
  Future<List<DropDownModel>> banksList(NoParams params);
  Future<List<DropDownModel>> projectsList(ProjectsListParams params);
  Future<List<DropDownModel>> rejectReasonsList(NoParams params);
  Future<List<DropDownModel>> unitsList(NoParams params);
  Future<List<DropDownModel>> productsList(ProductsListParams params);
}

final dropDownsRemoteDataSourceImpl = Provider<DropDownsRemoteDataSourceImpl>(
  (ref) => DropDownsRemoteDataSourceImpl(ref.watch(dioClientProvider)),
);

class DropDownsRemoteDataSourceImpl implements DropDownsRemoteDataSource {
  final DioClient _dioClient;

  DropDownsRemoteDataSourceImpl(this._dioClient);

  @override
  Future<List<DropDownModel>> customersList(CustomersListParams params) async {
    final response = await _dioClient.dio.get(
      EndPoints.customersList,
      queryParameters: <String, dynamic>{"OrganizationID": params.orgID},
    );
    return (response.data['data'] as List)
        .map((customer) => DropDownModel.fromJson(customer))
        .toList();
  }

  @override
  Future<List<DropDownModel>> citiesList(NoParams params) async {
    final response = await _dioClient.dio.get(EndPoints.citiesList);
    return (response.data['data'] as List)
        .map((org) => DropDownModel.fromJson(org))
        .toList();
  }

  @override
  Future<List<DropDownModel>> categoriesList(NoParams params) async {
    final response = await _dioClient.dio.get(EndPoints.categoriesList);
    return (response.data['data'] as List)
        .map((category) => DropDownModel.fromJson(category))
        .toList();
  }

  @override
  Future<List<DropDownModel>> banksList(NoParams params) async {
    final response = await _dioClient.dio.get(EndPoints.banksList);
    return (response.data['data'] as List)
        .map((bank) => DropDownModel.fromJson(bank))
        .toList();
  }

  @override
  Future<List<DropDownModel>> projectsList(ProjectsListParams params) async {
    final response = await _dioClient.dio.get(
      EndPoints.projectsList,
      queryParameters: <String, dynamic>{'OrganizationID': params.orgID},
    );
    return (response.data['data'] as List)
        .map((project) => DropDownModel.fromJson(project))
        .toList();
  }

  @override
  Future<List<DropDownModel>> rejectReasonsList(NoParams params) async {
    final response = await _dioClient.dio.get(EndPoints.rejectReasonsList);
    return (response.data['data'] as List)
        .map((rejectReason) => DropDownModel.fromJson(rejectReason))
        .toList();
  }

  @override
  Future<List<DropDownModel>> unitsList(NoParams params) async {
    final response = await _dioClient.dio.get(EndPoints.unitsList);
    return (response.data['data'] as List)
        .map((unit) => DropDownModel.fromJson(unit))
        .toList();
  }

  @override
  Future<List<DropDownModel>> productsList(ProductsListParams params) async {
    final response = await _dioClient.dio.get(
      EndPoints.productsList,
      queryParameters: {
        if (params.find?.isNotEmpty ?? false) 'find': params.find,
      },
    );
    return (response.data['data'] as List)
        .map((product) => DropDownModel.fromJson(product))
        .toList();
  }
}
