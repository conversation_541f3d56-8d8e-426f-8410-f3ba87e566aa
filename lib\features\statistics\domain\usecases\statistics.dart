import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/statistics/data/repositories/statistics_repository_impl.dart';
import 'package:mdd/features/statistics/domain/entity/statisctics_entity.dart';
import 'package:mdd/features/statistics/domain/repositories/statistics_repository.dart';

final statisticsUseCaseProvider = Provider<Statistics>(
    (ref) => Statistics(ref.watch(statisticsRepositoryImpl)));

class Statistics implements UseCase<StatisticsEntity?, StatisticsParams> {
  final StatisticsRepository _statisticsRepository;

  Statistics(this._statisticsRepository);

  @override
  Future<Either<Failure, StatisticsEntity?>> call(
      StatisticsParams params) async {
    return await _statisticsRepository.getStatistics(params);
  }
}

class StatisticsParams {
  final String startDate;
  final String endDate;

  StatisticsParams({required this.startDate, required this.endDate});
}
