import 'package:dartz/dartz.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/dropdowns/domain/entities/drop_down_entity.dart';
import 'package:mdd/features/dropdowns/domain/usecases/customers_list.dart';
import 'package:mdd/features/dropdowns/domain/usecases/products_list.dart';
import 'package:mdd/features/dropdowns/domain/usecases/projects_list.dart';

abstract class DropDownsRepository {
  Future<Either<Failure, List<DropDownEntity>>> customersList(
      CustomersListParams params);
  Future<Either<Failure, List<DropDownEntity>>> citiesList(NoParams params);
  Future<Either<Failure, List<DropDownEntity>>> categoriesList(NoParams params);
  Future<Either<Failure, List<DropDownEntity>>> banksList(NoParams params);
  Future<Either<Failure, List<DropDownEntity>>> projectsList(
      ProjectsListParams params);
  Future<Either<Failure, List<DropDownEntity>>> rejectReasonsList(
      NoParams params);
  Future<Either<Failure, List<DropDownEntity>>> unitsList(NoParams params);
  Future<Either<Failure, List<DropDownEntity>>> productsList(
      ProductsListParams params);
}
