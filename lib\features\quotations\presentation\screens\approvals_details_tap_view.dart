import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/widgets/description_widget/card_item_widget.dart';
import 'package:mdd/core/widgets/description_widget/description_card_item_widget.dart';
import 'package:mdd/features/quotations/data/models/approval_model.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/utils/enum_extensions.dart';
import 'package:mdd/utils/helper_functions.dart';

class ApprovalsDetailsTapView extends ConsumerStatefulWidget {
  const ApprovalsDetailsTapView({
    super.key,
    required this.approvals,
  });

  final List<ApprovalModel> approvals;
  @override
  ConsumerState createState() => _ApprovalsDetailsTapViewState();
}

class _ApprovalsDetailsTapViewState
    extends ConsumerState<ApprovalsDetailsTapView> {
  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: ListView.builder(
        itemCount: widget.approvals.length,
        itemBuilder: (context, index) {
          final approval = widget.approvals[index];
          return CardItemsWidget(
            title: approval.approverName,
            subtitle: '',
            image: 'assets/images/customers_image.png',
            imageBackground: AppColors.blueCuriousCard,
            statusName: approval.status.translatedName,
            statusColor: approval.status.statusColor,
            descriptionWidget: [
              DescriptionCardItemWidget(
                title: 'approval_limit'.tr(),
                trailing: approval.approvalLimit == 0
                    ? 'no_limit'.tr()
                    : approval.approvalLimit.toString(),
              ),
              DescriptionCardItemWidget(
                title: 'approved_on'.tr(),
                trailing: approval.approvedOn != null
                    ? HelperFunctions.formatDate(approval.approvedOn!)
                    : '',
              ),
              DescriptionCardItemWidget(
                title: 'notes'.tr(),
                trailing: approval.notes ?? '',
              ),
            ],
          );
        },
      ),
    );
  }
}
