import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/theme/dimensions.dart';

class TitleItemWidget extends StatelessWidget {
  final String title;
  const TitleItemWidget({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          const EdgeInsets.symmetric(horizontal: AppDimensions.kSizeMedium),
      child: Row(
        children: [
          SvgPicture.asset('assets/icons/square_icon.svg'),
          const SizedBox(
            width: AppDimensions.kSizeSmall,
          ),
          TextWidget(
            title,
            fontWeight: FontWeight.bold,
          ),
        ],
      ),
    );
  }
}
