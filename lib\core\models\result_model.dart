import 'package:json_annotation/json_annotation.dart';

part 'result_model.g.dart';

@JsonSerializable()
class ResultModel {
  final String? title;
  final int? status;
  final String? message;
  final String? messageAr;
  final String? messageEn;
  final String? execptionMessage;

  ResultModel({
    this.title,
    this.status,
    this.message,
    this.messageAr,
    this.messageEn,
    this.execptionMessage,
  });

  factory ResultModel.fromJson(Map<String, dynamic> json) =>
      _$ResultModelFromJson(json);

  Map<String, dynamic> toJson() => _$ResultModelToJson(this);
}
