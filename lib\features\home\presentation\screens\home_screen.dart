import 'package:auto_route/annotations.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/core/widgets/title_widget.dart';
import 'package:mdd/features/home/<USER>/entities/dashboard_entity.dart';
import 'package:mdd/features/home/<USER>/provider/dashboard_provider.dart';
import 'package:mdd/features/home/<USER>/widgets/card_menu_widgt.dart';
import 'package:mdd/features/home/<USER>/widgets/last_comment_widget.dart';
import 'package:mdd/features/organization/data/models/organizations_model.dart';
import 'package:mdd/features/organization/presentation/provider/organization_details_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/ui/ui_helper.dart';
import 'package:responsive_framework/responsive_framework.dart';
import 'package:upgrader/upgrader.dart';

@RoutePage()
class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({
    super.key,
  });


  @override
  ConsumerState createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      _onRefresh();
    });
    super.initState();
  }

  Future<void> _onRefresh() async {
    await ref.read(orgDetailsProvider.notifier).fetchOrgDetails();
    await ref.read(dashboardProvider.notifier).fetchDashboard();
  }

  @override
  Widget build(BuildContext context) {
    return UpgradeAlert(
      upgrader: Upgrader(
        debugLogging: true,
        minAppVersion: '2.0.0',
        languageCode: context.locale.languageCode,
      ),
      child: Consumer(builder: (context, ref, child) {
        final orgState = ref.watch(orgDetailsProvider);

        final dashState = ref.watch(dashboardProvider);
        if (dashState is LoadingViewState || orgState is LoadingViewState) {
          return const Center(child: LoaderWidget());
        }
        if (dashState is EmptyViewState || orgState is EmptyViewState) {
          return const SizedBox();
        }
        if (dashState is ErrorViewState || orgState is ErrorViewState) {
          return const SizedBox();
        }

        if (dashState is LoadedViewState<DashboardEntity> &&
            orgState is LoadedViewState<OrgsModel>) {

          return orgState.data.status != OrganizationStatus.aprroved
              ? Scaffold(
                  body: Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 15.0,
                        vertical: 40.0,
                      ),
                      margin: const EdgeInsets.all(15.0),
                      decoration: const BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.all(
                          Radius.circular(10.0),
                        ),
                      ),
                      child: TextWidget(
                        orgState.data.status ==
                                OrganizationStatus.waittingForApproval
                            ? 'account_waiting_for_approval'.tr()
                            : orgState.data.status ==
                                    OrganizationStatus.informationMissed
                                ? 'you_need_complete_missed_info'.tr()
                                : '',
                        color: AppColors.white,
                        fontWeight: FontWeight.bold,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                )
              : Scaffold(
                  body: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        const SizedBox(
                          height: AppDimensions.kSizeXLarge,
                        ),
                        TextWidget(
                          'balance'.tr(),
                          color: AppColors.textGrey,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: AppDimensions.kSizeSmall),
                        TitleWidget(
                          dashState.data.owedAmount ?? '',
                          color: AppColors.black,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(
                          height: AppDimensions.kSizeXLarge,
                        ),
                        const Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: AppDimensions.kSizeLarge),
                          child: LastCommentWidget(),
                        ),
                        const SizedBox(
                          height: AppDimensions.kSizeLarge,
                        ),
                        GridView(
                          physics: const ClampingScrollPhysics(),
                          padding: const EdgeInsets.symmetric(
                              horizontal: AppDimensions.kSizeLarge),
                          shrinkWrap: true,
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: ResponsiveBreakpoints.of(context)
                                    .smallerThan(TABLET)
                                ? 2
                                : 3,
                            crossAxisSpacing: 20,
                            mainAxisSpacing: 10,
                          ),
                          children: [
                            CardMenuWidget(
                              title: 'orders'.tr(),
                              content: dashState.data.yearOperations ?? '',
                              image: 'assets/images/orders_image.png',
                              backgroundColor: AppColors.brownCard,
                            ),
                            CardMenuWidget(
                              title: 'payments'.tr(),
                              content:
                                  dashState.data.paymentApprovedAmount ?? '',
                              image: 'assets/images/payments_image.png',
                              backgroundColor: AppColors.primary,
                            ),
                            CardMenuWidget(
                              title: 'duo_amount'.tr(),
                              content: dashState.data.creditBalance ?? '',
                              image: 'assets/images/due_amount_image.png',
                              backgroundColor: AppColors.blueCard,
                            ),
                            CardMenuWidget(
                              title: 'unused_balance'.tr(),
                              content: dashState.data.unUsedBalance ?? '',
                              image: 'assets/images/unused_balance_image.png',
                              backgroundColor: AppColors.purpleCard,
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                );
        }
        return const SizedBox();
      }),
    );
  }
}
