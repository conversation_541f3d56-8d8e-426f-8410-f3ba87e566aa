// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
      userID: json['userID'] as String?,
      fullName: json['fullName'] as String?,
      email: json['email'] as String?,
      userName: json['userName'] as String?,
      password: json['password'] as String?,
      lastPasswordChage: json['lastPasswordChage'] as String?,
      isMfaEnabled: json['isMfaEnabled'] as bool?,
      mfaCode: json['mfaCode'] as String?,
      isActive: json['isActive'] as bool?,
      userType: $enumDecodeNullable(_$UserTypeEnumMap, json['userType']),
      token: json['token'] as String?,
      tokenExpireDate: json['tokenExpireDate'] as String?,
      roleType: json['roleType'] as String?,
      customer: json['customer'] == null
          ? null
          : CustomersModel.fromJson(json['customer'] as Map<String, dynamic>),
      roles: (json['roles'] as List<dynamic>?)
          ?.map((e) => RoleModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
      'userID': instance.userID,
      'fullName': instance.fullName,
      'email': instance.email,
      'userName': instance.userName,
      'password': instance.password,
      'lastPasswordChage': instance.lastPasswordChage,
      'isMfaEnabled': instance.isMfaEnabled,
      'mfaCode': instance.mfaCode,
      'isActive': instance.isActive,
      'userType': _$UserTypeEnumMap[instance.userType],
      'token': instance.token,
      'tokenExpireDate': instance.tokenExpireDate,
      'roleType': instance.roleType,
      'customer': instance.customer?.toJson(),
      'roles': instance.roles?.map((e) => e.toJson()).toList(),
    };

const _$UserTypeEnumMap = {
  UserType.customer: 'C',
  UserType.employee: 'E',
};
