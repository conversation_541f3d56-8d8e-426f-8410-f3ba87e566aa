import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enums.dart';

import 'nav_bar_item_widget.dart';

class BottomNavBarWidget extends ConsumerStatefulWidget {
  final TabsRouter tabsRouter;
  const BottomNavBarWidget({
    super.key,
    required this.tabsRouter,
  });

  @override
  ConsumerState<BottomNavBarWidget> createState() => _BottomNavBarWidgetState();
}

class _BottomNavBarWidgetState extends ConsumerState<BottomNavBarWidget> {
  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      ref.listen<int>(bottomBarIndex, (previous, activeIndex) {
        widget.tabsRouter.setActiveIndex(activeIndex);
      });
      return BottomAppBar(
        elevation: 7.0,
        child: Container(
          padding:
              const EdgeInsets.symmetric(vertical: AppDimensions.kSizeSmall),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NavBarButtonWidget(
                svgUrl: 'assets/icons/home_icon.svg',
                text: 'home'.tr(),
                destination: BottomNavBarDestination.home,
                onTap: () {
                  ref
                      .read(bottomBarIndex.notifier)
                      .setActiveIndex(BottomNavBarDestination.home.index);
                },
                isSelected: widget.tabsRouter.activeIndex ==
                    BottomNavBarDestination.home.index,
              ),
              NavBarButtonWidget(
                svgUrl: 'assets/icons/orders_icon.svg',
                text: 'orders'.tr(),
                destination: BottomNavBarDestination.orders,
                onTap: () {
                  ref
                      .read(bottomBarIndex.notifier)
                      .setActiveIndex(BottomNavBarDestination.orders.index);
                },
                isSelected: widget.tabsRouter.activeIndex ==
                    BottomNavBarDestination.orders.index,
              ),
              NavBarButtonWidget(
                svgUrl: 'assets/icons/offers_icon.svg',
                text: 'quotations'.tr(),
                destination: BottomNavBarDestination.offers,
                onTap: () {
                  ref
                      .read(bottomBarIndex.notifier)
                      .setActiveIndex(BottomNavBarDestination.offers.index);
                },
                isSelected: widget.tabsRouter.activeIndex ==
                    BottomNavBarDestination.offers.index,
              ),
              NavBarButtonWidget(
                svgUrl: 'assets/icons/invoices_icon.svg',
                text: 'invoices'.tr(),
                destination: BottomNavBarDestination.invoices,
                onTap: () {
                  ref
                      .read(bottomBarIndex.notifier)
                      .setActiveIndex(BottomNavBarDestination.invoices.index);
                },
                isSelected: widget.tabsRouter.activeIndex ==
                    BottomNavBarDestination.invoices.index,
              ),
              NavBarButtonWidget(
                svgUrl: 'assets/icons/statistics_icon.svg',
                text: 'statistics'.tr(),
                destination: BottomNavBarDestination.statistics,
                onTap: () {
                  ref
                      .read(bottomBarIndex.notifier)
                      .setActiveIndex(BottomNavBarDestination.statistics.index);
                },
                isSelected: widget.tabsRouter.activeIndex ==
                    BottomNavBarDestination.statistics.index,
              ),
            ],
          ),
        ),
      );
    });
  }
}

final bottomBarIndex =
    StateNotifierProvider.autoDispose<BottomBarIndexProvider, int>((ref) {
  return BottomBarIndexProvider();
});

class BottomBarIndexProvider extends StateNotifier<int> {
  BottomBarIndexProvider() : super(0);

  void setActiveIndex(int index) {
    // BottomBarIndexProvider can use the "ref" to read other providers
    state = index;
  }
}
