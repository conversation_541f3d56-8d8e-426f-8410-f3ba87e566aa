import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/loader_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/notifications/domain/entities/notifications_entity.dart';
import 'package:mdd/features/notifications/presentation/provider/notifications_provider.dart';
import 'package:mdd/routes/app_router.gr.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/ui/ui_helper.dart';

class LastCommentWidget extends ConsumerStatefulWidget {
  const LastCommentWidget({super.key});

  @override
  ConsumerState<LastCommentWidget> createState() => _LastCommentWidgetState();
}

class _LastCommentWidgetState extends ConsumerState<LastCommentWidget> {
  @override
  void initState() {
    UiHelper.postBuildCallback((p0) {
      _fetch();
    });
    super.initState();
  }

  _fetch() {
    ref.read(notificationsProvider.notifier).getNotifications();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(notificationsProvider);

    if (state is LoadingViewState) {
      return const LoaderWidget();
    }
    if (state is LoadedViewState<List<NotificationsEntity>>) {
      return InkWell(
        onTap: () => context.pushRoute(const NotificationsRoute()),
        child: Column(
          children: [
            Stack(
              alignment: Alignment.center,
              clipBehavior: Clip.none,
              children: [
                SizedBox(
                  width: double.infinity,
                  child: Card(

                      color: AppColors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                              AppDimensions.kDefaultRadius)),
                      elevation: 0,
                      shadowColor: AppColors.lightGrey.withOpacity(0.1),
                      child: Column(
                        children: [
                          const SizedBox(
                            height: AppDimensions.kSizeXLarge,
                          ),
                          state.data.isEmpty
                              ? TextWidget('empty_notifications'.tr())
                              : TextWidget(
                                  state.data.first.content ?? '',
                                  textAlign: TextAlign.center,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                          const SizedBox(
                            height: AppDimensions.kSizeMedium,
                          ),
                          Container(
                            width: AppDimensions.kSize3XLarge,
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                              gradient:  LinearGradient(
                                colors: [
                                  Color(0xFF0C8991),
                                  Color(0xFF0C8991),
                                  Color(0xFF2D9260),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(10),
                              child: SvgPicture.asset(
                                'assets/icons/arrow_icon.svg',
                                height: AppDimensions.kSizeLarge,
                              ),
                            ),
                          ),
                          const SizedBox(
                            height: AppDimensions.kSizeMedium,
                          ),
                        ],
                      )),
                ),
                Positioned(
                  top: -AppDimensions.kSizeLarge,
                  child: CircleAvatar(
                    backgroundColor: AppColors.white,
                    child: SvgPicture.asset(
                        'assets/icons/new_notification_icon.svg'),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }
    return const SizedBox();
  }
}
