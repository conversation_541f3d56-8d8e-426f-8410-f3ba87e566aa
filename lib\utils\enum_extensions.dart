import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/utils/enums.dart';

extension OrderStatusTypeExtension on OrderStatusType {
  String? get translatedName {
    switch (this) {
      case OrderStatusType.created:
        return 'draft_status'.tr();
      case OrderStatusType.submitted:
        return 'new_status'.tr();
      case OrderStatusType.underProcess:
        return 'under_process_status'.tr();
      case OrderStatusType.quotationSubmitted:
        return 'quotation_submitted_status'.tr();
      case OrderStatusType.quotationApproved:
        return 'quotation_approved_status'.tr();
      case OrderStatusType.quotationDeclient:
        return 'quotation_declined_status'.tr();
      case OrderStatusType.paymentWaiting:
        return 'waiting_for_payment_status'.tr();
      case OrderStatusType.deliverProcess:
        return 'delivery_process_status'.tr();
      case OrderStatusType.completed:
        return 'completed_status'.tr();
      case OrderStatusType.ipoSubmitted:
        return 'ipo_submitted_status'.tr();
      case OrderStatusType.waiting:
        return 'waiting_status'.tr();
      case OrderStatusType.rejected:
        return 'rejected_status'.tr();
      case OrderStatusType.preparingShipment:
        return 'preparing_shipment_status'.tr();
      case OrderStatusType.ipoMissingDocs:
        return 'ipo_missing_docs_status'.tr();
      case OrderStatusType.ipoInComplete:
        return 'ipo_incomplete_status'.tr();
      case OrderStatusType.invocing:
        return 'invoicing_status'.tr();
      default:
        return null;
    }
  }

  Color? get statusColor {
    switch (this) {
      case OrderStatusType.created:
        return Colors.cyan[600];
      case OrderStatusType.submitted:
        return Colors.teal[600];
      case OrderStatusType.underProcess:
        return Colors.blueGrey[600];
      case OrderStatusType.quotationSubmitted:
        return Colors.indigo[600];
      case OrderStatusType.quotationApproved:
        return Colors.deepPurple[600];
      case OrderStatusType.quotationDeclient:
        return Colors.pink[600];
      case OrderStatusType.paymentWaiting:
        return Colors.purple[600];
      case OrderStatusType.deliverProcess:
        return Colors.deepOrange[600];
      case OrderStatusType.completed:
        return Colors.brown[600];
      case OrderStatusType.ipoSubmitted:
        return Colors.lime[600];
      case OrderStatusType.waiting:
        return Colors.lightGreen[600];
      case OrderStatusType.rejected:
        return Colors.red[600];
      case OrderStatusType.preparingShipment:
        return Colors.amber[600];
      case OrderStatusType.ipoMissingDocs:
        return Colors.orange[600];
      case OrderStatusType.ipoInComplete:
        return Colors.yellow[600];
      case OrderStatusType.invocing:
        return Colors.green[600];
      default:
        return Colors.grey[600];
    }
  }
}

extension QuotationStatusTypeExtension on QuotationsStatusType {
  String get translatedName {
    switch (this) {
      case QuotationsStatusType.created:
        return 'draft_status'.tr();
      case QuotationsStatusType.accepted:
        return 'accepted_status'.tr();
      case QuotationsStatusType.rejected:
        return 'rejected_status'.tr();
      case QuotationsStatusType.expired:
        return 'expired_status'.tr();
      case QuotationsStatusType.submitted:
        return 'waiting_for_approval_status'.tr();
      case QuotationsStatusType.canceled:
        return 'canceled_status'.tr();
        throw UnimplementedError();
    }
  }

  Color get statusColor {
    switch (this) {
      case QuotationsStatusType.created:
        return Colors.blue;
      case QuotationsStatusType.accepted:
        return AppColors.primary;
      case QuotationsStatusType.rejected:
        return Colors.red;
      case QuotationsStatusType.expired:
        return Colors.grey;
      case QuotationsStatusType.submitted:
        return Colors.amber;
      case QuotationsStatusType.canceled:
        return Colors.red;
    }
  }
}

extension InvoicesStatusTypeExtension on InvoicesStatusType {
  String get translatedName {
    switch (this) {
      case InvoicesStatusType.unpaid:
        return 'unpaid_status'.tr();
      case InvoicesStatusType.overDuo:
        return 'over_duo_status'.tr();
      case InvoicesStatusType.paid:
        return 'paid_status'.tr();
      case InvoicesStatusType.paidPartial:
        return 'paid_partial_status'.tr();
      case InvoicesStatusType.canceled:
        return 'canceled_status'.tr();
      case InvoicesStatusType.creditNote:
        return 'credit_note_status'.tr();
      case InvoicesStatusType.draft:
        return 'draft_status'.tr();
    }
  }

  Color get statusColor {
    switch (this) {
      case InvoicesStatusType.unpaid:
        return Colors.grey;
      case InvoicesStatusType.overDuo:
        return Colors.amber;
      case InvoicesStatusType.paid:
        return AppColors.primary;
      case InvoicesStatusType.paidPartial:
        return Colors.cyan;
      case InvoicesStatusType.canceled:
        return Colors.red;
      case InvoicesStatusType.creditNote:
        return Colors.orange;
      case InvoicesStatusType.draft:
        return Colors.blue;
    }
  }
}

extension PaymentsStatusTypeExtension on PaymentsStatusType {
  String get translatedName {
    switch (this) {
      case PaymentsStatusType.waiting:
        return 'waiting_status'.tr();
      case PaymentsStatusType.pending:
        return 'pending_status'.tr();
      case PaymentsStatusType.approved:
        return 'approved_status'.tr();
      case PaymentsStatusType.partiallyApproved:
        return 'partially_approved'.tr();
      case PaymentsStatusType.rejected:
        return 'rejected_status'.tr();
    }
  }

  Color get statusColor {
    switch (this) {
      case PaymentsStatusType.waiting:
        return Colors.amber;
      case PaymentsStatusType.pending:
        return Colors.blue;
      case PaymentsStatusType.approved:
        return AppColors.primary;
      case PaymentsStatusType.partiallyApproved:
        return Colors.cyan;
      case PaymentsStatusType.rejected:
        return Colors.red;
    }
  }
}

extension CustomerTypeExtension on CustomerType {
  String get translatedName {
    switch (this) {
      case CustomerType.non:
        return 'non'.tr();
      case CustomerType.demander:
        return 'demander'.tr();
      case CustomerType.authorizer:
        return 'authorizer'.tr();
      case CustomerType.fullAuthority:
        return 'full_authority'.tr();
      case CustomerType.receiver:
        return 'receiver'.tr();
    }
  }
}

extension ApprovalStatusTypeExtension on ApprovalStatusType {
  String get translatedName {
    switch (this) {
      case ApprovalStatusType.waiting:
        return 'waiting_status'.tr();
      case ApprovalStatusType.approved:
        return 'approved_status'.tr();
      case ApprovalStatusType.rejected:
        return 'rejected_status'.tr();
    }
  }

  Color get statusColor {
    switch (this) {
      case ApprovalStatusType.waiting:
        return Colors.amber;
      case ApprovalStatusType.approved:
        return AppColors.primary;
      case ApprovalStatusType.rejected:
        return Colors.red;
    }
  }
}

extension OrderTypesExtension on OrderTypes {
  String get translatedName {
    switch (this) {
      case OrderTypes.directPO:
        return 'directPO'.tr();
      case OrderTypes.tender:
        return 'tender'.tr();
      case OrderTypes.lvp:
        return 'lvp'.tr();
      case OrderTypes.payBill:
        return 'payBill'.tr();
      case OrderTypes.etimadTender:
        return 'etimadTender'.tr();
    }
  }

  String get orderTypeId {
    switch (this) {
      case OrderTypes.directPO:
        return '1';
      case OrderTypes.tender:
        return '2';
      case OrderTypes.lvp:
        return '3';
      case OrderTypes.payBill:
        return '4';
      case OrderTypes.etimadTender:
        return '5';
    }
  }
}
