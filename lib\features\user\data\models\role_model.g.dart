// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'role_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RoleModel _$RoleModelFromJson(Map<String, dynamic> json) => RoleModel(
      roleId: json['roleId'] as String?,
      roleName: json['roleName'] as String?,
      displayName: json['displayName'] as String?,
      employeeId: json['employeeId'] as String?,
      employeeName: json['employeeName'] as String?,
      employeeRoleId: json['employeeRoleId'] as String?,
      group: json['group'] as String?,
    );

Map<String, dynamic> _$RoleModelToJson(RoleModel instance) => <String, dynamic>{
      'employeeRoleId': instance.employeeRoleId,
      'roleId': instance.roleId,
      'employeeId': instance.employeeId,
      'roleName': instance.roleName,
      'group': instance.group,
      'displayName': instance.displayName,
      'employeeName': instance.employeeName,
    };
