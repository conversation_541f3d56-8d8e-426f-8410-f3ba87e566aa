import 'dart:ui';

class AppColors {
  static const Color primary = Color(0xFF008D92);
  // static const Color darkGreen = Color(0xFF628264);
  // static const Color green = Color(0xFF68A081);
  // static const Color greenShadow = Color(0xFF56FFAA);
  // static const Color lightGreen = Color(0xFF6FC5A4);
  // static const Color green2 = Color(0xFF1B985A);
  // static const Color lightGreen2 = Color(0xFF6BFDAD);
  static const Color activeIndicatorColor = Color(0xFF0C8991);
  static const Color unActiveIndicatorColor = Color(0xFFE7E7E7);
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color blue = Color(0xFF2B3F63);
  static const Color lightBlue = Color(0xFFbad5f8);
  static const Color deepBlue = Color(0xFF10458b);
  static const Color red = Color(0xFFEC747D);
  static const Color lightGrey = Color(0xFFE9E9E9);
  static const Color cardColor = Color(0xFFFfffff);
  // static const Color cardGrey = Color(0xFFF3F3F3);
  static const Color grey = Color(0xFFB7B7B7);
  static const Color darkGrey = Color(0xFF4D4D4D);
  static const Color textGrey = Color(0xFFAAB4AF);
  static const Color textGrey2 = Color(0xFFA9A9A9);
  static const Color brownCard = Color(0xFFA77A4B);
  // static const Color greenCard = Color(0xFF5EA14E);
  static const Color purpleCard = Color(0xFFB373F3);
  static const Color blueCard = Color(0xFF4063E9);
  static const Color yellowCard = Color(0xFFD49C04);
  static const Color blueYonderCard = Color(0xFF5172A6);
  static const Color blueCuriousCard = Color(0xFF1DA1D1);
  static const Color newStatusBackground = Color(0xFF32BAFC);
  static const Color newStatusText = Color(0xFF2197DB);
  static const Color waitingForApprovalStatusBackground = Color(0xFFFCD232);
  static const Color waitingForApprovalStatusText = Color(0xFFDB8A21);
  // static const Color sentStatusBackground = Color(0xFF19CB1F);
  // static const Color sentStatusText = Color(0xFF19CB1F);
  static const Color completedStatusBackground = Color(0xFF6528CE);
  static const Color completedStatusText = Color(0xFF833EEB);
  static const Color purple = Color(0xFF746EB1);
  static const Color subtitleGrey = Color(0xFF7D7D7D);
  static const Color listTileTitleTextGrey = Color(0xFFA7A7A7);
  static const Color listTileTrailingTextGrey = Color(0xFF282A27);
  static const Color tabBackgroundColor = Color(0xFF464C4A);
  static const Color tabTextColor = Color(0xFFB1B1B1);
  static const Color hintTextColor = Color(0xFF919191);
  static const Color cardDetailsBackground = Color(0xFFF8F8F8);
  static const Color scaffoldBackgroundColor = Color(0xFFFAFAFA);
  static const Color grey2 = Color(0xFF929292);
  static const Color borderColor1 = Color(0xFFDBDBDB);
  // static const Color iconColorGreen = Color(0xFF6EB799);
  static const Color disabledColor1 = Color(0xFF707070);
  static const Color disabledTitleColor1 = Color(0xFFAFAFAF);
  // static const Color cardViewTitleColor = Color(0xFF21DBB0);
  // static const Color cardViewBackgroundColor = Color(0xFF39FF82);
  static const Color lightGrey3 = Color(0xFFEBEBEB);
  static const Color textFiledBorderColor = Color(0xFFD1D1D1);
  static const Color blueChillColor = Color(0xFF0C8991);
  static const Color poloBlueColor = Color(0xFF7F96CC);
  static const Color astralColor = Color(0xFF387BA0);
  static const Color bostonBlueColor = Color(0xFF3C81C3);
}
