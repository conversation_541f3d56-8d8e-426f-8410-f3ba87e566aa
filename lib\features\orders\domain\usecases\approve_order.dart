import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/orders/data/repositories/orders_repository_impl.dart';
import 'package:mdd/features/orders/domain/repositories/orders_repository.dart';

final approveOrderUseCaseProvider = Provider<ApproveOrder>((ref) {
  return ApproveOrder(ref.watch(orderRepositoryImpl));
});

class ApproveOrder implements UseCase<ResultModel, ApproveOrderParams> {
  final OrdersRepository ordersRepository;

  ApproveOrder(this.ordersRepository);

  @override
  Future<Either<Failure, ResultModel>> call(ApproveOrderParams params) async {
    return ordersRepository.approveOrder(params);
  }
}

class ApproveOrderParams {
  final String orderID;
  final int status;
  final String? notes;

  ApproveOrderParams({
    required this.orderID,
    required this.status,
    this.notes,
  });
}
