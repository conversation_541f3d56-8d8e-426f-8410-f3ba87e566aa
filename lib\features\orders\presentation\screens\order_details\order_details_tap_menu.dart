import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/widgets/button_widget.dart';
import 'package:mdd/core/widgets/tap_menu_widget.dart';
import 'package:mdd/features/orders/domain/entities/orders_entity.dart';
import 'package:mdd/features/orders/presentation/provider/order_tap_provider.dart';
import 'package:mdd/features/orders/presentation/widgets/approve_order_widget.dart';
import 'package:mdd/features/user/domain/entities/user_entity.dart';
import 'package:mdd/features/user/presentation/provider/user_provider.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/extensions.dart';

class OrderDetailsTapMenu extends ConsumerStatefulWidget {
  const OrderDetailsTapMenu(
    this.order, {
    super.key,
  });

  final OrdersEntity order;
  @override
  ConsumerState createState() => _OrderDetailsTapMenuState();
}

class _OrderDetailsTapMenuState extends ConsumerState<OrderDetailsTapMenu> {
  bool get isCurrentApprover =>
      widget.order.orderStatus == OrderStatusType.waiting &&
      widget.order.currentApprover ==
          (ref.read(userProvider) as LoadedViewState<UserEntity>).data.userID;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (isCurrentApprover)
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.kSizeLarge,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ButtonWidget(
                  onPressed: () => showDialog(
                    context: context,
                    builder: (_) => Dialog(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          AppDimensions.kMediumRadius,
                        ),
                      ),
                      insetPadding: const EdgeInsets.symmetric(
                        horizontal: AppDimensions.kSizeXXLarge,
                      ),
                      child: ApproveOrderWidget(
                        orderID: widget.order.orderID!,
                        status: 1,
                      ),
                    ),
                  ),
                  title: 'approve'.tr(),
                  horizontalPadding: AppDimensions.kSizeXXLarge,
                ),
                const SizedBox(width: 10),
                ButtonWidget(
                  onPressed: () => showDialog(
                    context: context,
                    builder: (_) => Dialog(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          AppDimensions.kMediumRadius,
                        ),
                      ),
                      insetPadding: const EdgeInsets.symmetric(
                        horizontal: AppDimensions.kSizeXXLarge,
                      ),
                      child: ApproveOrderWidget(
                        orderID: widget.order.orderID!,
                        status: 2,
                      ),
                    ),
                  ),
                  title: 'reject'.tr(),
                  backgroundColor: AppColors.red,
                  horizontalPadding: AppDimensions.kSizeXXLarge,
                ),
              ],
            ),
          ),
        SizedBox(
          height: context.heightR(.05),
          child: Consumer(builder: (context, ref, child) {
            final selectedTapProvider = ref.watch(tapMenuSelected.state);
            return ListView(
              padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.kSizeMedium),
              scrollDirection: Axis.horizontal,
              children: [
                TapMenuWidget(
                  isActive: selectedTapProvider.state == 0,
                  tabTitle: 'details'.tr(),
                  onTap: () {
                    if (selectedTapProvider.state != 0) {
                      selectedTapProvider.state = 0;
                    }
                  },
                ),
                TapMenuWidget(
                  isActive: selectedTapProvider.state == 1,
                  tabTitle: 'offers'.tr(),
                  onTap: () {
                    if (selectedTapProvider.state != 1) {
                      selectedTapProvider.state = 1;
                    }
                  },
                ),
                TapMenuWidget(
                  isActive: selectedTapProvider.state == 2,
                  tabTitle: 'attachments'.tr(),
                  onTap: () {
                    if (selectedTapProvider.state != 2) {
                      selectedTapProvider.state = 2;
                    }
                  },
                ),
                TapMenuWidget(
                  isActive: selectedTapProvider.state == 3,
                  tabTitle: 'notes'.tr(),
                  onTap: () {
                    if (selectedTapProvider.state != 3) {
                      selectedTapProvider.state = 3;
                    }
                  },
                ),
                TapMenuWidget(
                  isActive: selectedTapProvider.state == 4,
                  tabTitle: 'comments'.tr(),
                  onTap: () {
                    if (selectedTapProvider.state != 4) {
                      selectedTapProvider.state = 4;
                    }
                  },
                ),
                if (isCurrentApprover ||
                    widget.order.orderStatus == OrderStatusType.rejected ||
                    widget.order.orderStatus == OrderStatusType.submitted)
                  TapMenuWidget(
                    isActive: selectedTapProvider.state == 5,
                    tabTitle: 'approvals'.tr(),
                    onTap: () {
                      if (selectedTapProvider.state != 5) {
                        selectedTapProvider.state = 5;
                      }
                    },
                  ),
              ],
            );
          }),
        ),
      ],
    );
  }
}
