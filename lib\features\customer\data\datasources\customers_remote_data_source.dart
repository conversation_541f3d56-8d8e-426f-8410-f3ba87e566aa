import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/endPoints/end_points.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/features/customer/data/models/customers_model.dart';
import 'package:mdd/features/customer/domain/usecases/create_new_customer.dart';
import 'package:mdd/features/customer/domain/usecases/customers.dart';
import 'package:mdd/features/customer/domain/usecases/update_customer.dart';
import 'package:mdd/services/dio_client.dart';
import 'package:mdd/utils/constants/constants.dart';

abstract class CustomersRemoteDataSource {
  Future<List<CustomersModel>> getCustomers(CustomersParams params);
  Future<ResultModel> createNewCustomer(CreateCustomerParams params);
  Future<ResultModel> updateCustomer(UpdateCustomerParams params);
}

final customersRemoteDataSourceImpl = Provider<CustomersRemoteDataSourceImpl>(
  (ref) {
    return CustomersRemoteDataSourceImpl(ref.watch(dioClientProvider));
  },
);

class CustomersRemoteDataSourceImpl implements CustomersRemoteDataSource {
  final DioClient _dioClient;

  CustomersRemoteDataSourceImpl(this._dioClient);
  @override
  Future<List<CustomersModel>> getCustomers(CustomersParams params) async {
    final response = await _dioClient.dio.get(
      EndPoints.customers,
      queryParameters: {
        "limit": params.limit ?? AppConstants.paginationLimit,
        "find": params.find,
        "page": params.page,
      },
    );
    return (response.data['data'] as List)
        .map((customer) => CustomersModel.fromJson(customer))
        .toList();
  }

  @override
  Future<ResultModel> createNewCustomer(CreateCustomerParams params) async {
    final response = await _dioClient.dio.post(
      EndPoints.customers,
      data: params.toJson(),
    );
    return ResultModel.fromJson(response.data);
  }

  @override
  Future<ResultModel> updateCustomer(UpdateCustomerParams params) async {
    final response = await _dioClient.dio.put(
      EndPoints.customers,
      data: params.customer,
    );
    return ResultModel.fromJson(response.data);
  }
}
