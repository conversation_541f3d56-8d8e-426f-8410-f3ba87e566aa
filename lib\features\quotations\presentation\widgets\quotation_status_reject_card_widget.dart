import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/features/quotations/data/models/quotations_model.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/enums.dart';
import 'package:mdd/utils/helper_functions.dart';

class QuotationStatusRejectCardWidget extends StatelessWidget {
  const QuotationStatusRejectCardWidget(this.quotation, {super.key});

  final QuotationsModel quotation;
  @override
  Widget build(BuildContext context) {
    if (quotation.quotationStatus == QuotationsStatusType.rejected) {
      return SizedBox(
        width: double.infinity,
        child: Card(
          margin: const EdgeInsets.symmetric(
            horizontal: AppDimensions.kSizeMedium,
          ),
          elevation: 0,
          color: Colors.redAccent,
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.kSizeMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  'quotation_has_been_refused'.tr(),
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                const SizedBox(height: AppDimensions.kSizeXSmall2),
                Wrap(
                  children: [
                    TextWidget(
                      '${'cancel_reason'.tr()}: ',
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                    TextWidget(
                      quotation.rejectReasonName ?? '',
                      color: Colors.white,
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.kSizeXSmall2),
                Wrap(
                  children: [
                    TextWidget(
                      '${'reject_date'.tr()}: ',
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                    TextWidget(
                      HelperFunctions.formatDate(quotation.rejectDate),
                      color: Colors.white,
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.kSizeXSmall2),
                Wrap(
                  children: [
                    TextWidget(
                      '${'notes'.tr()}: ',
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                    TextWidget(
                      quotation.rejectNotes ?? '',
                      color: Colors.white,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      );
    }
    return const SizedBox();
  }
}
