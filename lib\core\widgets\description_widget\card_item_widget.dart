import 'package:app_popup_menu/app_popup_menu.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mdd/core/widgets/description_widget/description_card_item_widget.dart';
import 'package:mdd/core/widgets/subtitle_widget.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/extensions.dart';

class CardItemsWidget<T> extends StatelessWidget {
  final String image;
  final Color imageBackground;
  final String title;
  final String subtitle;
  final String? statusName;
  final Color? statusColor;
  final List<DescriptionCardItemWidget> descriptionWidget;
  final void Function(T)? onSelected;
  final List<PopupMenuItem<T>>? menuItems;
  const CardItemsWidget({
    super.key,
    required this.image,
    required this.imageBackground,
    required this.title,
    required this.subtitle,
    this.statusName,
    this.statusColor,
    required this.descriptionWidget,
    this.menuItems,
    this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.kSizeMedium,
        vertical: AppDimensions.kSizeXSmall,
      ),
      child: Card(
        elevation: 0,
        shadowColor: Colors.black38,
        color: AppColors.cardColor,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(
              height: AppDimensions.kSizeLarge,
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(width: AppDimensions.kSizeMedium),
                Expanded(
                  child: Row(
                    children: [
                      Card(
                        color: imageBackground,
                        margin: EdgeInsets.zero,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(
                            AppDimensions.kSizeXSmall,
                          ),
                          child: Image.asset(
                            image,
                            height: AppDimensions.kSize3XLarge,
                          ),
                        ),
                      ),
                      const SizedBox(width: AppDimensions.kSizeSmall),
                      Flexible(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(title),
                            SubtitleWidget(
                              subtitle,
                              color: AppColors.subtitleGrey,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                if (statusName != null)
                  Card(
                    color: statusColor?.withOpacity(0.15) ??
                        AppColors.newStatusBackground.withOpacity(0.2),
                    margin: EdgeInsets.zero,
                    elevation: 0,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: AppDimensions.kSizeSmall / 2,
                        horizontal: AppDimensions.kSizeSmall,
                      ),
                      child: SubtitleWidget(
                        statusName!,
                        color: statusColor ?? AppColors.newStatusText,
                      ),
                    ),
                  ),
                const SizedBox(
                  width: AppDimensions.kSizeMedium,
                ),
                if (menuItems != null)
                  AppPopupMenu<T>(
                    menuItems: menuItems,
                    onSelected: onSelected,
                    padding: EdgeInsets.zero,
                    offset: Offset(0, context.heightR(0.05)),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppDimensions.kMediumRadius,
                      ),
                    ),
                    color: AppColors.white,
                    child: Card(
                      color: AppColors.white,
                      margin: EdgeInsets.zero,
                      elevation: 0,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: AppDimensions.kSizeSmall / 1.3,
                          horizontal: AppDimensions.kSizeSmall,
                        ),
                        child: SvgPicture.asset('assets/icons/more_icon.svg'),
                      ),
                    ),
                  ),
                // InkWell(
                //   onTap: onSelected,
                //   child: SizedBox(
                //     height: context.heightR(0.03),
                //     child: Card(
                //       color: AppColors.white,
                //       margin: EdgeInsets.zero,
                //       elevation: 0,
                //       child: Padding(
                //         padding: const EdgeInsets.symmetric(
                //             vertical: AppDimensions.kSizeSmall / 1.3,
                //             horizontal: AppDimensions.kSizeSmall),
                //         child: SvgPicture.asset('assets/icons/more_icon.svg'),
                //       ),
                //     ),
                //   ),
                // ),
                const SizedBox(
                  width: AppDimensions.kSizeMedium,
                ),
              ],
            ),
            const Divider(
              indent: AppDimensions.kSizeMedium,
              endIndent: AppDimensions.kSizeMedium,
            ),
            ...descriptionWidget
          ],
        ),
      ),
    );
  }
}
