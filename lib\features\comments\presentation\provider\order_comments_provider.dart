import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/models/view_states.dart';
import 'package:mdd/core/provider/base_provider.dart';
import 'package:mdd/features/comments/data/models/comments_model.dart';
import 'package:mdd/features/comments/domain/usecases/order_comments.dart';

final orderCommentsProvider =
    StateNotifierProvider.autoDispose<OrderCommentsProvider, ViewState>(
  (ref) => OrderCommentsProvider(
    ref.watch(orderCommentsUseCaseProvider),
  ),
);

class OrderCommentsProvider extends BaseProvider<List<CommentsModel>> {
  final OrderComments _orderComments;

  OrderCommentsProvider(this._orderComments);

  Future<void> orderComments(String orderId) async {
    setLoadingState();
    final res = await _orderComments.call(OrderCommentsParams(orderId));
    res.fold(
      (failure) {
        setErrorState(failure.message);
      },
      setLoadedState,
    );
  }
}
