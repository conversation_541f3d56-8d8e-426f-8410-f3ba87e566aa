import 'package:flutter/material.dart';
import 'package:mdd/theme/colors.dart';

import 'dimensions.dart';

ThemeData getThemeData() {
  // ThemeData
  return ThemeData(
    // <------------------------------------------------ Main Theme Font
    fontFamily: 'IBMPlexSansArabic',
    useMaterial3: false,
    primaryColor: AppColors.primary,
    secondaryHeaderColor: AppColors.white,
    cardColor: AppColors.cardColor,
    scaffoldBackgroundColor: AppColors.scaffoldBackgroundColor,
    dividerColor: AppColors.black.withOpacity(0.10),
    disabledColor: Colors.grey,
    splashColor: Colors.transparent,
    highlightColor: Colors.transparent,
    textSelectionTheme: const TextSelectionThemeData(
      cursorColor: AppColors.hintTextColor,
    ),

    // <------------------------------------------------ Primary Icon Theme
    primaryIconTheme: const IconThemeData(color: Colors.black54),
 floatingActionButtonTheme:  const FloatingActionButtonThemeData(
   elevation: 0,
 ),
    // <------------------------------------------------ Icon Theme
    iconTheme: IconThemeData(color: Colors.red.withOpacity(0.45)),

    // <------------------------------------------------ Tab Bar Theme
    tabBarTheme: TabBarThemeData(

      labelColor: Colors.blue,
      //labelStyle: ,
      unselectedLabelColor: Colors.blue,
      indicator: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
      ),
    ),
    cardTheme: CardThemeData(
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.kMediumRadius))),
    // <------------------------------------------------ FAB Theme

    // <------------------------------------------------ App Bar Theme Theme
    appBarTheme: AppBarTheme(
        elevation: 1,
        color: AppColors.white,
        shadowColor: AppColors.black.withOpacity(0.5)),

    // <------------------------------------------------ Input Decoration Theme
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      contentPadding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.kSizeLarge,
          vertical: AppDimensions.kSizeLarge),
      fillColor: Colors.white,
      enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.kSizeSmall),
          borderSide: BorderSide(color: AppColors.white.withOpacity(0.7))),
      focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.kSizeSmall),
          borderSide: const BorderSide(color: AppColors.white)),
      border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.kSizeSmall),
          borderSide: const BorderSide(color: AppColors.white)),
      labelStyle: const TextStyle(
        fontSize: AppDimensions.kSizeMedium,
        color: AppColors.white,
      ),
      hintStyle: const TextStyle(
        fontSize: AppDimensions.kSizeLarge,
        color: AppColors.white,
      ),
    ),

    // <------------------------------------------------ Text Theme
    textTheme: const TextTheme(
      bodyLarge: TextStyle(color: AppColors.darkGrey),
      bodyMedium: TextStyle(color: AppColors.darkGrey),
      labelLarge: TextStyle(color: AppColors.darkGrey),
      bodySmall: TextStyle(color: AppColors.darkGrey),
      titleMedium: TextStyle(color: AppColors.darkGrey), // ==> text field style
      displayLarge: TextStyle(color: AppColors.darkGrey),
      displayMedium: TextStyle(color: AppColors.darkGrey),
      displaySmall: TextStyle(color: AppColors.darkGrey),
      headlineMedium: TextStyle(color: AppColors.darkGrey),
      headlineSmall: TextStyle(color: AppColors.darkGrey),
      titleLarge: TextStyle(color: AppColors.darkGrey),
    ),
    bottomAppBarTheme: const BottomAppBarThemeData(color: AppColors.white),
    colorScheme: ColorScheme.fromSwatch(
            primarySwatch: createMaterialColor(AppColors.primary))
        .copyWith(surface: AppColors.white),
  );
}

MaterialColor createMaterialColor(Color color) {
  List strengths = <double>[.05];
  final swatch = <int, Color>{};
  final int r = color.red, g = color.green, b = color.blue;

  for (int i = 1; i < 10; i++) {
    strengths.add(0.1 * i);
  }
  for (var strength in strengths) {
    final double ds = 0.5 - strength;
    swatch[(strength * 1000).round()] = Color.fromRGBO(
      r + ((ds < 0 ? r : (255 - r)) * ds).round(),
      g + ((ds < 0 ? g : (255 - g)) * ds).round(),
      b + ((ds < 0 ? b : (255 - b)) * ds).round(),
      1,
    );
  }
  return MaterialColor(color.value, swatch);
}
