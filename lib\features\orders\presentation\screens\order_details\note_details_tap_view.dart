import 'package:flutter/material.dart';
import 'package:mdd/core/widgets/text_widget.dart';
import 'package:mdd/theme/colors.dart';
import 'package:mdd/theme/dimensions.dart';

class NoteDetailsTapView extends StatelessWidget {
  final String note;
  const NoteDetailsTapView({super.key, required this.note});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Card(
        margin:
            const EdgeInsets.symmetric(horizontal: AppDimensions.kSizeMedium),
        elevation: 0,
        color: AppColors.cardDetailsBackground,
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.kSizeMedium),
          child: TextWidget(note),
        ),
      ),
    );
  }
}
