import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mdd/core/errors/failure.dart';
import 'package:mdd/core/models/result_model.dart';
import 'package:mdd/core/usecases/usecase.dart';
import 'package:mdd/features/organization/data/models/organizations_model.dart';
import 'package:mdd/features/organization/data/repositories/organizations_repository_impl.dart';
import 'package:mdd/features/organization/domain/repositories/organizations_repository.dart';

final updateOrgUseCaseProvider = Provider<UpdateOrg>((ref) {
  return UpdateOrg(ref.watch(orgsRepositoryImpl));
});

class UpdateOrg implements UseCase<ResultModel, UpdateOrgParams> {
  final OrgsRepository _orgsRepository;

  UpdateOrg(this._orgsRepository);

  @override
  Future<Either<Failure, ResultModel>> call(UpdateOrgParams params) async {
    return _orgsRepository.update(params);
  }
}

class UpdateOrgParams extends Equatable {
  final OrgsModel org;

  const UpdateOrgParams(this.org);

  @override
  List<Object?> get props => [org];
}
