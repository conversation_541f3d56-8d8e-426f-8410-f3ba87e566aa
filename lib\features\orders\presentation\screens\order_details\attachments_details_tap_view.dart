import 'dart:convert';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mdd/features/attachments/domain/entites/attachments_entity.dart';
import 'package:mdd/features/attachments/presentation/widgets/attachment_widget.dart';
import 'package:mdd/theme/dimensions.dart';
import 'package:mdd/utils/files_handler.dart';
import 'package:mdd/utils/sentry_reporter.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';

class AttachmentsDetailsTapView extends StatelessWidget {
  final List<AttachmentsEntity> attachments;
  const AttachmentsDetailsTapView({super.key, required this.attachments});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: ListView.builder(
        itemCount: attachments.length,
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.kSizeMedium,
        ),
        itemBuilder: (context, index) {
          return AttachmentWidget(
            onPressed: () async {
              await FilesHandler(attachments[index]).openFile();
            },
            title: attachments[index].fileName ?? '',
            buttonTitle: "download".tr(),
          );
        },
      ),
    );
  }
}

class FileProcess {
  static bool isFolderCreated = false;
  static Directory? directory;

  static checkDocumentFolder() async {
    try {
      if (!isFolderCreated) {
        directory = await getApplicationDocumentsDirectory();
        await directory!.exists().then((value) {
          if (value) directory!.create();
          isFolderCreated = true;
        });
      }
    } catch (e, stackTrace) {
      SentryReporter.genericThrow(e, stackTrace: stackTrace);
      if (kDebugMode) print(e.toString());
    }
  }

  static Future<File> downloadFile(String base64str, String fileName) async {
    Uint8List bytes = base64.decode(base64str);
    await checkDocumentFolder();
    String dir = "${directory!.path}/$fileName";
    File file = File(dir);
    if (!file.existsSync()) file.create();
    await file.writeAsBytes(bytes);
    return file;
  }

  static void openFile(String fileName) {
    String dir = "${directory!.path}/$fileName.pdf";
    OpenFilex.open(dir);
  }
}
