class OrderDetailsEntity {
  final String? orderDetailID;
  final int? sNo;
  final String? orderID;
  final int? orderNo;
  final String? categoryID;
  final String? categoryName;
  final String? description;
  final int? qty;
  final String? productID;
  final String? productName;
  final String? unitID;
  final String? unitName;
  final String? fileName;
  final String? createdOn;
  final String? image;

  const OrderDetailsEntity({
    this.orderDetailID,
    this.sNo,
    this.orderID,
    this.orderNo,
    this.categoryID,
    this.categoryName,
    this.description,
    this.qty,
    this.productID,
    this.productName,
    this.unitID,
    this.unitName,
    this.fileName,
    this.createdOn,
    this.image,
  });
}
